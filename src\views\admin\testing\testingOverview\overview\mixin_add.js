export default {
  data() {
    return {
      modalAdd: false,
      pluginListModal: [],
      addPlugin: null
    }
  },
  computed: {
    addDisabled: function() {
      for (let i = 0; i < this.pluginListModal.length; i++) {
        if (this.pluginListModal[i]['plugin'] === this.addPlugin) {
          if (this.pluginListModal[i]['pluginConfig']) {
            return this.pluginListModal[i]['pluginConfig']['type']['value'] === null
          } else {
            return this.addedKeys.includes(this.pluginListModal[i]['plugin'])
          }
        }
      }
    },
    addedKeys: function() {
      const out = []
      this.layout.forEach((item) => {
        out.push(item.pluginConfig.pluginApiKey || item.plugin)
      })
      return out
    }
  },
  methods: {
    'changeConfigItem': function(pluginConfig, itemConfig) {
      if (pluginConfig.times && itemConfig.key !== 'times') {
        pluginConfig.times.value = null
      }
    },
    'selectPlugin': function(item) {
      this.addPlugin = item['plugin']
    },
    'modalAddOpen': function() {
      const pluginModalData = this.filterPluginsByRole()
      let addPlugin = null
      for (let i = 0; i < pluginModalData.length; i++) {
        if (!pluginModalData[i]['pluginConfig']) {
          continue
        }
        const typeList = pluginModalData[i]['pluginConfig']['type']['list']
        for (const key in typeList) {
          if (!this.addedKeys.includes(key)) {
            pluginModalData[i]['pluginConfig']['type']['value'] = key
            break
          }
        }
        if (!addPlugin) {
          if (pluginModalData[i]['pluginConfig']['type']['value']) {
            addPlugin = pluginModalData[i]['plugin']
          }
        }
      }
      this.pluginListModal = pluginModalData
      this.addPlugin = addPlugin
      this.modalAdd = true
    },
    'addOk': function() {
      for (let i = 0; i < this.pluginListModal.length; i++) {
        if (this.pluginListModal[i]['plugin'] === this.addPlugin) {
          const item = JSON.parse(JSON.stringify(this.pluginListModal[i]))
          const pluginConfig = item['pluginConfig']

          // 获取当前角色的默认布局配置，用于匹配w和h值
          const defaultLayout = this.updateLayoutByRole()

          // 默认使用组件定义的h和w
          let itemH = item['h']
          let itemW = item['w']

          // 如果选择了特定类型，优先从默认布局中查找对应的w和h值
          if (pluginConfig && pluginConfig['type'] && pluginConfig['type']['value']) {
            const selectedType = pluginConfig['type']['value']
            const typeInfo = pluginConfig['type']['list'][selectedType]

            // 从默认布局中查找匹配的模块配置
            const matchingDefaultItem = defaultLayout.find(layoutItem =>
              layoutItem.pluginConfig && layoutItem.pluginConfig.pluginApiKey === selectedType
            )

            if (matchingDefaultItem) {
              // 使用默认布局中的w和h值
              itemH = matchingDefaultItem.h
              itemW = matchingDefaultItem.w
            } else if (typeof typeInfo === 'object') {
              // 如果默认布局中没有找到，则使用typeInfo中的h和w
              if (typeInfo.h !== undefined) {
                itemH = typeInfo.h
              }
              if (typeInfo.w !== undefined) {
                itemW = typeInfo.w
              }
            }
          }

          // 获取正确的插件名称
          let pluginName = ''
          let timeName = ''

          if (pluginConfig && pluginConfig['type'] && pluginConfig['type']['value']) {
            const selectedType = pluginConfig['type']['value']
            const typeValue = pluginConfig['type']['list'][selectedType]

            // 如果是对象形式，需要使用.name属性
            if (typeof typeValue === 'object' && typeValue.name) {
              pluginName = typeValue.name
            } else {
              // 字符串形式直接使用
              pluginName = typeValue + ''
            }
          } else {
            pluginName = item.pluginCnName
          }

          // 处理时间范围
          if (pluginConfig && pluginConfig['times'] && pluginConfig['times']['value']) {
            const selectedTime = pluginConfig['times']['value']
            timeName = pluginConfig['times']['list'][selectedTime] + ''
          } else {
            timeName = '不限'
          }

          const addItem = {
            'h': itemH,
            'w': itemW,
            'plugin': item['plugin'],
            'pluginConfig': {
              'pluginName': pluginName,
              'pluginApiKey': pluginConfig ? pluginConfig['type']['value'] : null,
              'timeName': timeName,
              'timeApiKey': pluginConfig && pluginConfig['times'] ? pluginConfig['times']['value'] : 'no_limit'
            }
          }

          this.addPluginHandel(addItem)
          console.log('addOk', addItem)
          break
        }
      }
      this.addCancel()
    },
    'addCancel': function() {
      this.modalAdd = false
      this.addPlugin = null
      this.pluginListModal = []
    }
  }
}
