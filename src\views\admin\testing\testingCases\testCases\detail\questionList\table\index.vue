<template>
  <div class="resource-table" style="padding: 0;">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索问题标题"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column v-for="item in columnsViewArr" :key="item" :min-width="colMinWidth" :width="columnsObj[item].colWidth" :label="columnsObj[item].title" :fixed="columnsObj[item].master ? 'left' : false" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="item === 'problemNo'">
            <a :href="getIssueUrl(scope.row)" target="_blank">
              {{ scope.row[item] || '-' }}
            </a>
          </span>
          <span v-else-if="item === 'title'">
            <a :href="getIssueUrl(scope.row)" target="_blank">
              {{ scope.row[item] || '-' }}
            </a>
          </span>
          <span v-else-if="item === 'impactLevel'">
            <span :class="module.impactObj[scope.row[item]] && module.impactObj[scope.row[item]].type">
              {{ (module.impactObj[scope.row[item]] && module.impactObj[scope.row[item]].label) || '-' }}
            </span>
          </span>
          <span v-else-if="item === 'status'">
            <el-badge
              :type="
                (module.statusObj[scope.row.status] &&
                  module.statusObj[scope.row.status].type) ||
                  'info'
              "
              is-dot
            />
            {{
              (module.statusObj[scope.row.status] &&
                module.statusObj[scope.row.status].label) ||
                "-"
            }}
          </span>
          <span v-else-if="item === 'type'">
            {{
              (module.issueTypeObj[scope.row.type] &&
                module.issueTypeObj[scope.row.type].label) ||
                "-"
            }}
          </span>
          <span v-else-if="item === 'projectName'">
            <a :href="getProjectUrl(scope.row)" target="_blank" @click.prevent="toProjectDetail(scope.row)">
              {{ scope.row.projectName || '-' }}
            </a>
          </span>
          <span v-else>
            {{ scope.row[item] || '-' }}
          </span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import { getIssueList } from '@/api/testing/testCase'
import mixinsPageTable from '@/packages/mixins/page_table'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tTableView from '@/packages/table-view/index.vue'
import module from '../config.js'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig
  },
  mixins: [mixinsPageTable],
  props: {
    id: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      module,
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'problemNo', label: 'ID' },
        { key: 'title', label: '问题标题', master: true },
        { key: 'statusList', label: '状态', type: 'select', valueList: module.searchStatusArr },
        { key: 'typeList', label: '问题类型', type: 'select', valueList: module.typeStrArr },
        { key: 'impactLevelList', label: '影响程度', type: 'select', valueList: module.levelStrArr },
        { key: 'projectName', label: '所属项目' },
        { key: 'versionNumber', label: '影响版本' },
        { key: 'createByName', label: '创建人' },
        { key: 'createAt', label: '创建时间', type: 'time_range' }
      ],
      // 所有可配置显示列
      columnsObj: {
        'problemNo': {
          title: 'ID',
          master: true,
          colWidth: 80
        },
        'title': {
          title: '问题标题'
        },
        'type': {
          title: '问题类型'
        },
        'impactLevel': {
          title: '影响程度'
        },
        'status': {
          title: '状态',
          colWidth: 80
        },
        'projectName': {
          title: '所属项目'
        },
        'versionNumber': {
          title: '影响版本'
        },
        'createByName': {
          title: '创建人'
        },
        'createAt': {
          title: '创建时间'
        }
      },
      // 当前显示列 - 按要求调整顺序
      columnsViewArr: [
        'problemNo',
        'title',
        'type',
        'impactLevel',
        'status',
        'projectName',
        'versionNumber',
        'createByName',
        'createAt'
      ]
    }
  },
  created() {
    // 组件创建时调用一次列表加载
  },
  methods: {
    toProjectDetail(row) {
      if (row.taskUserCount === null) {
        this.$message.warning('无权限访问该项目')
        return
      }
      window.open(`/testing/testing/detail/${row.projectId}/overview`, '_blank')
    },
    getIssueUrl(row) {
      return `/testing/unFixedQuestion/detail/${row.id}/overview`
    },
    getProjectUrl(row) {
      return `/testing/testing/detail/${row.projectId}/overview`
    },

    getList(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params.pageType = 1
      params.caseId = this.$route.params.id
      if (params.createAt) {
        params.createTimeStart = params.createAt.split(',')[0]
        params.createTimeEnd = params.createAt.split(',')[1]
        delete params.createAt
      }
      if (params.statusList) {
        // 判断是否勾选了"已关闭"，2和6为已关闭
        if (params.statusList.includes('6')) {
          params.statusList.push('2')
        }
      }
      getIssueList(params).then(res => {
        this.tableData = res.data.records
        this.total = res.data.total
      }).finally(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
<style scoped>
.success {
  color: #67C23A;
}

.warning {
  color: #E6A23C;
}

.primary {
  color: var(--color-600);
}

.danger {
  color: #F56C6C;
}
</style>
