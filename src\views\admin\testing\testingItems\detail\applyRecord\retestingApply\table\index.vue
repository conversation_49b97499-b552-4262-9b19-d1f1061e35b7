<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索复测任务"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      type="list"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        :show-overflow-tooltip="columnsObj[item].showOverflowTooltip"
      >
        <template slot-scope="scope">
          <span v-if="item == 'sort'">
            {{ scope.$index + 1 }}
          </span>
          <div v-else-if="item == 'taskList'">
            <div class="flex jc-between ai-center">
              <el-tooltip :content="scope.row.taskList[0].taskName" class="item" effect="dark" placement="top">
                <div class="ellipsis">{{ scope.row.taskList[0].taskName || "-" }}</div>
              </el-tooltip>
              <CountPopover :list="scope.row.taskList" :show-name="'taskName'" />
            </div>
          </div>
          <div v-else-if="item == 'auditStatus'">
            <el-badge :type="statusMapping[scope.row.auditStatus]" is-dot />
            {{ statusObj[scope.row[item]].label || '-' }}
          </div>
          <div v-else-if="item === 'option'" class="action-buttons">
            <el-button
              v-permission="'manage.testing.project.projectDetail.applyRecord.retestApply.retestApplyAudit'"
              v-if="scope.row.auditStatus == 0"
              :disabled="detailData.pendingStatus === 9"
              type="text"
              @click.stop="toExamine(scope.row, 'examine')">审核</el-button>
            <el-button
              type="text"
              @click.stop="handleDetail(scope.row, 'examine')">查看详情</el-button>
          </div>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import CountPopover from '@/components/testing/CountPopover'
import examine from '../action/modal-examine.vue'
import { queryRetestApplyAPI } from '@/api/testing/index'
import mixinsActionMenu from '@/views/admin/testing/testingItems/detail/applyRecord/delayApply/table/action_menu.js'
import { mapGetters } from 'vuex'
import statusConf from '../../config.js'
import { testingItemsDetailAPI } from '@/api/testing/index'
export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol,
    CountPopover,
    examine
  },
  mixins: [mixinsPageTable, mixinsActionMenu],
  props: {
    projectId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      moduleName: module.name,
      statusObj: statusConf.statusObj,
      statusMapping: statusConf.statusMapping,
      searchKeyList: [
        { key: 'taskName', label: '复测任务', placeholder: '请输入复测任务', master: true },
        { key: 'applyName', label: '申请人', placeholder: '请输入申请人' },
        { key: 'auditName', label: '审核人', placeholder: '请输入审核人' },
        { key: 'auditStatus', label: '审核状态', type: 'select', placeholder: '请选择审核状态', valueList: statusConf.statusArr },
        { key: 'applyTime', label: '申请时间', type: 'time_range', placeholder: '请选择申请时间' },
        { key: 'auditTime', label: '审核时间', type: 'time_range', placeholder: '请选择审核时间' }
      ],
      columnsObj: {
        'sort': {
          title: '序号', master: true, colWidth: 50
        },
        'taskList': {
          title: '复测任务', master: true
        },
        'auditStatus': {
          title: '审核状态',
          showOverflowTooltip: true
        },
        'applyName': {
          title: '申请人',
          showOverflowTooltip: true
        },
        'applyTime': {
          title: '申请时间',
          showOverflowTooltip: true
        },
        'auditUserName': {
          title: '审核人',
          showOverflowTooltip: true
        },
        'auditTime': {
          title: '审核时间',
          showOverflowTooltip: true
        },
        'option': {
          title: '操作'
        }
      },
      columnsViewArr: [
        'sort',
        'taskList',
        'auditStatus',
        'applyName',
        'applyTime',
        'auditUserName',
        'auditTime',
        'option'
      ],
      // 弹窗title映射
      titleMapping: {
        'examine': '审核'
      },
      retestingApplyAuth: null // 复测申请权限数据
    }
  },
  computed: {
    ...mapGetters(['manage'])
  },
  created() {
    this.retestingApplyAuth = this.manage.testing.project.projectDetail.applyRecord.retestApply
    this.getData()
  },
  mounted() {
    this.$bus.$on('refreshRetestingApplyList', this.getList)
  },
  beforeDestroy() {
    this.$bus.$off('refreshRetestingApplyList', this.getList)
  },
  methods: {
    // 根据id获取详情数据
    'getData': function() {
      this.loading = true
      this.id = this.$route.name === 'testingTask_detail' ? this.$route.params.projectId : this.$route.params.id
      testingItemsDetailAPI(this.id).then(res => {
        if (res.data && res.data.code === 0) {
          this.detailData = res.data.data
        } else {
          this.$message.error(res.data.msg || '获取项目详情失败')
        }
        this.loading = false
      })
    },
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }

      // 构建查询参数
      const params = this.getPostData('page', 'limit')
      params.pageType = 1 // 分页
      params.projectId = this.projectId || this.$route.params.id

      // 处理日期范围参数
      if (params.applyTime) {
        const dateRange = params.applyTime.split(',')
        if (dateRange.length === 2) {
          params.applyTimeBegin = dateRange[0]
          params.applyTimeEnd = dateRange[1]
        }
        delete params.applyTime
      }

      if (params.auditTime) {
        const dateRange = params.auditTime.split(',')
        if (dateRange.length === 2) {
          params.auditTimeBegin = dateRange[0]
          params.auditTimeEnd = dateRange[1]
        }
        delete params.auditTime
      }

      // 调用接口获取数据
      queryRetestApplyAPI(params).then((res) => {
        if (res.data && res.data.code === 0) {
          // 处理返回的数据
          this.tableData = res.data.data ? res.data.data.records : []
          this.tableTotal = res.data.data ? res.data.data.total : 0
        }
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
        this.tableData = []
        this.tableTotal = 0
      })
    },
    toExamine(data, name) {
      this.selectItem = data
      this.modalName = name
      this.modalShow = true
    },
    // 打开侧拉查看详情
    handleDetail(row, type) {
      const params = Object.assign({
        showDrawer: true,
        projectId: this.projectId || this.$route.params.id,
        type
      }, row)
      this.$emit('show-drawer-detail', params)
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.refresh()
        this.$emit('call', type)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.resource-table {
  height: 100%;
  padding: 15px 0 0 0;
}
::v-deep.action-buttons .el-button {
  padding: 0;
  margin: 0;
  border: none;
  color: var(--color-600);
  &.is-disabled {
    cursor: not-allowed;
    color: #909399;
    border: none;
  }
}
</style>
