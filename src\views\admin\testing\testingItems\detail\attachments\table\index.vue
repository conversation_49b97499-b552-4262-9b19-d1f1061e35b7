<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          style="padding-left: 5px;"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索附件名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="id"
      type="list"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :width="columnsObj[item].width"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item === 'operation'">
            <el-link
              :underline="false"
              type="primary"
              size="small"
              @click="handleView(scope.row)"
            >
              查看
            </el-link>
            <el-link
              v-permission="'manage.testing.project.projectDetail.projectAttachment.attachmentUpload'"
              :disabled="data.pendingStatus == 9"
              :underline="false"
              type="primary"
              size="small"
              @click="handleUpdate(scope.row)"
            >
              更新
            </el-link>
            <el-link
              v-permission="'manage.testing.project.projectDetail.projectAttachment.attachmentDownload'"
              :underline="false"
              type="primary"
              size="small"
              @click="handleDownload(scope.row)"
            >
              下载
            </el-link>
            <el-link
              v-permission="'manage.testing.project.projectDetail.projectAttachment.attachmentDelete'"
              :disabled="data.pendingStatus == 9"
              :underline="false"
              type="primary"
              size="small"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-link>
          </span>
          <div v-else-if="item === 'index'">{{ indexMethod(scope.$index) }}</div>
          <div v-else-if="item === 'fileSize'">{{ formatFileSize(scope.row[item]) }}</div>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import {
  attachmentPageAPI,
  removeAttachmentAPI,
  updateAttachmentAPI,
  downloadAttachmentAPI
} from '@/api/testing/index'
import { getToken } from '@/utils/auth'
import mixinsActionMenu from '../../applyRecord/delayApply/table/action_menu.js'
import mixinsPageTable from '@/packages/mixins/page_table'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import tTableView from '@/packages/table-view/index.vue'
import module from '../config.js'
import { commonArchiveTypes } from '@/utils/index.js'
export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable, mixinsActionMenu],
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      moduleName: module.name,
      searchKeyList: [
        { key: 'fileName', label: '附件名称', placeholder: '请输入', master: true },
        { key: 'taskName', label: '测试任务', placeholder: '请输入' },
        { key: 'createBy', label: '提交人', placeholder: '请输入' },
        { key: 'createAt', label: '提交时间', placeholder: '请选择', type: 'time_range' },
        { key: 'updateBy', label: '最后更新人', placeholder: '请输入' },
        { key: 'updateAt', label: '更新时间', placeholder: '请选择', type: 'time_range' }
      ],
      columnsObj: {
        'index': { title: '序号', width: 50, master: true },
        'fileName': {
          title: '附件名称'
        },
        'typeName': {
          title: '测试任务'
        },
        'fileSize': {
          title: '文件大小'
        },
        'createByName': {
          title: '提交人'
        },
        'createAt': {
          title: '提交时间'
        },
        'updateByName': {
          title: '最后更新人'
        },
        'updateAt': {
          title: '最后更新时间'
        },
        'operation': {
          title: '操作'
        }
      },
      columnsViewArr: [
        'index',
        'fileName',
        'typeName',
        'fileSize',
        'createByName',
        'createAt',
        'updateByName',
        'updateAt',
        'operation'
      ],
      // 弹窗title映射
      titleMapping: {
        'viewAttachment': '查看附件',
        'updateAttachment': '更新附件'
      },
      projectId: this.$route.params.id || '',
      isUpdating: false, // 添加更新状态标志
      commonArchiveTypes
    }
  },
  created() {
    // 监听父组件的processTasks变化
    this.$parent.$watch('processTasks', (tasks) => {
      if (tasks && tasks.length > 0) {
        console.log('表格组件接收到processTasks:', tasks)
        // 可以在这里根据任务更新一些UI元素
      }
    })
  },
  methods: {
    // 根据任务ID获取任务名称
    getTaskNameById(taskId) {
      if (this.$parent.processTasks && this.$parent.processTasks.length > 0) {
        const task = this.$parent.processTasks.find(t => t.id === taskId)
        return task ? task.processName : '-'
      }
      return '-'
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '0 B'

      if (size < 1024) {
        return size + ' B'
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB'
      } else {
        return (size / 1024 / 1024).toFixed(2) + ' MB'
      }
    },
    getList: function(showLoading = true, id) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params.taskId = this.$parent.activeName
      params.projectId = this.projectId
      if (params.createAt) {
        params.createAtStart = params.createAt.split(',')[0]
        params.createAtEnd = params.createAt.split(',')[1]
        delete params.createAt
      }
      if (params.updateAt) {
        params.updateAtStart = params.updateAt.split(',')[0]
        params.updateAtEnd = params.updateAt.split(',')[1]
        delete params.updateAt
      }
      attachmentPageAPI(params).then(res => {
        if (res.data.code === 0) {
          this.tableData = res.data.data.records || []
          this.tableTotal = Number(res.data.data.total) || 0
          this.tableLoading = false
        }
      })
    },
    handleDetail(row) {
      this.$emit('link-event', { name: 'attachmentDetail', row, params: { id: row.id }})
    },
    handleView(row) {
      const suffix = String(row.fileName).substring(String(row.fileName).lastIndexOf('.') + 1)
      if (row && row.fileName && this.commonArchiveTypes.includes(suffix)) {
        this.$message.warning('该文件不支持预览，请下载查看')
        return
      }
      if (row && row.fileUrl) {
        const fileUrl = window.ADMIN_CONFIG.VIP_URL + row.fileUrl
        if (suffix == 'xlsx') {
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(fileUrl)) + '&officePreviewType=html'
        } else {
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(fileUrl))
        }
        window.open(this.previewUrl)
      }
    },
    handleUpdate(row) {
      // 防止重复点击
      if (this.isUpdating) {
        return
      }
      this.isUpdating = true
      // 创建一个隐藏的文件上传input
      const fileInput = document.createElement('input')
      fileInput.type = 'file'
      fileInput.style.display = 'none'
      document.body.appendChild(fileInput)
      // 监听文件选择事件
      fileInput.addEventListener('change', (event) => {
        const file = event.target.files[0]
        if (!file) {
          document.body.removeChild(fileInput)
          this.isUpdating = false
          return
        }
        // 文件大小限制检查
        const isLt600M = file.size / 1024 / 1024 < 600
        if (!isLt600M) {
          this.$message.error('文件大小不能超过600MB')
          document.body.removeChild(fileInput)
          this.isUpdating = false
          return
        }
        // 创建FormData用于上传
        const formData = new FormData()
        formData.append('file', file)
        // 上传文件
        fetch('/api/testing/attachment/upload', {
          method: 'POST',
          headers: {
            'Admin-Token': getToken()
          },
          body: formData
        })
          .then(response => response.json())
          .then(res => {
            if (res.code === 0) {
              // 上传成功，调用更新API
              const params = {
                id: row.id,
                fileId: res.data.id,
                fileUrl: res.data.path,
                fileName: res.data.name,
                fileSize: file.size,
                taskId: row.typeId,
                projectId: this.projectId
              }

              return this.updateAttachment(params)
            } else {
              throw new Error(res.msg || '文件上传失败')
            }
          })
          .then(() => {
            this.$message.success('文件更新成功')
            this.getList(false)
          })
          .catch(error => {
            this.$message.error(error.message || '更新失败')
            console.error('更新失败:', error)
          })
          .finally(() => {
            document.body.removeChild(fileInput)
            this.isUpdating = false
          })
      })

      // 如果用户取消了文件选择，也需要重置状态
      fileInput.addEventListener('cancel', () => {
        document.body.removeChild(fileInput)
        this.isUpdating = false
      })

      // 触发文件选择对话框
      fileInput.click()
    },
    // 调用更新附件API
    updateAttachment(params) {
      return new Promise((resolve, reject) => {
        updateAttachmentAPI(params).then(res => {
          if (res.data && res.data.code === 0) {
            resolve(res.data)
          } else {
            reject(new Error(res.data.msg || '更新失败'))
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    handleDownload(item) {
      const params = {}
      params.id = item.id
      downloadAttachmentAPI(params).then((res) => {
        if (res.data.data) {
          fetch(res.data.data, {
            method: 'get',
            responseType: 'blob'
          })
            .then((response) => response.blob())
            .then((blob) => {
              const a = document.createElement('a')
              const URL = window.URL || window.webkitURL
              const href = URL.createObjectURL(blob)
              a.href = href
              a.download = item.fileName
              document.body.appendChild(a)
              a.click()
              document.body.removeChild(a)
              URL.revokeObjectURL(href)
            })
        }
      })
    },
    handleDelete(row) {
      // 删除附件
      this.$confirm('此操作将永久删除该附件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 执行删除API
        removeAttachmentAPI(row.id, row.typeId || '', this.projectId).then(res => {
          if (res.data && res.data.code === 0) {
            this.$message.success('删除成功')
            // 刷新列表
            this.getList(false)
          }
        })
      })
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.resource-table {
  height: 100%;
  padding: 0 15px;
  .operation-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    .operation-left {
      display: flex;
      align-items: center;
    }
    .operation-right {
      display: flex;
      align-items: center;
    }
  }
  // 点击查看按钮
  ::v-deep .click-view {
    cursor: pointer;
    color: var(--color-600);
    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
