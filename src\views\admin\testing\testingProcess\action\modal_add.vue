<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="流程名称" prop="processName">
        <el-input v-model.trim="form.processName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="流程描述" prop="processDesc">
        <el-input
          v-model.trim="form.processDesc"
          :autosize="false"
          :rows="3"
          type="textarea"
          style="width: 100%"
          maxlength="255"
          placeholder="请输入"
        />
      </el-form-item>
    </el-form>
    <el-divider content-position="left">流程任务</el-divider>
    <el-form
      ref="taskForm"
      :model="form"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="厂商可选" prop="vendorStatus">
        <el-switch
          v-model="form.vendorStatus"
          :active-value="1"
          :inactive-value="0"
        />
      </el-form-item>
      <el-form-item label="任务">
        <div class="task-list-scroll">
          <div
            v-for="(task, taskIdx) in form.testProcessTaskBOList"
            :key="taskIdx"
            class="task-card"
          >
            <div class="task-header">
              <el-form-item
                :prop="`testProcessTaskBOList.${taskIdx}.taskName`"
                :rules="[validate.required(), validate.name_64_char]"
                label="任务名称"
              >
                <el-input v-model.trim="task.taskName" placeholder="请输入" @blur="validateTaskName($event, 'task', taskIdx)" />
              </el-form-item>
              <div v-if="lastInputKey === `task-${taskIdx}` && nameRepeatMap[`task-${taskIdx}`]" class="error-info">任务名称不可重复，请重新输入</div>
              <i
                v-if="form.testProcessTaskBOList.length > 1"
                class="el-icon-delete task-delete"
                @click="removeTask(taskIdx)"
              />
            </div>
            <div class="subtask-list">
              <div
                v-for="(sub, subIdx) in task.sonTestProcessTaskBOList"
                :key="subIdx"
                class="subtask-row"
              >
                <el-form-item
                  :prop="`testProcessTaskBOList.${taskIdx}.sonTestProcessTaskBOList.${subIdx}.taskName`"
                  :rules="[validate.required(), validate.name_64_char]"
                  label="子任务"
                  class="subtask-item"
                >
                  <el-input v-model.trim="task.sonTestProcessTaskBOList[subIdx].taskName" placeholder="请输入" @blur="validateTaskName($event, 'sub', taskIdx, subIdx)" />
                </el-form-item>
                <div v-if="lastInputKey === `sub-${taskIdx}-${subIdx}` && nameRepeatMap[`sub-${taskIdx}-${subIdx}`]" class="error-info">任务名称不可重复，请重新输入</div>
                <div class="dash-line" />
                <i
                  v-if="task.sonTestProcessTaskBOList.length > 0"
                  class="el-icon-delete subtask-delete"
                  @click="removeSubtask(taskIdx, subIdx)"
                />
              </div>
              <div :class="{ 'is-disabled': hasNameRepeatError }" class="add-subtask-btn" @click="addSubtask(taskIdx)">+ 添加子任务</div>
            </div>
          </div>
          <div :class="{ 'is-disabled': hasNameRepeatError }" class="add-task-btn" @click="addTask">+ 添加任务</div>
        </div>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import validate from '@/packages/validate/index'
import { addTestProcessesApi, editTestProcessesApi, getTestProcessesByIdApi, getTaskProcessesByIdApi } from '@/api/testing/testProcesses.js'
export default {
  props: {
    // 传入数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String,
      default: 'addTestingProcess'
    }
  },
  data() {
    return {
      validate,
      loading: false,
      form: {
        processName: '',
        processDesc: '',
        vendorStatus: 0,
        testProcessTaskBOList: [
          { taskName: '', sonTestProcessTaskBOList: [] }
        ]
      },
      rules: {
        processName: [validate.required(), validate.name_64_char],
        processDesc: [validate.required()]
      },
      nameRepeatMap: {},
      lastInputKey: '' // 记录最后一次输入的key
    }
  },
  computed: {
    // 只有最后一次输入的key有重复才算有错误
    hasNameRepeatError() {
      return this.lastInputKey && this.nameRepeatMap[this.lastInputKey]
    }
  },
  mounted() {
    if (this.name == 'editTestingProcess') {
      getTestProcessesByIdApi(this.data[0].id).then((res) => {
        if (res.data && [0, 200].includes(res.code)) {
          const commonFiledArr = ['vendorStatus', 'processName', 'processDesc']
          commonFiledArr.map(key => {
            this.form[key] = res.data[key]
          })
        }
      })
      // 查询检测流程任务信息
      getTaskProcessesByIdApi(this.data[0].id).then((res) => {
        if (res.data && [0, 200].includes(res.code)) {
          // 转换接口数据为表单格式
          this.form.testProcessTaskBOList = (res.data || []).map(item => ({
            id: item.id,
            taskName: item.processName,
            sonTestProcessTaskBOList: (item.children || []).map(sub => ({
              id: sub.id,
              taskName: sub.processName
            }))
          }))
        }
      })
    }
  },
  methods: {
    // 校验任务名称和子任务名称重复，只校验最后一次输入
    validateTaskName(e, type, tIdx, sIdx) {
      let lastKey = ''
      if (type === 'task') {
        lastKey = `task-${tIdx}`
      } else if (type === 'sub') {
        lastKey = `sub-${tIdx}-${sIdx}`
      }
      this.lastInputKey = lastKey
      const allNames = []
      this.form.testProcessTaskBOList.forEach((task, taskIdx) => {
        allNames.push({ name: (task.taskName || '').trim(), key: `task-${taskIdx}` });
        (task.sonTestProcessTaskBOList || []).forEach((sub, subIdx) => {
          allNames.push({ name: (sub.taskName || '').trim(), key: `sub-${taskIdx}-${subIdx}` })
        })
      })
      const nameCount = {}
      allNames.forEach(item => {
        if (!item.name) return
        nameCount[item.name] = (nameCount[item.name] || 0) + 1
      })
      const repeatMap = {}
      allNames.forEach(item => {
        repeatMap[item.key] = item.name && nameCount[item.name] > 1
      })
      this.nameRepeatMap = repeatMap
    },
    findLastRepeatKey() {
      let lastKey = ''
      for (const key in this.nameRepeatMap) {
        if (this.nameRepeatMap[key]) {
          lastKey = key
        }
      }
      return lastKey
    },
    // 添加任务
    addTask() {
      if (this.hasNameRepeatError) return
      this.form.testProcessTaskBOList.push({ taskName: '', sonTestProcessTaskBOList: [] })
    },
    // 删除任务
    removeTask(idx) {
      if (this.lastInputKey === `task-${idx}`) this.lastInputKey = ''
      if (this.form.testProcessTaskBOList.length > 1) {
        this.form.testProcessTaskBOList.splice(idx, 1)
      }
      this.validateTaskName()
      this.lastInputKey = this.findLastRepeatKey()
    },
    // 添加子任务
    addSubtask(taskIdx) {
      if (this.hasNameRepeatError) return
      if (!this.form.testProcessTaskBOList[taskIdx].sonTestProcessTaskBOList) {
        this.$set(this.form.testProcessTaskBOList[taskIdx], 'sonTestProcessTaskBOList', [])
      }
      this.form.testProcessTaskBOList[taskIdx].sonTestProcessTaskBOList.push({ taskName: '' })
    },
    // 删除子任务
    removeSubtask(taskIdx, subIdx) {
      if (this.lastInputKey === `sub-${taskIdx}-${subIdx}`) this.lastInputKey = ''
      if (this.form.testProcessTaskBOList[taskIdx].sonTestProcessTaskBOList) {
        this.form.testProcessTaskBOList[taskIdx].sonTestProcessTaskBOList.splice(subIdx, 1)
      }
      this.validateTaskName()
      this.lastInputKey = this.findLastRepeatKey()
    },
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.validateTaskName()
      this.lastInputKey = this.findLastRepeatKey()
      // 检查是否有重复
      if (Object.values(this.nameRepeatMap).some(Boolean)) {
        return
      }
      this.$refs.form.validate(valid => {
        this.$refs.taskForm.validate(taskValid => {
          if (valid && taskValid) {
            this.loading = true
            const params = JSON.parse(JSON.stringify(this.form))
            if (this.name == 'addTestingProcess') {
              addTestProcessesApi(params).then((res) => {
                if ([0, 200].includes(res.code)) {
                  this.$message.success('新增成功')
                  this.$emit('call', 'refresh')
                  this.close()
                }
              }).finally(() => {
                this.loading = false
              })
            } else {
              params.id = this.data[0].id
              editTestProcessesApi(params).then((res) => {
                if ([0, 200].includes(res.code)) {
                  this.$message.success('编辑成功')
                  this.$emit('call', 'refresh')
                  this.close()
                }
              }).finally(() => {
                this.loading = false
              })
            }
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.drawer-wrap {
  .el-input,
  .el-select,
  .el-data-picker {
    width: 100%;
  }
}
.task-list-scroll {
  max-height: 400px;
  overflow-y: auto;
  ::v-deep .el-form-item {
    margin-bottom: 10px;
  }
  .task-card {
    background: #f3f6fe;
    border-radius: 4px;
    margin-bottom: 10px;
    padding: 10px 30px 8px 10px;
    .task-header {
      position: relative;
      .task-delete {
        color: var(--color-600);
        right: -20px;
        top: 10px;
        position: absolute;
        cursor: pointer;
      }
    }
    .subtask-list {
      margin-left: 26px;
      .subtask-row {
        position: relative;
        .subtask-delete {
          color: var(--color-600);
          right: -20px;
          top: 10px;
          position: absolute;
          cursor: pointer;
        }
        .dash-line {
          width: 15px;
          height: 33px;
          border-left: 1px dashed #676767;
          border-bottom: 1px dashed #676767;
          position: absolute;
          left: -20px;
          top: -12px;
        }
      }
      .add-subtask-btn {
        width: 85px;
        color: var(--color-600);
        font-size: 14px;
        cursor: pointer;
        background: none;
        border: none;
        outline: none;
        padding: 0;
      }
    }
  }
  .add-task-btn {
    width: 75px;
    color: var(--color-600);
    cursor: pointer;
    margin-left: 0;
    background: none;
    border: none;
    outline: none;
    padding: 0;
  }
}
.add-task-btn.is-disabled,
.add-subtask-btn.is-disabled {
  opacity: 0.7;
  color: var(--color-600);
  cursor: not-allowed;
}
.add-subtask-btn.is-disabled {
  cursor: not-allowed !important;
}
.error-info {
  font-size: 12px;
  color: #e45d5d;
  margin: -18px 0 0 40px;
}
</style>
