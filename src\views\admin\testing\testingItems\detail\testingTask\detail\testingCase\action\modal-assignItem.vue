<template>
  <div class="dialog-wrap">
    <batch-template
      :data="data"
      :available-data="availableData"
      view-key="title"
    />
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="指派给" prop="assignUser">
        <el-select v-model="form.assignUser" filterable style="width: 100%;" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.userId"
            :label="item.userName"
            :value="item.userId"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import { taskCaseAssignTo } from '@/api/testing/testCase.js'
import { getTaskQueryPersonApi } from '@/api/testing/index.js'
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
export default {
  name: 'AssignItem',
  components: {
    batchTemplate
  },
  mixins: [],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    }
  },
  data() {
    return {
      moduleName: module.name,
      form: {
        assignUser: ''
      },
      rules: {
        assignUser: [
          { required: true, message: '必填项', trigger: 'blur' }
        ]
      },
      availableData: [],
      options: []
    }
  },
  mounted() {
    this.getUserList()
    this.filterData()
  },
  methods: {
    filterData() {
      this.availableData = this.data.filter(item => {
        return item
      })
    },
    getUserList() {
      const params = {
        taskId: this.data[0].taskId || this.$route.params.id
      }
      getTaskQueryPersonApi(params).then(res => {
        this.options = res.data.data || []
      })
    },
    close() {
      this.$emit('close')
    },
    confirm() {
      this.$refs['form'].validate((valid, object) => {
        if (valid) {
          const params = {
            ids: this.availableData.map(item => item.id),
            assignUser: this.form.assignUser
          }
          taskCaseAssignTo(params).then(res => {
            if ([0, 200].includes(res.code)) {
              this.$message.success('指派成功')
              this.$emit('call', 'refresh')
              this.close()
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    }
  }
}
</script>
