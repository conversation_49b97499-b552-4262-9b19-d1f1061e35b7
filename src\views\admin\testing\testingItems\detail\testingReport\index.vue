<template>
  <el-row :gutter="20">
    <el-col v-if="!isOnlyTester" :span="24" style="margin-bottom: 15px;">
      <detail-card :key="cardKey" title="项目测试报告">
        <project slot="content" :data="data" @refresh="refresh"/>
      </detail-card>
    </el-col>
    <el-col :span="24">
      <detail-card :key="cardKey" title="任务测试报告">
        <task slot="content" :data="data" :is-only-tester="isOnlyTester" @refresh="refresh"/>
      </detail-card>
    </el-col>
  </el-row>
</template>

<script>
import detailCard from '@/packages/detail-view/detail-card.vue'
import project from './project/index.vue'
import task from './task/index.vue'

export default {
  components: {
    detailCard,
    project,
    task
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    id: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      cardKey: 0,
      roleIds: [],
      isOnlyTester: false, // 仅为检测人员
      userInfo: JSON.parse(localStorage.getItem('loginUserInfo')) || {}
    }
  },
  created() {
    // 检测项目负责人：181251，检测主管：181252，检测人员：181253，检测厂商：181254
    this.roleIds = this.userInfo.roleIds ? JSON.parse(this.userInfo.roleIds) : []
    this.isOnlyTester = this.roleIds.includes(181253) && !this.roleIds.includes(181251) && !this.roleIds.includes(181252)
  },
  methods: {
    refresh() {
      this.cardKey += 1
    }
  }
}
</script>

<style scoped lang="scss">

</style>
