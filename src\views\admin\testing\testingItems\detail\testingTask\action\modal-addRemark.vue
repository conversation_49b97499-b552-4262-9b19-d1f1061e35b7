<template>
  <div class="dialog-wrap">
    <myEditor
      :key="editorTimer"
      :editor-config="blurEditorFocusConfig"
      :content="questionAnalysis"
      :only-editor="!canOpt"
      :is-read-only="!canOpt"
      id-prefix="questionAnalysis"
      width="100%"
      height="300px"
      @contentChange="contentChange('questionAnalysis', $event)"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import myEditor from '@/packages/editor/index.vue'
import { operaRecordAddTaskRemark } from '@/api/testing/index.js'

export default {
  name: 'AddRemark',
  components: {
    myEditor
  },
  mixins: [],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    }
  },
  data() {
    return {
      moduleName: module.name,
      editorTimer: 'addRemark' + new Date().getTime(),
      canOpt: true, // 是否能编辑
      questionAnalysis: '',
      blurEditorFocusConfig: {
        placeholder: '请输入',
        autoFocus: false
      }
    }
  },
  computed: {},
  mounted() { },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm() {
      if (!this.questionAnalysis || this.questionAnalysis === '<p><br></p>') {
        this.$message.warning('请输入备注内容')
        return
      }
      const params = {}
      params.taskId = this.$route.params.id
      params.remark = this.questionAnalysis
      operaRecordAddTaskRemark(params)
        .then((res) => {
          if (res.data.code == 0) {
            this.$message.success('添加成功')
            this.$emit('call', 'refresh')
            this.close()
          }
        })
    },
    // 过滤html代码、空格、回车 空白字符
    'delHtml': function(str) {
      str = str.replace(/<("[^"]*"|'[^']*'|[^'">])*>/gi, '')
      str = str.replace(/[\r\n]/g, '')
      str = str.replace(/\s/g, '')
      str = str.replace(/&nbsp;/ig, '')
      return str
    },
    'contentChange': function(key, value) {
      if (this.delHtml(value)) {
        this.questionAnalysis = value
      } else {
        const imgStrs = value.match(/<img.*?>/g)
        if (imgStrs && imgStrs.length) { // 内容只有图片时
          this.questionAnalysis = value
        } else {
          this.questionAnalysis = ''
        }
      }
      if (String(this.delHtml(value)).length != 0) {
        // this.$refs['form'].validateField(key)
      }
    }
  }
}
</script>
