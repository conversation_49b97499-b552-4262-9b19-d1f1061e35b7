<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索测试任务"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="sessionId"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item === 'operation'">
            <el-link
              v-permission="'manage.testing.project.projectDetail.deviceLog.sessionRecords.deviceLogSessionRecords'"
              :underline="false"
              type="primary"
              size="small"
              @click="handlePlayback(scope.row)"
            >
              回看
            </el-link>
            <el-link
              v-permission="'manage.testing.project.projectDetail.deviceLog.sessionRecords.deviceLogDownloadSession'"
              :underline="false"
              type="primary"
              size="small"
              @click="handleDownload(scope.row)"
            >
              下载
            </el-link>
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import module from '../config.js'
import { downloadFileWithBuffer } from '@/utils'
import { getSessionRecordApi, downloadSessionApi, replaySessionRecordApi } from '@/api/testing/index'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      moduleName: module.name,
      searchKeyList: [
        { key: 'taskName', label: '测试任务', placeholder: '请输入测试任务名称', master: true },
        { key: 'round', label: '测试轮次', placeholder: '请输入测试轮次' },
        { key: 'realName', label: '用户', placeholder: '请输入用户名称' },
        { key: 'asset', label: '设备名称', placeholder: '请输入设备名称' },
        { key: 'account', label: '账号', placeholder: '请输入账号' },
        { key: 'protocol', label: '协议', placeholder: '请选择协议' },
        { key: 'loginFrom', label: '登录来源', placeholder: '请输入登录来源' },
        { key: 'remoteAddr', label: '远端地址', placeholder: '请输入远端地址' },
        { key: 'timeRange', label: '开始日期', type: 'time_range', placeholder: '请选择开始日期' }
      ],
      columnsObj: {
        'taskName': {
          title: '测试任务', master: true
        },
        'round': {
          title: '测试轮次'
        },
        'realName': {
          title: '用户'
        },
        'asset': {
          title: '设备名称'
        },
        'account': {
          title: '账号'
        },
        'protocol': {
          title: '协议'
        },
        'loginFrom': {
          title: '登录来源'
        },
        'remoteAddr': {
          title: '远端地址'
        },
        'dateStart': {
          title: '开始日期'
        },
        'duration': {
          title: '时长'
        },
        'operation': {
          title: '操作'
        }
      },
      columnsViewArr: [
        'taskName',
        'round',
        'realName',
        'asset',
        'account',
        'protocol',
        'loginFrom',
        'remoteAddr',
        'dateStart',
        'duration',
        'operation'
      ],
      projectId: this.$route.params.id || ''
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params.projectId = this.projectId
      if (params.timeRange) {
        params.dateStart = params.timeRange.split(',')[0]
        params.dateEnd = params.timeRange.split(',')[1]
        delete params.timeRange
      }
      getSessionRecordApi(params).then((res) => {
        this.tableData = (res.data && res.data.data) ? res.data.data.records : []
        this.tableTotal = res.data.data.total || 0
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
      })
    },
    handleDownload(row) {
      downloadSessionApi(row.sessionId).then(res => {
        downloadFileWithBuffer(res.data, `${this.data.name}_会话记录`, 'application/x-tar')
      })
    },
    // 回看会话记录
    handlePlayback(row) {
      replaySessionRecordApi(row.sessionId, this.projectId).then((res) => {
        window.open(res.data.data, '_blank')
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.resource-table {
  height: 100%;
  padding: 15px 0 0 0;
}
</style>
