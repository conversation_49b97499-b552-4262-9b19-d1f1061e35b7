<template>
  <div class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-width="120px">
      <el-form-item label="测试任务" prop="projectTestTaskId">
        <el-select
          v-model="formData.projectTestTaskId"
          filterable
          style="width: 100%;"
          placeholder="请选择"
          @change="getTaskStatus"
        >
          <el-option
            v-for="item in testTaskList"
            :key="item.taskId"
            :label="item.taskName"
            :value="item.taskId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="任务测试报告" prop="file">
        <div class="file-up">
          <el-button type="ghost" @click="selectFile()">上传文件</el-button>
        </div>
        <div
          v-show="file.name"
          style="border-color: var(--color-601-border); background-color: var(--color-601-background)"
          class="file-container"
        >
          <i :style="{ color: '#909399' }" class="el-icon-document" size="16" />
          <span
            :title="file.name"
            style="color: #000000d9;"
            class="file-name-ellipsis"
          >
            {{ file.name }}
          </span>
          <i
            style="color: var(--color-600);cursor: pointer;"
            class="el-icon-delete delete-icon delete"
            @click.stop="clearFileName()"
          />
        </div>
      </el-form-item>
    </el-form>
    <input
      id="importInputFile"
      ref="userFileInput"
      type="file"
      @change="handleChange"
    >
    <div class="dialog-footer">
      <el-button type="text" @click="closeView">取消</el-button>
      <el-button type="primary" @click="sureClick('form')">确定</el-button>
    </div>
  </div>
</template>

<script>
import validate from '@/packages/validate'
import {
  testingReportTaskCreate,
  testingFileUploadReport,
  testingReportById,
  testingReportUpdate,
  getTaskStatusApi,
  testingReportQueryLastRoundTask
} from '@/api/testing/index'
import { verifyFileTypeWithFileName } from '@/utils'
export default {
  components: {},
  props: {
    show: {
      type: Boolean,
      default: false
    },
    pageType: {
      type: String,
      default: 'questionBank' // or questionLibrary
    },
    questionDepotId: String, // 题库id
    content: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    },
    activeName: {
      type: [String, Number],
      default: null
    },
    taskName: {
      type: [String, Number],
      default: null
    },
    taskId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      validate: validate,
      maxSize: 600, // 文件最大大小
      loading: false,
      fileTypes: ['xlsx', 'xls', 'doc', 'docx', 'pdf'],
      showDialog: false,
      file: { name: '' },
      formData: {
        projectTestTaskId: '',
        file: ''
      },
      rules: {
        projectTestTaskId: [validate.required(['change'])],
        file: [validate.required(['blur', 'change'])]
      },
      testTaskList: [],
      resultData: null,
      apiType: testingReportTaskCreate,
      taskStatus: null
    }
  },
  computed: {},
  watch: {
    show: function(val) {
      this.showDialog = val
      this.resetData()
    }
  },
  created() {
    if (this.name == 'updateReport') {
      this.formData.projectTestTaskId = Number(this.content.taskId)
      this.getTaskStatus(this.formData.projectTestTaskId)
      this.apiType = testingReportUpdate
      testingReportById({ id: this.content.id, projectTestTaskId: this.formData.projectTestTaskId })
        .then(res => {
          if (res.data.code == 0) {
            this.file = res.data.data
            this.formData.file = res.data.data.fileUrl
            this.formData.fileUrl = res.data.data.fileUrl
            this.formData.fileName = res.data.data.name
            this.formData.size = res.data.data.size
          }
        })
        .catch(() => {})
    }
    this.getTestTaskListData()
  },
  methods: {
    // 获取任务状态
    getTaskStatus(id) {
      const data = {
        id
      }
      getTaskStatusApi(data).then(res => {
        this.taskStatus = res.data.data
      })
    },
    getTestTaskListData() {
      testingReportQueryLastRoundTask({ projectId: this.$route.params.id })
        .then(res => {
          if (res.data.code == 0) {
            this.testTaskList = this.getDisplayReport(res.data.data)
            this.formData.projectTestTaskId = this.testTaskList.find(item => item.taskName == this.taskName).taskId || ''
            if (this.formData.projectTestTaskId) {
              this.getTaskStatus(this.formData.projectTestTaskId)
            }
          }
        })
        .catch(() => {})
    },
    getDisplayReport(list) {
      const result = []
      list.forEach(item => {
        if (item.children && Array.isArray(item.children) && item.children.length > 0) {
          // 有子任务，只展示子任务
          result.push(...item.children)
        } else {
          // 没有子任务，展示自己
          result.push(item)
        }
      })
      return result
    },
    clearFileName() {
      this.file = { name: '' }
      this.formData.file = ''
      this.$refs.userFileInput.value = ''
    },
    sureClick(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (![1, 2, 3].includes(this.taskStatus)) {
            this.$message.error('仅测试任务状态为 “ 测试中、测试通过、测试不通过 ” 支持上传任务测试报告')
            return
          }
          const params = {}
          params.projectId = this.$route.params.id
          params.projectTestTaskId = this.formData.projectTestTaskId
          params.fileUrl = this.formData.fileUrl
          params.fileName = this.formData.fileName
          params.size = this.formData.size
          if (this.name == 'updateReport') {
            params.id = this.content.id
          }
          this.apiType(params)
            .then(res => {
              if (res.data.code == 0 && res.data.data) {
                this.$emit('call', 'refresh')
                this.closeView()
              }
            })
            .catch(() => {})
        }
      })
    },

    updateFile(file) {
      const formData = new FormData()
      formData.append('file', file[0])
      testingFileUploadReport(formData)
        .then(res => {
          if (res.data.code == 0) {
            this.file = file[0]
            this.formData.file = res.data.data
            this.formData.fileUrl = res.data.data
            this.formData.fileName = file[0].name
            this.formData.size = file[0].size
            this.$nextTick(() => {
              this.$refs['form'].clearValidate('file')
            })
          }
        })
        .catch(() => {
          this.clearFileName()
        })
    },

    /**
     * 选择文件选择文件
     */
    selectFile() {
      this.$refs.userFileInput.click()
    },

    /**
     * 选择触发
     */
    uploadFile(event) {
      var files = event.target.files
      const file = files[0]
      if (verifyFileTypeWithFileName(file.name)) {
        this.file = file
      }
      event.target.value = ''
    },
    handleChange(e) {
      const files = e.target.files
      if (!files || !files.length) {
        return
      }
      const fileArr = files[0].name.split('.')
      const fileType = fileArr[fileArr.length - 1]
      if (!this.fileTypes.includes(fileType.toLowerCase())) {
        this.$message.error('文件格式支持上传xlsx、xls、doc、docx、pdf文件，且大小不超过600MB')
        this.clearFileName()
        return
      }
      if (this.maxSize && (files[0].size / 1024 / 1024) > this.maxSize) {
        this.$message.error(
          `文件大小不能超过${this.$options.filters['transStore'](
            this.maxSize,
            'MB',
            0
          )}`
        )
        this.clearFileName()
        return
      } else {
        this.updateFile(files)
      }
    },

    /**
     * 关闭
     */
    closeView() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped lang="scss">
.el-steps {
  margin-bottom: 15px;

  /deep/ .el-step__title {
    font-size: 14px;
  }

  /deep/ .el-step.is-simple .el-step__arrow::before,
  /deep/ .el-step.is-simple .el-step__arrow::after {
    height: 10px;
    width: 2px;
  }

  /deep/ .el-step.is-simple .el-step__arrow::after {
    transform: rotate(45deg) translateY(3px);
  }
  /deep/ .el-step.is-simple .el-step__arrow::before {
    transform: rotate(-45deg) translateY(-2px);
  }
}

.file-up {
  display: flex;
  align-items: center;
}
.file-container {
  overflow-wrap: break-word;
  word-break: normal;
  line-height: 1.5;
  border: 1px solid;
  margin-top: 5px;
  padding: 6px 18px 6px 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;
  .file-name-ellipsis {
    display: inline-block;
    max-width: 260px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
  }
  .delete {
    position: absolute;
    top: 10px;
    right: 10px;
  }
}
/deep/ .el-loading-spinner {
  top: 45%;
  .el-icon-loading {
    font-size: 40px;
    color: #999;
  }
  .el-loading-text {
    color: #333;
  }
}

.content {
  padding: 10px 0;
}

.content-tips {
  font-size: 12px;
  color: #999;
  line-height: 15px;
}

#importInputFile {
  display: none;
}

.file-select {
  .el-input {
    width: 400px;
  }
  button {
    margin-left: 20px;
  }
}

.is-hidden {
  visibility: hidden;
}

// 结果信息
.result-info {
  text-align: center;
  padding-top: 30px;

  &__icon {
    font-size: 40px;
    color: $xr-color-primary;
  }

  &__des {
    margin-top: 15px;
    color: #333;
    font-size: 14px;
  }

  &__detail {
    margin-top: 15px;
    font-size: 12px;
    color: #666;
    &--all {
      color: #333;
      font-weight: 600;
    }

    &--suc {
      color: $xr-color-primary;
      font-weight: 600;
    }

    &--err {
      color: #f94e4e;
      font-weight: 600;
    }
  }

  &__btn--err {
    margin-top: 10px;
  }
}
</style>
