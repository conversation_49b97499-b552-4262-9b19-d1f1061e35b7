<template>
  <div class="resource-table" style="padding: 0;">
    <split-pane ref="split-pane" :min-percent="minPercent" :default-percent="percent" split="vertical" @resize="resize">
      <div
        slot="paneL"
        class="collapse-transition"
        style="padding-right: 0;height: 100%;"
      >
        <div class="tree-container">
          <tree
            ref="treeRef"
            :tree-width="treeWidth"
            :i-search="false"
            :tree-data="treeData"
            :default-props="defaultProps"
            :default-expanded-keys="treeData.length > 0 ? [treeData[0].id] : []"
            :default-checked-keys="treeData.length > 0 ? [treeData[0].id] : []"
            :is-show-dropdown="false"
            :is-show-data-count="true"
            :is-show-children-num="false"
            @currentTreeNode="currentTreeNode"
            @clearCurrentNode="clearCurrentNode"
          />
        </div>
      </div>
      <div
        slot="paneR"
        ref="tableColRef"
        class="resource-table collapse-transition"
      >
        <!-- 操作区 -->
        <div class="operation-wrap">
          <div class="operation-left">
            <slot name="action" />
            <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
          </div>
          <div class="operation-right">
            <el-badge :value="searchBtnShowNum">
              <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
            </el-badge>
            <!-- 自定义表格列 -->
            <t-table-config
              :data="columnsObj"
              :active-key-arr="columnsViewArr"
              style="padding-left: 5px;"
              @on-change-col="onChangeCol"
            />
          </div>
        </div>
        <div
          class="collapse-btn"
          @click="toggleCollapse"
        >
          <i
            :class="fold ? 'el-icon-caret-right' : 'el-icon-caret-left'"
          />
        </div>
        <!-- 搜索区 -->
        <t-search-box
          v-show="searchView"
          :search-key-list="searchKeyListView"
          :default-placeholder="'默认搜索测试用例标题'"
          @search="searchMultiple"
        />
        <!-- 列表 -->
        <t-table-view
          ref="tableView"
          :height="height"
          :loading="tableLoading"
          :data="tableData"
          :total="tableTotal"
          :page-size="pageSize"
          :current="pageCurrent"
          :select-item="selectItem"
          :default-sort="{ prop: 'sortNumber', order: 'descending' }"
          current-key="id"
          @on-select="onSelect"
          @on-current="onCurrent"
          @on-change="changePage"
          @on-sort-change="onSortChange"
          @on-page-size-change="onPageSizeChange"
        >
          <el-table-column
            v-for="item in columnsViewArr"
            :key="item"
            :min-width="colMinWidth"
            :width="columnsObj[item].colWidth"
            :label="columnsObj[item].title"
            :fixed="columnsObj[item].master ? 'left' : false"
            :sortable="columnsObj[item].sortable"
            :prop="item"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span v-if="item === 'sortNumber'">
                <a :href="`/testing/testingCaseDetail/${scope.row.caseId}/overview?routeSearch=true&searchVal=${scope.row.sortNumber}&searchKey=sortNumber&moduleName=testCases`" target="_blank">
                  {{ scope.row[item] || '-' }}
                </a>
              </span>
              <span v-else-if="item === 'title'">
                <a :href="`/testing/testingCaseDetail/${scope.row.caseId}/overview?routeSearch=true&searchVal=${scope.row.title}&searchKey=title&moduleName=testCases`" target="_blank">
                  {{ scope.row[item] || '-' }}
                </a>
              </span>
              <span v-else-if="item === 'priority'">
                <el-badge
                  :type="
                    (module.priorityObj[scope.row.priority] &&
                      module.priorityObj[scope.row.priority].type) ||
                      'info'
                  "
                  is-dot
                />
                {{
                  (module.priorityObj[scope.row.priority] &&
                    module.priorityObj[scope.row.priority].label) ||
                    "-"
                }}
              </span>
              <span v-else-if="item === 'type'">
                {{
                  (module.typeObj[scope.row.type] &&
                    module.typeObj[scope.row.type].label) ||
                    "-"
                }}
              </span>
              <span v-else-if="item === 'projectName'">
                <a v-if="scope.row.projectId" :href="`/testing/testing/detail/${scope.row.projectId}/overview`" target="_blank">
                  {{ scope.row[item] || '-' }}
                </a>
                <span v-else>
                  -
                </span>
              </span>
              <span v-else-if="item === 'referenceCount'">
                {{ scope.row[item].toString() || "-" }}
              </span>
              <span v-else>
                {{ scope.row[item] || "-" }}
              </span>
            </template>
          </el-table-column>
        </t-table-view>
      </div>
    </split-pane>
  </div>
</template>

<script>
import { caseCategoryTreeUrlName, caseProjectPage, getSuiteCaseList, suiteAssociationCase } from '@/api/testing/testCase.js'
import mixinsPageTable from '@/packages/mixins/page_table'
import splitPane from '@/packages/mixins/split-pane'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import tTableView from '@/packages/table-view/index.vue'
import tree from '@/packages/tree/index.vue'
import module from '../config.js'

export default {
  name: 'CaseListTable',
  components: {
    tree,
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable, splitPane],
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'label',
        count: 'count'
      },
      module,
      moduleName: module.name,
      selectItem: [],
      // 排序相关
      sortField: '',
      sortOrder: '',
      searchKeyList: [
        { key: 'title', label: '用例标题', master: true, placeholder: '请输入测试用例标题' },
        { key: 'sortNumber', label: '用例编号' },
        { key: 'type', label: '类型', type: 'select', valueList: module.typeArr },
        { key: 'priority', label: '优先级', type: 'select', valueList: module.priorityArr },
        { key: 'projectName', label: '关联项目', type: 'select', valueList: [] },
        { key: 'categoryName', label: '分类' },
        { key: 'createByName', label: '创建人' },
        { key: 'createAt', label: '创建时间', type: 'time_range' }
      ],
      columnsObj: {
        'sortNumber': {
          title: '用例编号', master: true, colWidth: 100, sortable: true
        },
        'title': {
          title: '用例标题', master: true, sortable: true
        },
        'type': {
          title: '类型', sortable: true
        },
        'priority': {
          title: '优先级', colWidth: 90, sortable: true
        },
        'projectName': {
          title: '关联项目', sortable: true
        },
        'categoryName': {
          title: '分类', sortable: true
        },
        'referenceCount': {
          title: '引用次数', sortable: true
        },
        'createByName': {
          title: '创建人', sortable: true
        },
        'createAt': {
          title: '创建时间', sortable: true
        }
      },
      columnsViewArr: [
        'sortNumber',
        'title',
        'type',
        'priority',
        'projectName',
        'categoryName',
        'referenceCount',
        'createByName',
        'createAt'
      ],
      // 测试用例数据
      caseList: [],
      searchParams: {},
      requestParams: {},
      // 树相关
      currentNodeId: '0',
      treeData: [],
      treeWidth: 280
    }
  },
  computed: {
    'searchBtnShowNum': function() { // 搜索项的数量
      if (this.searchView) return null
      return Object.keys(this.searchParams).length || null
    }
  },
  async created() {
    await this.getTreeData()
    await this.getProjectList()
    // 初始加载数据
    this.initDefaultTree()
  },
  methods: {
    async getProjectList() {
      const res = await caseProjectPage({ pageType: 0 })
      if (res && res.code === 0) {
        const projectItem = this.searchKeyList.find(item => item.key === 'projectName')
        if (projectItem) {
          projectItem.valueList = res.data.records.map(item => ({
            label: item.projectName,
            value: String(item.id)
          }))
        }
      }
    },
    toggleCollapse() {
      this.fold = !this.fold
      if (this.fold) {
        this.percent = this.minPercent
      } else {
        this.percent = 20
      }
    },
    // 初始化默认树选择
    initDefaultTree() {
      // 使用树的根分类ID
      if (this.treeData && this.treeData.length > 0) {
        this.currentNodeId = this.treeData[0].id
      } else {
        this.currentNodeId = '0' // 如果没有树数据，使用默认值
      }
      // 加载数据
      this.getList()
      // 确保根节点被选中
      this.$nextTick(() => {
        if (this.$refs.treeRef) {
          this.$refs.treeRef.setCurrentKey(this.currentNodeId)
        }
      })
    },
    clearCurrentNode() {
      // 重置为根分类
      if (this.treeData && this.treeData.length > 0) {
        this.currentNodeId = this.treeData[0].id
      } else {
        this.currentNodeId = '0'
      }
      this.getList()
    },
    currentTreeNode(data, node) {
      if (data && data.id !== undefined) {
        this.currentNodeId = data.id
        // 保留当前搜索条件，结合树节点进行搜索
        this.getList(this.searchParams)
      }
    },
    // 转换树结构数据，将id作为nodeKey，name作为label
    transformTreeData(data) {
      if (!data || !Array.isArray(data)) return []

      return data.map(item => {
        const node = {
          nodeKey: item.id,
          label: item.name || '未命名分类',
          id: item.id,
          parentId: item.parentId,
          type: item.type,
          count: item.count
        }

        if (
          item.children &&
          Array.isArray(item.children) &&
          item.children.length > 0
        ) {
          node.children = this.transformTreeData(item.children)
        }

        return node
      })
    },
    // 获取分类树结构
    async getTreeData() {
      try {
        this.tableLoading = true
        const params = {
          type: 1,
          busId: this.$route.params.id,
          urlName: 'suite-case'
        }
        const res = await caseCategoryTreeUrlName(params)

        if (res && res.data) {
          // 转换树结构数据
          this.treeData = this.transformTreeData(res.data)
        } else {
          this.treeData = []
        }
      } catch (error) {
        console.error('获取分类树结构失败:', error)
        this.treeData = []
      } finally {
        this.tableLoading = false
      }
    },
    // 获取数据列表
    getList(params = {}, showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      this.searchParams = { ...params }
      const requestParams = this.getPostData('page', 'limit')

      // 合并传入的参数
      if (params && Object.keys(params).length > 0) {
        Object.assign(requestParams, params)
      }

      requestParams.suiteId = this.$route.params.id
      requestParams.pageType = 1

      // 处理类型
      if (requestParams.type) {
        requestParams.type = requestParams.type.split(',')
      }
      // 处理优先级
      if (requestParams.priority) {
        requestParams.priority = requestParams.priority.split(',')
      }
      // 处理关联项目
      if (requestParams.projectName) {
        requestParams.projectIds = requestParams.projectName.split(',')
      }
      // 处理时间范围
      if (requestParams.createAt) {
        const timeArr = requestParams.createAt.split(',')
        if (timeArr.length === 2) {
          requestParams.createTimeStart = timeArr[0]
          requestParams.createTimeEnd = timeArr[1]
        }
      }

      // 处理排序
      if (this.sortField) {
        requestParams.sortField = this.sortField
        requestParams.sortOrder = this.sortOrder || ''
      } else if (!this.sortField && !params.sortField) {
        // 默认排序
        requestParams.sortField = 'sortNumber'
        requestParams.sortOrder = 'desc'
      }

      // 添加分类ID
      if (this.currentNodeId !== undefined && this.currentNodeId !== '0') {
        requestParams.categoryId = this.currentNodeId
      }

      this.requestParams = requestParams
      // 显示当前测试套件包含的测试用例
      getSuiteCaseList(requestParams).then(res => {
        this.tableData = res.data.records
        this.tableTotal = res.data.total
        this.tableLoading = false
        this.handleSelection()
      }).finally(() => {
        this.tableLoading = false
      })
    },
    // 添加测试用例
    addTestCases(cases) {
      suiteAssociationCase({
        suiteId: this.$route.params.id,
        caseIds: cases.map(item => item.id)
      }).then(res => {
        if ([0, 200].includes(res.code)) {
          this.$message.success('添加成功')
        }
        this.refresh()
      })
    },
    // 删除测试用例
    handleDelete(row) {
      this.$confirm('确定要从测试套件中移除该测试用例吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 从列表中删除
        this.caseList = this.caseList.filter(item => item.id !== row.id)
        this.$message.success('已成功移除测试用例')
        this.getList()
      }).catch(() => {
        // 取消删除
      })
    },
    // 批量删除测试用例
    deleteTestCases(cases) {
      if (!cases || !cases.length) return

      // 从列表中删除选定的测试用例
      const caseIds = cases.map(item => item.id)
      this.caseList = this.caseList.filter(item => !caseIds.includes(item.id))

      // 刷新表格
      this.getList()

      // 清空选择
      this.selectItem = []
    },
    // 处理排序变化
    onSortChange({ column, prop, order }) {
      // 映射排序字段
      const fieldMapping = {
        '用例编号': 'sortNumber',
        '用例标题': 'title',
        '分类': 'categoryName',
        '优先级': 'priority',
        '类型': 'type',
        '引用次数': 'referenceCount',
        '创建人': 'createByName',
        '创建时间': 'createAt'
      }

      this.sortField = fieldMapping[column.label] || prop || ''
      if (order === 'descending') {
        this.sortOrder = 'desc'
      } else if (order === 'ascending') {
        this.sortOrder = 'asc'
      } else {
        this.sortOrder = ''
      }
      this.getList()
    },
    // 处理搜索
    searchMultiple(params) {
      this.pageSize = 10
      this.pageCurrent = 1
      this.getList(params)
    },
    // 刷新表格
    'refresh': function() {
      this.$emit('refresh')
      this.selectItem = []
      if (this.single) {
        this.$emit('on-select', [])
        this.setHighlightRow(null)
      }
      this.getTreeData()
      this.getList(this.searchParams)
    }
  }
}
</script>

<style lang="scss" scoped>
.resource-table {
  height: 100%;
  padding: 0;
  padding-bottom: 15px;

  .collapse-btn {
    z-index: 200;
    position: absolute;
    top: calc(50% - 80px);
    left: -10px;
    width: 10px;
    height: 60px;
    background-color: var(--color-600);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .collapse-transition {
    transition: all 0.3s ease;
  }
}

.operation-right {
  display: flex;
  align-items: center;
}


.tree-container {
  height: 100%;
  overflow-y: auto;
  border: 1px solid var(--neutral-300);
}

.operation-wrap {
  flex-shrink: 0;
}

.t-table-view {
  flex: 1;
  overflow: hidden;
}
</style>
