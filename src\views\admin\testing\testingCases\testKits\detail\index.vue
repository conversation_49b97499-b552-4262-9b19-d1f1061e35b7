<template>
  <div class="content-wrap-layout">
    <detail-view
      :loading="loading"
      :data="data"
      :id="id"
      :view-item="viewItem"
      mode="drawer"
      title-key="name"
    />
  </div>
</template>

<script>
import detailView from '@/packages/detail-view/index'
import moduleConf from '../config'
import detailOverview from './detail_overview.vue'
import caseList from './caseList/index.vue'
import { testSuiteGet } from '@/api/testing/testCase.js'

export default {
  name: 'TestCaseDetail',
  components: {
    detailView,
    detailOverview,
    caseList
  },
  data() {
    return {
      moduleName: moduleConf.name,
      id: null,
      loading: false,
      data: null,
      viewItem: [
        {
          transName: '概况',
          name: 'overview',
          component: detailOverview
        },
        {
          transName: '测试用例',
          name: 'caseList',
          component: caseList
        }
      ]
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.params.hasOwnProperty('id') ? to.params.id : null
      const fromId = from.params.hasOwnProperty('id') ? from.params.id : null
      if (toId !== fromId) {
        this.loadBase()
      }
    }
  },
  created() {
    this.loadBase()
  },
  methods: {
    actionHandler: function(type) {
      if (type === 'refresh') {
        this.loadBase()
      }
    },
    loadBase: function() {
      this.id = this.$route.params.id
      this.getData(this.id)
    },
    // 获取测试用例详情
    getData: function(id) {
      if (id) {
        this.loading = true
        testSuiteGet(id).then(res => {
          this.data = res.data
        }).finally(() => {
          this.loading = false
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.case-detail {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;

  .el-page-header {
    margin-bottom: 20px;
  }

  .el-tabs {
    height: calc(100% - 40px);
  }
}
</style>
