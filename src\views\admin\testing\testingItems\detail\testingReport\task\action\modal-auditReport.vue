<template>
  <div class="dialog-wrap">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="测试报告名称">
        <span class="name-row">
          <span
            :title="content.name"
            class="name-ellipsis"
          >{{ content.name || '-' }}</span>
          <span class="action-btns">
            <el-link :disabled="false" :underline="false" type="primary" @click.stop="handlePreview">预览</el-link>
            <el-link :disabled="false" :underline="false" type="primary" @click.stop="handleDown">下载</el-link>
          </span>
        </span>
      </el-form-item>
      <el-form-item label="审核结果" prop="auditStatus">
        <el-radio-group v-model="form.auditStatus" size="small">
          <el-radio-button label="1">通过</el-radio-button>
          <el-radio-button label="2">驳回</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审核意见" prop="auditOpinion">
        <el-input v-model.trim="form.auditOpinion" type="textarea"/>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm('form')">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import validate from '@/packages/validate'
import { testingReportAudit, testingReportDownloadById, testingReportById } from '@/api/testing/index'
export default {
  name: 'AuditReport',
  components: {},
  mixins: [],
  props: {
    content: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    }
  },
  data() {
    return {
      validate: validate,
      moduleName: module.name,
      form: {
        auditStatus: '1',
        auditOpinion: ''
      },
      rules: {
        auditStatus: [validate.required(['blur', 'change'])],
        auditOpinion: [{ validator: this.auditOpinionValidate, trigger: 'blur' }]
      }
    }
  },
  computed: {},
  watch: {
    'form.auditStatus'(val) {
      if (val === '2') {
        this.$set(this.rules, 'auditOpinion', [
          validate.required(),
          { validator: this.auditOpinionValidate, trigger: 'blur' }
        ])
      } else {
        this.$set(this.rules, 'auditOpinion', [
          { validator: this.auditOpinionValidate, trigger: 'blur' }
        ])
        this.$refs['form'].clearValidate('auditOpinion')
      }
    },
    'form.auditOpinion'(val) {
      if (val) {
        this.$refs['form'].clearValidate('auditOpinion')
      }
    }
  },
  mounted() {
  },
  methods: {
    auditOpinionValidate(rule, value, callback) {
      const textTip = this.form.auditStatus == '1' ? '0-255个字符' : '1-255个字符'
      if (value && value.length > 255) {
        callback(new Error(textTip))
      } else {
        callback()
      }
    },
    // 查看附件
    handlePreview() {
      testingReportById({ id: this.content.id }).then(res => {
        if (res.data.code == 0) {
          if (this.content && this.content.name.includes('zip')) {
            this.$message.warning('该文件不支持预览，请下载查看')
            return
          }
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(window.ADMIN_CONFIG.VIP_URL + res.data.data.fileUrl))
          window.open(this.previewUrl, '_blank')
        }
      })
    },
    // 下载附件
    handleDown() {
      testingReportDownloadById({ id: this.content.id }).then(res => {
        if (res.data.data) {
          fetch(res.data.data, {
            method: 'get',
            responseType: 'blob'
          })
            .then((response) => response.blob())
            .then((blob) => {
              const a = document.createElement('a')
              const URL = window.URL || window.webkitURL
              const href = URL.createObjectURL(blob)
              a.href = href
              a.download = this.content.name
              document.body.appendChild(a)
              a.click()
              document.body.removeChild(a)
              URL.revokeObjectURL(href)
            })
        }
      })
    },
    close() {
      this.$emit('close')
    },
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const data = {
            id: this.content.id,
            auditStatus: this.form.auditStatus,
            auditOpinion: this.form.auditOpinion
          }
          testingReportAudit(data)
            .then((res) => {
              if (res.data.code == 0 && res.data.data) {
                this.$message.success('审核成功')
                this.$emit('call', 'refresh')
                this.close()
              }
            })
        }
      })
    }
  }
}
</script>
<style scoped>
.name-row {
  display: flex;
  align-items: center;
}
.name-ellipsis {
  max-width: 240px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  vertical-align: middle;
}
.action-btns {
  margin-left: 16px;
  flex-shrink: 0;
}
</style>
