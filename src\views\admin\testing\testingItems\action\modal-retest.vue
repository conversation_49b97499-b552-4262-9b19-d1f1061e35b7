<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert
      v-if="redeploy"
      :closable="false"
      type="warning"
      title="需要重新部署环境进行复测。"
    />
    <el-form ref="form" :model="formData" :rules="rules" label-width="120px" label-position="left">
      <el-form-item label="检测项目">
        <span>{{ data[0].name }}</span>
      </el-form-item>

      <el-form-item label="当前状态">
        <span>{{ getStatusLabel(data[0].status) }}</span>
      </el-form-item>

      <el-form-item label="复测任务" prop="taskIdList">
        <el-checkbox-group v-model="formData.taskIdList">
          <el-checkbox v-for="task in testTasks" :key="task.id" :label="task.id">
            {{ task.processName }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="申请说明" prop="reason">
        <el-input
          v-model.trim="formData.reason"
          :rows="4"
          type="textarea"
          placeholder="请输入"
        />
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import { retestApplyAPI, getCurrentTestTaskAPI, getEnvType } from '@/api/testing/index'
import module from '../config'
import validate from '@/packages/validate'
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      formData: {
        taskIdList: [],
        reason: ''
      },
      rules: {
        taskIdList: [
          { type: 'array', required: true, message: '请至少选择一项复测任务', trigger: 'change' }
        ],
        reason: [validate.required(), validate.description]
      },
      testTasks: [],
      statusArr: module.statusArr,
      redeploy: 0
    }
  },
  computed: {
    // 筛选返回可操作数据
    availableArr() {
      // 只有测试不通过的项目才能申请复测
      return this.data.filter(item => item.status === '4')
    }
  },
  created() {
    this.fetchTestTasks()
    this.getProjectEnvType()
  },
  methods: {
    // 获取项目测试环境
    getProjectEnvType() {
      getEnvType(this.data[0].id).then(res => {
        this.redeploy = (res.data && res.data.data) ? res.data.data.redeploy : false
      })
    },
    // 获取检测任务列表
    fetchTestTasks() {
      if (this.data.length === 0) return
      this.loading = true
      getCurrentTestTaskAPI(this.data[0].id).then(res => {
        if (res.data.code === 0) {
          // 仅展示测试不通过的数据
          this.testTasks = this.getDisplayTasks(res.data.data).filter(item => item.status == '3')
          // 默认全选
          this.formData.taskIdList = this.testTasks.map(task => task.id)
        } else {
          this.$message.error(res.data.msg || '获取任务列表失败')
        }
      }).catch(() => {
        this.$message.error('获取任务列表失败')
      }).finally(() => {
        this.loading = false
      })
    },
    // 处理任务数据
    getDisplayTasks(tasks) {
      const result = []
      tasks.forEach(task => {
        if (Array.isArray(task.children) && task.children.length > 0) {
          task.children.forEach(child => {
            result.push({
              status: child.status,
              id: child.id || child.sourceTaskId,
              processName: child.name || child.processName
            })
          })
        } else {
          result.push({
            status: task.status,
            id: task.id || task.sourceTaskId,
            processName: task.name || task.processName
          })
        }
      })
      return result
    },
    getStatusLabel(status) {
      return this.statusArr.find(item => item.value == status) ? this.statusArr.find(item => item.value == status).label : ''
    },
    close() {
      this.$emit('close')
    },
    confirm() {
      this.$refs.form.validate(valid => {
        if (!valid) return

        this.loading = true
        const id = this.data[0].id
        const params = {
          reason: this.formData.reason,
          taskIdList: this.formData.taskIdList
        }

        // 调用API
        retestApplyAPI(id, params).then(res => {
          if (res.data.code === 0) {
            this.$message.success('申请复测成功')
            this.$emit('call', 'refresh')
            this.close()
          } else {
            this.$message.error(res.data.msg || '申请复测失败')
          }
        }).finally(() => {
          this.loading = false
        })
      })
    }
  }
}
</script>
