export default {
  name: 'testingTask',
  // 测试状态映射
  statusArr: [
    { label: '未开始', value: 0, type: 'info' },
    { label: '测试中', value: 1, type: 'warning' },
    { label: '测试通过', value: 2, type: 'success' },
    { label: '测试不通过', value: 3, type: 'danger' }
  ],
  get statuStrArr() {
    return this.statusArr.map(item => {
      return { label: item.label, value: String(item.value), type: item.type }
    })
  },
  // 将数组转换为对象形式，方便查找
  get statusObj() {
    return this.statusArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  },
  get statusMapping() {
    return this.statusArr.reduce((acc, curr) => {
      acc[curr.value] = curr.label
      return acc
    }, {})
  }
}
