<template>
  <div v-loading="loading" class="wrapper" element-loading-text="加载拓扑信息中...">
    <topology
      v-if="topologyId"
      ref="topo"
      :topo-id="topologyId"
      :scene-name="data.sceneName"
      :show-to-console="true"
      :scene-id="data.sceneTypeId"
      :topo-type="'consolePermissions'"
      :testing-module="testingModule"
      style="height: 100%;"
      @toConsole="toConsole"
    />
    <el-empty
      v-else
      :image="img"
      :image-size="110"
      style="margin: 100px auto"
      description="暂无数据"
    />
  </div>
</template>

<script>
import { taskInstanceConsole, instanceConsole } from '@/api/testing/index'
import topology from '@/packages/topo/index'

export default {
  components: {
    topology
  },
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      topologyId: '',
      img: require('@/packages/table-view/nodata.png'),
      loading: false,
      routeName: '',
      topologyTypeMap: {
        'testing_detail': 'testingEdit',
        'testingTask_detail': 'testingNo'
      },
      testingModule: ''
    }
  },
  watch: {
    topologyId: {
      handler(newVal) {
        if (newVal) {
          // 向父组件传递拓扑ID变更事件
          this.$emit('topologyIdChanged', newVal)
        }
      },
      immediate: true
    }
  },
  mounted() {
    // 1.testing_detail: 检测项目测试环境
    // 2.testingTask_detail：测试任务详情测试环境
    this.routeName = this.$route.name
    this.$set(this, 'testingModule', this.topologyTypeMap[this.routeName])
    this.getTopologyInfo()
  },
  methods: {
    toConsole() {
      if (this.$route.query.taskId) {
        const params = {
          vmId: null,
          projectId: this.$route.params.projectId,
          taskId: this.$route.params.id,
          ip: null
        }
        taskInstanceConsole(params).then((res) => {
          window.open(res.data.data, '_blank')
        })
      } else {
        const params = {
          vmId: null,
          projectId: this.$route.params.id,
          ip: null
        }
        instanceConsole(params).then((res) => {
          window.open(res.data.data, '_blank')
        })
      }
    },
    getTopologyInfo() {
      // 如果有场景ID，则调用API获取拓扑信息
      if (this.data) {
        this.topologyId = this.data.topologyTemplateId
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.wrapper {
  height: 100%;
  width: calc(100% - 30px);
  padding: 0 15px 10px 15px;
}
</style>
