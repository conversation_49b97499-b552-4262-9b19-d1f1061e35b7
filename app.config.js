const VIP = '**************' // 要连接的环境的VIP（连不同的环境，只需要修改这个地址即可）
const PORT = '38080' // 如果连接的是后端本地IP，需将此端口改为后端本地启动的端口

module.exports = {
  VIP,
  PORT,
  // 项目 远程数据源 配置, 项目部署 static/config.js 文件以此为范本
  // 后台-主项目配置
  ADMIN_CONFIG: {
    BASE_API: '/api/', // base api
    VIP_URL: 'https://' + VIP + ':' + PORT, // 要连接的环境的完整地址
    WS_PREFIX: 'wss://' + VIP + ':' + PORT, // 靶场ws
    MATCH_URL: 'http://localhost:58080', // 比赛平台url
    BIG_SCREEN_URL: 'http://localhost:48080', // 多维大屏url
    PENETRANT_URL: 'http://localhost:8098', // 渗透平台url
    EXCLUSIVE_URL: 'https://***********:58080', // ctf独享模式跳转ip
    LARGE_SCREEN_SHOW: true // 是否展示导航栏3D大屏入口
  },
  // 后台-网络编排-配置
  NFVO_CONFIG: {
    nfvo_api: '/nfvo/api', // api前缀
    VIP_URL: 'https://' + VIP + ':' + PORT, // 要连接的环境的完整地址
    socket_url: 'wss://' + VIP + ':' + PORT // nfvo ws地址
  }
}
