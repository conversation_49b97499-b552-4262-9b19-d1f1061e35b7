<template>
  <div class="login-by-pwd">
    <el-form ref="form" :rules="rules" :model="form" class="login-from">
      <div class="account-title">账号</div>
      <el-form-item prop="username">
        <el-input
          ref="username"
          v-model.trim="form.username"
          :class="{error: !validateRes.username}"
          placeholder="请输入您的账号"
          prefix-icon="el-icon-user"
          type="text"
          @focus="focusKey = 'username'"
          @blur="checkFromItem('username', form.username)"
          @keyup.enter.native="debouncedHandleLogin"
        />
      </el-form-item>
      <div class="psd-title">密码</div>
      <el-form-item prop="password">
        <el-input
          ref="password"
          v-model.trim="form.password"
          :maxlength="20"
          :class="{error: !validateRes.password}"
          :type="eyeOpen ? 'password' : 'text'"
          prefix-icon="el-icon-lock"
          placeholder="请输入您的密码"
          @focus="focusKey = 'password'"
          @keyup.enter.native="debouncedHandleLogin"
          @blur="checkFromItem('password', form.password)">
          <!-- 打开 -->
          <svg v-if="!eyeOpen" slot="suffix" t="1595297125238" class="form-control-feedback" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" @click="eyeOpen=true">
            <path d="M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 0 0 0 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766z m-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176z m0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z" p-id="4042" />
          </svg>
          <!-- 关闭 -->
          <svg v-if="eyeOpen" slot="suffix" t="1595296629226" class="form-control-feedback" viewBox="0 0 1267 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" @click="eyeOpen=false">
            <path d="M954.78595663 116.62496767L1001.70161092 163.56275107 257.82293917 907.4856826l-46.93778339-46.93778417z" p-id="2672" />
            <path d="M445.79537486 483.40800716a162.87698604 162.87698604 0 0 1 240.11077449-113.74833038l47.35825378-47.35825378a228.16056078 228.16056078 0 0 0-316.4593616 316.45936238l47.35825379-47.35825456a162.87698604 162.87698604 0 0 1-18.36792046-107.99452366zM1009.44711928 451.09817057a110.65012687 110.65012687 0 0 1 0 121.27253821c-102.46201747 148.04986937-249.84798603 232.80786641-404.31556215 232.8078664a436.62539949 436.62539949 0 0 1-186.99871398-42.93224866l-49.79255727 49.79255647a517.84259276 517.84259276 0 0 0 237.45517205 58.42326694c180.58100647 0 341.68759063-94.49520782 452.55901726-243.43027852a197.84242662 197.84242662 0 0 0 0-229.48836238 645.97543877 645.97543877 0 0 0-137.20615673-137.6487578l-49.34995621 48.68605618a589.54387424 589.54387424 0 0 1 137.64875703 142.51736316z" p-id="2673" />
            <path d="M291.10649744 764.45932868l48.46475565-48.46475564a588.87997344 588.87997344 0 0 1-138.75525887-143.62386426 108.21582418 108.21582418 0 0 1 0-121.27253821C303.27801169 303.04830121 450.66398024 218.29030417 605.13155713 218.29030417a436.84670003 436.84670003 0 0 1 188.54781536 44.26005028l50.23515752-50.89905833a518.94909385 518.94909385 0 0 0-238.11907208-58.42326693c-180.58100647 0-341.68759063 94.49520782-452.55901803 243.43027851A192.97382046 192.97382046 0 0 0 116.50059822 511.73443968a192.97382046 192.97382046 0 0 0 36.73584168 114.85483146 651.72924547 651.72924547 0 0 0 137.87005754 137.87005754zM830.63651469 555.55188969a228.16056078 228.16056078 0 0 0-34.74413926-170.8437957l-47.35825456 47.35825456a162.87698604 162.87698604 0 0 1-221.30025298 221.30025297l-47.35825456 47.35825457a228.16056078 228.16056078 0 0 0 351.20350241-145.61556668z" p-id="2674" />
          </svg>
        </el-input>
      </el-form-item>
      <div v-if="captchaEnabled" class="validate-title">验证码</div>
      <el-form-item v-if="captchaEnabled" prop="code">
        <div style="display: flex; height: 40px;">
          <el-input
            ref="code"
            v-model.trim="form.code"
            :class="{error: !validateRes.code}"
            placeholder="请输入验证码"
            type="text"
            @focus="focusKey = 'code'"
            @keyup.enter.native="debouncedHandleLogin"
            @blur="checkFromItem('code', form.code)"
          >
            <template slot="prefix">
              <img src="../../../assets/login/verify-code.png" style="width: 17px; height: 17px; vertical-align: middle; margin-left: 5px;">
            </template>
          </el-input>
          <img :src="codeImg" class="code" alt="" @click="getCode">
        </div>
      </el-form-item>
    </el-form>

    <div class="cell login-action">
      <el-checkbox v-model="rememberMe">记住密码</el-checkbox>
      <div class="empty">&nbsp;</div>
    </div>

    <div class="control">
      <div class="btn" @click="debouncedHandleLogin">登&nbsp;录</div>
      <!-- 单位信息 -->
      <div class="host-unit" v-html="configData.unitInformation"/>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { codeAPI } from '@/api/login'
import { Loading } from 'element-ui'
import encryption from '@/utils/encryption'
import Mixins from './Mixins'
import { debounce } from 'throttle-debounce'
import menuConfig from '@/router/config'
import adminRouter from '@/router/modules/admin'
import { queryFirstConfigByName } from '@/packages/api/index'

export default {
  name: 'LoginByPwd',
  mixins: [Mixins],
  props: {
    configData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const pwdReg = /^[\da-zA-Z!@#$%^&*]{1,20}$/
    return {
      menuConfig: menuConfig,
      adminRouter: adminRouter,
      eyeOpen: true,
      redirect: undefined,
      rememberMe: false,
      form: {
        username: '',
        password: '',
        code: '',
        uuid: ''
      },
      captchaEnabled: true,
      codeImg: '',
      errorInfo: null,
      validateRes: {
        username: true,
        password: true,
        code: true
      },
      rules: {
        username: [{ required: true, message: '账号不能为空' }],
        password: [
          { required: true, message: '密码不能为空' },
          { reg: pwdReg, message: '密码由1-20位组成' }
        ],
        code: [{ required: true, message: '验证码不能为空' }]
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created() {
    this.getCode()
    this.checkRememberedCredentials()
  },
  methods: {
    // 验证记住密码
    checkRememberedCredentials() {
      const remembered = localStorage.getItem('remembered')
      if (remembered === 'true') {
        const encryptedUsername = localStorage.getItem('username')
        const encryptedPassword = localStorage.getItem('password')
        if (encryptedUsername && encryptedPassword) {
          this.form.username = encryption.decrypt(encryptedUsername)
          this.form.password = encryption.decrypt(encryptedPassword)
          this.rememberMe = true
        }
      }
      const query = this.$route.query
      if (query.type === 'demo') {
        this.form.username = 'admin'
        this.form.password = '8KXJqWsc22@'
      }
    },
    clearRememberedCredentials() {
      localStorage.removeItem('remembered')
      localStorage.removeItem('username')
      localStorage.removeItem('password')
    },
    debouncedHandleLogin() {
      debounce(300, this.handleLogin)()
    },
    // 登录
    handleLogin() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          // 如果勾选了记住密码
          if (this.rememberMe) {
            // 加密存储
            const encryptedUsername = encryption.encrypt(this.form.username)
            const encryptedPassword = encryption.encrypt(this.form.password)
            localStorage.setItem('remembered', 'true')
            localStorage.setItem('username', encryptedUsername)
            localStorage.setItem('password', encryptedPassword)
          } else {
            this.clearRememberedCredentials()
          }
          const loading = Loading.service({
            target: document.querySelector('.login-main-content')
          })
          const form = JSON.parse(JSON.stringify(this.form))
          form.password = encryption.encrypt(form.password)
          form.source = 'admin'
          this.$store.dispatch('Login', form).then(res => {
            this.$store.dispatch('getAuth').then(auths => {
              Promise.all([this.getIntroduceData()]).then((data) => {
                if (data[0] && data[0]['default']) { // 靶场介绍页是否是默认跳转页
                  this.$router.push({ name: 'home' })
                } else {
                  this.goPages(auths.manage)
                }
                loading.close()
              }).catch(() => {
                loading.close()
              })
            }).catch(() => {
              this.getCode()
              loading.close()
            })
          }).catch(() => {
            this.getCode()
            loading.close()
          })
        }
      })
    },
    goPages(manage) {
      // 有权限的业务系统列表
      const data = this.menuConfig[0].subs.filter(sub => sub.key !== 'penetration' && (sub.alwaysShow || manage.hasOwnProperty(sub.key)))
      // 如果有实训默认挑战至实训
      if (data.find(item => item.key === 'training')) {
        this.$router.push({ path: '/manage/training/overview' })
      }
      // 没有实训则跳转至业务系统第一个app
      const itemRouters = this.adminRouter.find(item => item.path === data[0].path)
      const sideItemPath = itemRouters.children.filter(item => !item.hidden)[0].path
      this.$router.push({ path: itemRouters.path + (itemRouters.path[itemRouters.path.length - 1] === '/' ? '' : '/') + sideItemPath })
    },
    // 获取靶场介绍配置数据
    getIntroduceData() {
      return new Promise((resolve, reject) => {
        queryFirstConfigByName('introduce').then(res => {
          const introduceData = res.data.value ? JSON.parse(res.data.value) : null
          resolve(introduceData)
        }).catch((error) => {
          reject(error)
        })
      })
    },
    /**
     * 获取验证码
     */
    getCode() {
      codeAPI().then(res => {
        this.captchaEnabled = res.captchaEnabled
        this.codeImg = 'data:image/jpg;base64,' + res.img
        this.form.uuid = res.uuid
      })
    },

    /**
     * 校验登录表单
     */
    checkForm() {
      this.clearError()
      const arr = this.captchaEnabled ? ['username', 'password', 'code'] : ['username', 'password']
      for (let i = 0; i < arr.length; i++) {
        const res = this.checkFromItem(arr[i], this.form[arr[i]] || null)
        if (!res) return false
      }
      return true
    },
    clearError() {
      this.errorInfo = null
      this.validateRes = {
        username: true,
        password: true,
        code: this.captchaEnabled
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import '../index';
.form-control-feedback {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 8px;
  z-index: 2;
  display: block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  pointer-events: auto;
  cursor: pointer;
}

.login-action {
  margin: 20px 0 25px 0;
  .login-by-code {
    font-size: 14px;
    color: #3e6bea;
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }

  /deep/ .el-checkbox {
    .el-checkbox__inner {
      width: 14px;
      height: 14px;
      &::after {
        top: 2px;
        left: 5px;
      }
    }
    .el-checkbox__label {
      font-size: 14px;
      font-weight: normal;
      color: #000;
    }
  }
}

.control {
  .others {
    font-size: 14px;
    .el-dropdown {
      font-size: 14px;
    }
    .register {
      cursor: pointer;
      &:hover {
        text-decoration: underline;
      }
    }
  }
  .host-unit {
    font-weight: 500;
    color: var(--color-600);
    margin-top: 30px;
    padding: 0 15%;
  }
}
.code{
  width: 134px;
  height: 100%;
}
.center-tips {
  font-size: 12px;
  color: #999;
  margin-top: 10px;
  .el-icon-warning {
    color: #f9a74e;
    font-size: 14px;
  }
}
.account-title, .psd-title, .validate-title {
  color: #000;
  font-size: 16px;
  margin-bottom: 5px;
}
</style>
