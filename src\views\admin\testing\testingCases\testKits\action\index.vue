<template>
  <div class="action-menu-wrap">
    <el-button
      v-permission="'manage.testing.testing.suite.suiteList.suiteCreate'"
      type="primary" icon="el-icon-plus" @click="handleAdd"
    >创建测试套件</el-button
    >
    <el-dropdown trigger="click" placement="bottom" @command="clickDrop">
      <el-button type="primary">
        操作
        <i class="el-icon-arrow-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-permission="'manage.testing.testing.suite.suiteList.suiteUpdate'"
          :disabled="singleDisabled" command="edit"
        >编辑</el-dropdown-item
        >
        <el-dropdown-item
          v-permission="'manage.testing.testing.suite.suiteList.suiteCopy'"
          :disabled="singleDisabled" command="copy"
        >克隆</el-dropdown-item
        >
        <el-dropdown-item
          v-permission="'manage.testing.testing.suite.suiteList.suiteDelete'"
          :disabled="multipleDisabled" command="delete"
        >删除</el-dropdown-item
        >
      </el-dropdown-menu>
    </el-dropdown>

    <!-- 创建/编辑/复制测试套件弹窗 -->
    <modal-add
      :visible.sync="addVisible"
      :title="titleMapping[actionType]"
      :edit-data="editData"
      :action-type="actionType"
      @close="closeAddDialog"
      @success="handleAddSuccess"
    />

    <!-- 中部弹窗 start -->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end -->
  </div>
</template>

<script>
import mixinsActionMenu from '@/packages/mixins/action_menu'
import module from '../config'
import ModalAdd from './modal-add.vue'
import deleteTestKit from './modal-delete.vue'

export default {
  name: 'TestCasesAction',
  components: {
    ModalAdd,
    deleteTestKit
  },
  mixins: [mixinsActionMenu],
  props: {
    moduleName: {
      type: String,
      default: ''
    },
    selectItem: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      module,
      loading: false,
      dialogVisible: false,
      addVisible: false,
      actionType: 'add', // add, edit, copy 三种操作类型
      modalAction: ['delete'], // 需要弹窗打开的操作
      titleMapping: {
        add: '创建测试套件',
        edit: '编辑',
        deleteTestKit: '删除',
        copy: '克隆'
      },
      editData: null // 用于编辑/复制的数据
    }
  },
  computed: {
    // 当前选中项
    activeItem() {
      return this.selectItem.length === 1 ? this.selectItem[0] : {}
    }
  },
  methods: {
    // 处理添加
    handleAdd() {
      this.actionType = 'add'
      const parentTable = this.$parent.$parent.$parent
      this.editData = {
        categoryId: parentTable.currentNodeId,
        categoryName: parentTable.currentNodeData.label
      }
      this.addVisible = true
    },

    // 下拉菜单点击
    clickDrop(command) {
      if (command === 'edit') {
        if (this.singleDisabled) return
        this.actionType = 'edit'
        // 设置编辑数据，clone一份避免直接修改原数据
        this.editData = JSON.parse(JSON.stringify(this.activeItem))
        this.addVisible = true
      } else if (command === 'copy') {
        if (this.singleDisabled) return
        this.actionType = 'copy'
        // 设置复制数据，clone一份避免直接修改原数据
        const copyData = JSON.parse(JSON.stringify(this.activeItem))
        this.editData = copyData
        this.addVisible = true
      } else if (command === 'delete') {
        if (this.multipleDisabled) return
        this.modalName = 'deleteTestKit'
        this.modalShow = true
      }
    },

    // 关闭添加弹窗
    closeAddDialog() {
      this.addVisible = false
      // 清空编辑数据
      this.editData = null
    },

    // 处理添加/编辑/复制成功
    handleAddSuccess(data) {
      let successMsg = '创建成功'
      if (this.actionType === 'edit') {
        successMsg = '编辑成功'
      } else if (this.actionType === 'copy') {
        successMsg = '克隆成功'
      }
      this.$message.success(successMsg)
      this.$emit('call', 'refresh')
      this.closeAddDialog()
    },

    // 确认回调
    confirmCall(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.action-menu-wrap {
  display: inline-block;
}

.selected-items {
  margin-top: 10px;
  max-height: 200px;
  overflow-y: auto;

  .el-tag {
    margin-bottom: 5px;
  }
}

.mr-5 {
  margin-right: 5px;
}
</style>
