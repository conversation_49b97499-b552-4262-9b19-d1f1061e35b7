<template>
  <div class="content-wrap-layout">
    <detail-view
      :loading="loading"
      :data="data"
      :id="id"
      :view-item="viewItem"
      mode="drawer"
      title-key="name"
    />
  </div>
</template>

<script>
import { testCaseGet } from '@/api/testing/testCase'
import detailView from '@/packages/detail-view/index'
import moduleConf from '../config'
import correlationKit from './correlationKit/index.vue'
import detailOverview from './detail_overview.vue'
import history from './history/index.vue'
import questionList from './questionList/index.vue'

export default {
  name: 'TestCaseDetail',
  components: {
    detailView,
    detailOverview,
    correlationKit,
    history,
    questionList
  },
  data() {
    return {
      moduleName: moduleConf.name,
      id: null,
      loading: false,
      data: null,
      viewItem: [
        {
          transName: '概况',
          name: 'overview',
          component: detailOverview
        },
        {
          transName: '问题清单',
          name: 'question',
          component: questionList
        },
        {
          transName: '关联套件',
          name: 'correlationKit',
          component: correlationKit
        },
        {
          transName: '历史记录',
          name: 'history',
          component: history
        }
      ]
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.params.hasOwnProperty('id') ? to.params.id : null
      const fromId = from.params.hasOwnProperty('id') ? from.params.id : null
      if (toId !== fromId) {
        this.loadBase()
      }
    }
  },
  created() {
    this.loadBase()
  },
  methods: {
    actionHandler: function(type) {
      if (type === 'refresh') {
        this.loadBase()
      }
    },
    loadBase: function() {
      this.id = this.$route.params.id
      this.getData(this.id)
    },
    // 获取测试用例详情
    getData: function(id) {
      if (id) {
        this.loading = true
        testCaseGet(id).then(res => {
          this.data = res.data
          this.loading = false
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.case-detail {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;

  .el-page-header {
    margin-bottom: 20px;
  }

  .el-tabs {
    height: calc(100% - 40px);
  }
}
</style>
