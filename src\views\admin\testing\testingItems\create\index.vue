<template>
  <create-view :loading="loading" :title="isEdit ? '编辑检测项目' : '创建检测项目'">
    <el-form slot="content" ref="form" :model="formData" :rules="rules" label-position="left" label-width="150px">
      <el-card>
        <el-form-item label="项目名称" prop="name">
          <el-input v-model.trim="formData.name" placeholder="请输入" show-word-limit />
        </el-form-item>

        <el-form-item label="关联检测申请" prop="testApplicationId">
          <el-tag
            v-if="formData.testApplicationId"
            :disable-transitions="true"
            :closable="!editDisabled && !testApplyDisabled"
            :class="(editDisabled || testApplyDisabled) ? 'tag-no-close' : ''"
            @click="drawerName = 'selectedApplication'"
            @close="closeTestApplicationId">
            {{ formData.testApplicationName }}
          </el-tag>
          <el-button v-else :disabled="editDisabled" type="ghost" @click="drawerName = 'selectedApplication'">选择检测申请</el-button>
        </el-form-item>

        <el-form-item label="检测产品" prop="productName">
          <div v-if="!isEdit || !formData.testApplicationId">
            <el-input :disabled="hasTestApplication || editDisabled" v-model.trim="formData.productName" placeholder="请输入" show-word-limit />
          </div>
          <span v-else>{{ formData.productName }}</span>
        </el-form-item>
        <el-form-item label="版本号" prop="productVersion">
          <div v-if="!isEdit || !formData.testApplicationId">
            <el-input :disabled="hasTestApplication || editDisabled" v-model.trim="formData.productVersion" placeholder="请输入" show-word-limit />
          </div>
          <span v-else>{{ formData.productVersion }}</span>
        </el-form-item>

        <el-form-item label="厂商名称" prop="vendorId">
          <div v-if="!isEdit || !formData.testApplicationId">
            <el-select :disabled="hasTestApplication || editDisabled" v-model="formData.vendorId" filterable placeholder="请选择" @change="handleManufacturerChange">
              <el-option
                v-for="item in manufacturerList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </div>
          <span v-else>{{ manufacturerList.find(item => item.id == formData.vendorId) ? manufacturerList.find(item => item.id == formData.vendorId).name : '-' }}</span>
        </el-form-item>

        <el-form-item label="厂商联系人">
          <span>{{ formData.contactPerson || '-' }}</span>
        </el-form-item>

        <el-form-item label="联系方式">
          <span>{{ formData.contactPhone || '-' }}</span>
        </el-form-item>

        <el-form-item label="测试项目负责人" prop="managerId">
          <el-tag
            v-if="formData.managerId"
            :disable-transitions="true"
            closable
            @click="drawerName = 'selectedManager'"
            @close="formData.managerId = null">
            {{ formData.managerName }}
          </el-tag>
          <el-button v-else type="ghost" @click="drawerName = 'selectedManager'">选择测试项目负责人</el-button>
        </el-form-item>

        <el-form-item label="检测流程" prop="testProcessId">
          <div v-if="!isEdit || !formData.testApplicationId">
            <el-select v-model="formData.testProcessId" :disabled="editDisabled" filterable placeholder="请选择">
              <el-option
                v-for="item in processOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <span v-else>{{ processOptions.find(item => item.value == formData.testProcessId) ? processOptions.find(item => item.value == formData.testProcessId).label : '-' }}</span>
        </el-form-item>

        <el-form-item label="计划开始日期">
          <el-date-picker
            v-model="formData.planStartDate"
            type="date"
            placeholder="开始日期"
            value-format="yyyy-MM-dd"/>
        </el-form-item>

        <el-form-item label="项目描述">
          <myEditor
            :key="contentTimer"
            :content="formData.description"
            id-prefix="create"
            width="100%"
            height="200px"
            @contentChange="contentChange"
          />
        </el-form-item>
      </el-card>

      <!-- 侧拉弹窗 start -->
      <el-drawer
        :title="titleMapping[drawerName]"
        :visible.sync="drawerShow"
        :size="drawerWidth"
        @close="drawerClose"
      >
        <transition name="el-fade-in-linear">
          <component
            :is="drawerName"
            :name="drawerName"
            @close="drawerClose"
            @call="drawerConfirmCall"
          />
        </transition>
      </el-drawer>
      <!-- 侧拉弹窗 end -->
    </el-form>

    <div slot="footer">
      <el-button type="text" @click="$router.go(-1)">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </create-view>
</template>

<script>
import { getAllVendorsAPI, getVendorContactByVendorIdAPI, queryTestProcessListAPI, testingItemsCreateAPI, testingItemsDetailAPI, testingItemsUpdateAPI } from '@/api/testing/index'
import createView from '@/packages/create-view/index'
import myEditor from '@/packages/editor/index.vue'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import validate from '@/packages/validate'
import module from '../config'
import selectedApplication from './select-application.vue'
import selectedManager from './select-manager.vue'

export default {
  name: 'TestingItemsCreate',
  components: {
    createView,
    myEditor,
    selectedApplication,
    selectedManager
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      contentTimer: `create` + new Date().getTime(),
      drawerAction: ['selectedApplication', 'selectedManager'], // 需要侧拉打开的操作
      titleMapping: {
        'selectedApplication': '关联检测申请',
        'selectedManager': '选择测试项目负责人'
      },
      loading: false,
      isEdit: false,
      statusOptions: module.statusArr,
      manufacturerList: [],
      processOptions: [],
      formData: {
        name: '',
        testApplicationId: null,
        testApplicationName: '',
        productName: '',
        productVersion: '',
        vendorId: '',
        contactPerson: '',
        contactPhone: '',
        managerId: null,
        managerName: '',
        testProcessId: '', // 默认为标准流程
        planStartDate: '',
        description: ''
      },
      rules: {
        name: [
          validate.required(),
          validate.name_64_char
        ],
        productName: [
          validate.required(),
          validate.name_64_char
        ],
        productVersion: [
          validate.required(),
          validate.name_64_char
        ],
        vendorId: [
          validate.required()
        ],
        managerId: [
          validate.required()
        ],
        testProcessId: [
          validate.required()
        ]
      },
      timer: Date.now().toString(), // 用于myEditor组件的key
      statusText: '',
      roundText: '',
      actualBeginTimeText: '',
      actualEndTimeText: '',
      createAtText: '',
      createByText: '',
      hasTestApplication: false,
      editDisabled: false,
      testApplyDisabled: false
    }
  },
  created() {
    this.getVendorList()
    this.getProcessList()
    this.isEdit = !!this.$route.params.id
    if (this.isEdit && this.$route.params.status != '4') {
      this.editDisabled = true
    }
    if (this.isEdit) {
      this.getDetail()
    }
    // 从检测申请关联检测项目跳转来
    const { testApplyId, applicationNumber, productName, version, vendorId } = this.$route.query || {}
    if (testApplyId) {
      this.testApplyDisabled = true
      this.drawerConfirmCall('confirm_application', {
        id: testApplyId,
        applicationNumber: applicationNumber || '',
        productName: productName || '',
        version: version || '',
        vendorId: vendorId || ''
      })
    }
  },
  methods: {
    contentChange(value) {
      if (this.filterHtml(value)) {
        this.formData.description = value
      } else {
        const imgStrs = value.match(/<img.*?>/g)
        if (imgStrs && imgStrs.length) { // 内容只有图片时
          this.formData.description = value
        } else {
          this.formData.description = ''
        }
      }
      this.$nextTick(() => {
        this.$refs['form'].clearValidate('description')
      })
    },
    // 过滤html代码、空格、回车 空白字符
    filterHtml(str) {
      str = str.replace(/<("[^"]*"|'[^']*'|[^'">])*>/gi, '')
      str = str.replace(/[\r\n]/g, '')
      str = str.replace(/\s/g, '')
      str = str.replace(/&nbsp;/ig, '')
      return str
    },
    closeTestApplicationId() {
      this.formData.testApplicationId = ''
      this.formData.testApplicationName = ''
      this.hasTestApplication = false
      this.formData.productName = ''
      this.formData.productVersion = ''
      this.formData.vendorId = ''
      this.formData.contactPhone = ''
      this.formData.contactPerson = ''
      this.$forceUpdate()
    },
    drawerConfirmCall(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'confirm_application') {
        this.formData.testApplicationId = data.id
        this.formData.testApplicationName = data.applicationNumber
        this.formData.vendorId = data.vendorId
        // 如果有厂商ID，获取厂商联系人信息
        if (data.vendorId) {
          // 先从厂商列表中获取基本信息
          const vendor = this.manufacturerList.find(item => item.id === data.vendorId)
          if (vendor) {
            this.formData.contactPerson = vendor.user || vendor.contactPerson || ''
            this.formData.contactPhone = vendor.phone || vendor.contactPhone || ''
          }

          // 然后通过API获取详细的联系人信息
          this.getVendorContactInfo(data.vendorId)
        }
        this.formData.productName = data.productName
        this.formData.productVersion = data.version
        this.hasTestApplication = true
        this.drawerClose()
        this.$nextTick(() => {
          this.$refs['form'].clearValidate('testApplicationId')
        })
      } else if (type === 'confirm_manager') {
        this.formData.managerId = data.userId
        this.formData.managerName = data.realname
        this.drawerClose()
        this.$nextTick(() => {
          this.$refs['form'].clearValidate('managerId')
        })
      }
    },
    handleManufacturerChange(vendorId) {
      const vendor = this.manufacturerList.find(item => item.id === vendorId)
      if (vendor) {
        // 调用API获取详细的联系人信息
        this.getVendorContactInfo(vendorId)
      }
    },

    // 获取厂商联系人信息
    getVendorContactInfo(vendorId) {
      if (!vendorId) return

      getVendorContactByVendorIdAPI(vendorId).then(res => {
        if (res.data && res.data.code === 0 && Array.isArray(res.data.data)) {
          // 查找主联系人
          const mainContact = res.data.data.find(item => item.isMain === 1)
          if (mainContact) {
            // 更新联系人信息
            this.formData.contactPerson = mainContact.contactName || ''
            this.formData.contactPhone = mainContact.contactPhone || ''
          }
        }
      }).catch(error => {
        console.error(error)
      })
    },
    getVendorList() {
      // 优先尝试获取所有厂商列表
      getAllVendorsAPI().then(res => {
        if (res.data && res.data.code === 0 && Array.isArray(res.data.data)) {
          this.manufacturerList = res.data.data.map(item => ({
            id: item.id,
            name: item.name,
            user: item.contact || '', // 联系人
            phone: item.phone || item.email || '', // 联系方式，优先使用phone，没有则用email
            email: item.email || '',
            address: item.address || '',
            status: item.status
          }))

          // 如果在编辑模式下，确保设置当前选中厂商的联系信息
          if (this.isEdit && this.formData.vendorId) {
            this.handleManufacturerChange(this.formData.vendorId)
          }
        }
      })
    },
    // 获取检测流程列表
    getProcessList() {
      const params = {
        page: 1,
        limit: 1000,
        pageType: 1, // 分页
        status: '1' // 只获取启用状态的流程
      }
      queryTestProcessListAPI(params).then(res => {
        if (res.data && res.data.code === 0 && res.data.data && Array.isArray(res.data.data.records)) {
          const processList = res.data.data.records || []
          this.processOptions = processList.map(item => ({
            label: item.processName || '',
            value: item.id
          }))
        }
      })
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          const params = { ...this.formData }

          // 选择使用创建或更新API
          let api
          if (this.isEdit) {
            api = (data) => testingItemsUpdateAPI(this.$route.params.id, data)
          } else {
            api = testingItemsCreateAPI
          }
          api(params).then(res => {
            if (res.data.code === 0) {
              this.$message.success(this.isEdit ? '编辑成功' : '创建成功')
              this.loading = false
              this.$router.go(-1)
            }
          }).catch(() => {
            this.loading = false
          })
        }
      })
    },
    getDetail() {
      this.loading = true
      const id = this.$route.params.id
      testingItemsDetailAPI(id).then(res => {
        if (res.data.code === 0) {
          // 将接口返回的数据映射到表单
          const data = res.data.data || {}
          this.formData = {
            ...this.formData,
            name: data.name || '',
            testApplicationId: data.applicationId || null,
            testApplicationName: data.applicationNo || '',
            productName: data.productName || '',
            productVersion: data.productVersion || '',
            vendorId: data.vendorId || '',
            managerId: data.managerId || null,
            managerName: data.managerName || '',
            testProcessId: data.processId,
            planStartDate: data.planBeginDate || '',
            description: data.description || ''
          }
          this.contentTimer = `create` + new Date().getTime()
          if (this.$route.params.status == '0') {
            this.hasTestApplication = false
          } else {
            if (data.applicationId) {
              this.hasTestApplication = true
            } else {
              this.hasTestApplication = false
            }
          }


          // 如果有厂商ID，获取厂商联系人信息
          if (data.vendorId) {
            // 先从厂商列表中获取基本信息
            const vendor = this.manufacturerList.find(item => item.id === data.vendorId)
            if (vendor) {
              this.formData.contactPerson = vendor.user || vendor.contactPerson || ''
              this.formData.contactPhone = vendor.phone || vendor.contactPhone || ''
            }

            // 然后通过API获取详细的联系人信息
            this.getVendorContactInfo(data.vendorId)
          }
        } else {
          this.$message.error(res.message || '获取详情失败')
        }
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.el-input {
  width: 400px;
}
.el-select {
  width: 400px;
}
.el-date-editor {
  width: 400px;
}
.w-33 {
  width: 33.3%;
}
.w-66 {
  width: 66.6%;
}
.tag-no-close {
  padding-right: 10px;
  pointer-events: none;
  cursor: default;
}
</style>
