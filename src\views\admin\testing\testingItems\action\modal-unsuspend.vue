<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-width="120px" label-position="left">
      <el-form-item label="检测项目">
        <span>{{ data[0].name }}</span>
      </el-form-item>

      <el-form-item label="状态恢复至" prop="status" class="form-item">
        <el-select v-model="formData.status" filterable placeholder="请选择">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
            <span>{{ item.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>

    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import { unsuspendProjectAPI } from '@/api/testing/index'
import { getProjectEnvAPI } from '@/api/testing/index'

export default {
  name: 'ActionUnsuspend',
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      formData: {
        status: ''
      },
      rules: {
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },
      statusOptions: [
        { label: '待测试', value: '0' },
        { label: '测试中', value: '1' },
        { label: '测试通过', value: '2' },
        { label: '测试不通过', value: '3' },
        { label: '待送审资料', value: '4' },
        { label: '待审核资料', value: '5' },
        { label: '待部署环境', value: '6' },

        { label: '取消挂起', value: '8' },
        { label: '已挂起', value: '9' }
      ],
      statusMap: {
        '0': { label: '待测试' },
        '1': { label: '测试中' },
        '2': { label: '测试通过' },
        '3': { label: '测试不通过' },
        '4': { label: '待送审资料' },
        '5': { label: '待审核资料' },
        '6': { label: '待部署环境' },

        '8': { label: '取消挂起' },
        '9': { label: '已挂起' }

      },
      envType: 0 // 0是本平台部署 1是外部部署
    }
  },
  computed: {
    // 筛选返回可操作数据
    availableArr() {
      // 只有挂起状态的项目才能取消挂起
      return this.data.filter(item => item.status === '5')
    },
    // 根据tab值和projectStatus值获取状态顺序
    statusSequence() {
      const projectStatus = this.formData.status
      if (this.envType === 0) {
        if (projectStatus === '2') {
          // envType=0, projectStatus=2: 待送审资料 -> 待审核资料 -> 待部署环境 -> 待测试 -> 测试中 -> 测试通过
          return ['4', '5', '6', '0', '1', '2']
        } else if (projectStatus === '3') {
          // envType=0, projectStatus=3: 待送审资料 -> 待审核资料 -> 待部署环境 -> 待测试 -> 测试中 -> 测试不通过
          return ['4', '5', '6', '0', '1', '3']
        } else if (projectStatus === '1') {
          // envType=0, projectStatus=1: 待送审资料 -> 待审核资料 -> 待部署环境 -> 测试中
          return ['4', '5', '6', '0', '1']
        } else {
          // 默认顺序
          return ['4', '5', '6', '0']
        }
      } else if (this.envType === 1) {
        if (projectStatus === '2') {
          // envType=1, projectStatus=2: 待送审资料 -> 待审核资料  -> 待测试 -> 测试中 -> 测试通过
          return ['4', '5', '0', '1', '2']
        } else if (projectStatus === '3') {
          // envType=1, projectStatus=3: 待送审资料 -> 待审核资料  -> 待测试 -> 测试中 -> 测试不通过
          return ['4', '5', '0', '1', '3']
        } else if (projectStatus === '1') {
          // envType=1, projectStatus=1: 待送审资料 -> 待审核资料  -> 待测试 -> 测试中
          return ['4', '5', '0', '1']
        } else {
          // 默认顺序
          return ['4', '5', '0']
        }
      } else {
        return ['4', '5']
      }
    }
  },
  mounted() {
    this.formData.status = String(this.data[0].projectStatus)

    // 获取项目环境类型
    this.getProjectEnv()
  },
  methods: {
    // 获取项目环境类型
    getProjectEnv() {
      const projectId = this.data[0].id
      if (!projectId) return
      getProjectEnvAPI(projectId).then(res => {
        if (res.data && res.data.code === 0) {
          // envType: 0:本平台部署 1:外部部署
          if (res.data.data) {
            this.envType = res.data.data.envType
          }
          // 获取当前状态
          const currentStatus = this.formData.status
          // 根据状态在序列中的位置过滤选项
          const currentIndex = this.statusSequence.indexOf(currentStatus)
          if (currentIndex > -1) {
            // 只显示序列中当前状态及之前的选项
            const allowedStatuses = this.statusSequence.slice(0, currentIndex + 1)
            this.statusOptions = this.statusOptions.filter(item => allowedStatuses.includes(item.value))
          }
        }
      })
    },
    getStatusLabel(status) {
      return this.statusMap[status] && this.statusMap[status].label || '-'
    },
    getStatusClass(status) {
      const statusClassMap = {
        '0': 'status-info', // 待测试 - 灰色
        '1': 'status-warning', // 测试中 - 橙色
        '2': 'status-success', // 测试通过 - 绿色
        '3': 'status-danger', // 测试不通过 - 红色
        '4': 'status-info', // 待送审资料 - 灰色
        '5': 'status-info', // 待审核资料 - 灰色
        '6': 'status-info', // 待部署环境 - 灰色

        '8': 'status-info', // 取消挂起 - 灰色
        '9': 'status-info' // 已挂起 - 灰色
      }
      return statusClassMap[status] || 'status-info'
    },
    close() {
      this.$emit('close')
    },
    confirm() {
      this.$refs.form.validate(valid => {
        if (!valid) return

        if (!this.data || !this.data.length) {
          this.$message.warning('没有可操作的项目')
          return
        }

        this.loading = true
        const id = this.data[0].id
        const params = {
          status: Number(this.formData.status) // 转换为数字类型
        }

        // 调用取消挂起API
        unsuspendProjectAPI(id, params).then(res => {
          if (res.data.code === 0) {
            this.$message.success('取消挂起成功')
            this.$emit('call', 'refresh')
            this.close()
          } else {
            this.$message.error(res.data.msg || '取消挂起失败')
          }
        }).finally(() => {
          this.loading = false
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-wrap {
  padding: 0 15px;
}

.form-item {
  margin-bottom: 20px;
}

::v-deep .el-form-item__label {
  color: #606266;
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-info {
  background-color: #909399;
}

.status-warning {
  background-color: #e6a23c;
}

.status-success {
  background-color: #67c23a;
}

.status-danger {
  background-color: #f56c6c;
}

.status-suspend {
  background-color: var(--color-600);
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;
}
</style>
