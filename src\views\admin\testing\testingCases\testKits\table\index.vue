<template>
  <div class="resource-table" style="padding: 0;">
    <split-pane ref="split-pane" :min-percent="minPercent" :default-percent="percent" split="vertical" @resize="resize">
      <div
        slot="paneL"
        class="collapse-transition"
        style="padding-right: 0;height: 100%;"
      >
        <div class="tree-container">
          <tree
            ref="treeRef"
            :tree-width="treeWidth"
            :i-search="false"
            :tree-data="treeData"
            :max-level="100"
            :default-props="defaultProps"
            :dropdown-items="dropdownItems"
            :default-expanded-keys="treeData.length > 0 ? [treeData[0].id] : []"
            :default-checked-keys="treeData.length > 0 ? [treeData[0].id] : []"
            :is-show-dropdown="treeDropDownControl"
            :is-show-data-count="true"
            :is-show-children-num="false"
            @currentTreeNode="currentTreeNode"
            @clearCurrentNode="clearCurrentNode"
            @setTreeNode="setTreeNode"
            @setTreeHandle="onCommandClick"
          />
        </div>
      </div>
      <div
        slot="paneR"
        ref="tableColRef"
        class="resource-table collapse-transition"
      >
        <!-- 操作区 -->
        <div class="operation-wrap">
          <div class="operation-left">
            <slot name="action" />
            <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
          </div>
          <div class="operation-right">
            <el-badge :value="searchBtnShowNum">
              <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
            </el-badge>
            <!-- 自定义表格列 -->
            <t-table-config
              v-if="!customColData.length"
              :data="columnsObj"
              :active-key-arr="columnsViewArr"
              @on-change-col="onChangeCol"
            />
          </div>
        </div>
        <div
          class="collapse-btn"
          @click="toggleCollapse"
        >
          <i
            :class="fold ? 'el-icon-caret-right' : 'el-icon-caret-left'"
          />
        </div>
        <!-- 搜索区 -->
        <t-search-box
          v-show="searchView"
          ref="searchBoxRef"
          :search-key-list="searchKeyListView"
          default-placeholder="默认搜索名称"
          @search="searchMultiple"
        />
        <!-- 列表区 -->
        <t-table-view
          ref="tableRef"
          :height="height"
          :single="single"
          :loading="loading"
          :data="tableData"
          :total="tableTotal"
          :page-size="pageSize"
          :current="pageCurrent"
          :current-row="currentRow"
          :select-item="selectItem"
          :active-pagination="activePagination"
          :nocheck="false"
          current-key="id"
          @selection-change="handleSelection"
          @row-click="handleRowClick"
          @on-select="onSelect"
          @on-current="onCurrent"
          @on-change="changePage"
          @on-sort-change="onSortChange"
          @on-page-size-change="onPageSizeChange"
        >
          <el-table-column v-for="item in columnsViewArr" :key="item" :min-width="colMinWidth" :label="columnsObj[item].title" :fixed="columnsObj[item].master ? 'left' : false" :show-overflow-tooltip="true" :sortable="columnsObj[item].sortable">
            <template slot-scope="scope">
              <span v-if="item === 'suiteName'">
                <a :href="`/testing/testingKitDetail/${scope.row.id}/overview`" @click.prevent="rowLinkClick(scope.row)">
                  {{ scope.row[item] || '-' }}
                </a>
              </span>
              <span v-else-if="item === 'caseCount'">
                {{ scope.row[item].toString() || "-" }}
              </span>
              <span v-else>
                {{ scope.row[item] || '-' }}
              </span>
            </template>
          </el-table-column>
        </t-table-view>
      </div>
    </split-pane>

    <!-- 树结构弹窗 -->
    <el-dialog
      :visible.sync="treeDiaVisible"
      :title="treeDiaTitle"
      width="500px"
      @close="treeDiaClose"
    >
      <div v-loading="treeDiaLoading" class="dialog-wrap">
        <!-- 创建/编辑表单 -->
        <el-form
          v-if="['add', 'edit'].includes(currentCommand)"
          ref="treeForm"
          :model="treeForm"
          :rules="treeRules"
          label-width="80px"
        >
          <el-form-item label="分类名称" prop="name">
            <el-input
              v-model.trim="treeForm.name"
              placeholder="请输入"
              maxlength="64"
            />
          </el-form-item>
        </el-form>

        <!-- 删除确认 -->
        <div v-if="currentCommand === 'delete'" class="delete-confirm">
          <el-alert :closable="false" type="warning">
            <div slot="title">
              <p>1.删除套件分类将同时删除其下所有子分类</p>
              <p>
                2.删除套件分类，必须先保证所选分类及其下所有子分类下的套件资源已全部移除
              </p>
            </div>
          </el-alert>
          <div class="delete-tree-view">
            <el-tree
              :data="deleteTreeData"
              :props="defaultProps"
              default-expand-all
            />
          </div>
        </div>

        <!-- 移动至 -->
        <div v-if="currentCommand === 'move'" class="move-tree-container">
          <el-alert :closable="false" type="warning">
            <div slot="title">
              <p>所选分类下的测试用例、所有子分类及其下的测试用例都将一并移动至目标分类下。</p>
            </div>
          </el-alert>
          <el-form ref="moveForm" :model="moveForm" :rules="moveRules" style="padding:0;" label-width="80px" class="move-form">
            <el-form-item label="分类" prop="targetId">
              <el-select
                ref="moveSelect"
                v-model="moveForm.targetNodeName"
                :popper-append-to-body="false"
                placeholder="请选择"
                clearable
                class="move-select"
                @clear="clearMoveTarget"
              >
                <el-option :value="moveForm.targetNodeName" class="tree-select-option" disabled>
                  <div class="tree-select-container">
                    <div class="tree-filter-fixed">
                      <el-input
                        v-model="moveTreeFilterText"
                        class="tree-filter-input"
                        size="mini"
                        placeholder="按名称搜索"
                        clearable
                      />
                    </div>
                    <div class="tree-container-scroll">
                      <el-tree
                        ref="moveTree"
                        :data="moveTreeData"
                        :props="defaultProps"
                        :filter-node-method="filterMoveNode"
                        class="move-tree"
                        node-key="id"
                        highlight-current
                        default-expand-all
                        @node-click="handleMoveNodeClick"
                      >
                        <div
                          slot-scope="{ node, data }"
                          :id="data['id']"
                          :node-data="data"
                          style="width:100%"
                        >
                          <el-tooltip
                            :content="data['label']"
                            placement="top"
                            width="200"
                          >
                            <div class="tree-info">
                              <span>{{ data['label'] }}</span>
                            </div>
                          </el-tooltip>
                        </div>
                      </el-tree>
                    </div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>

        <div class="dialog-footer">
          <el-button type="text" @click="treeDiaClose">取消</el-button>
          <el-button
            type="primary" @click="handleTreeOperation"
          >确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  caseCategoryCreate,
  caseCategoryDelete,
  caseCategoryMove,
  caseCategoryTree,
  caseCategoryUpdate,
  getSuiteList
} from '@/api/testing/testCase'
import mixinsPageTable from '@/packages/mixins/page_table'
import splitPane from '@/packages/mixins/split-pane'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import tTableView from '@/packages/table-view/index.vue'
import tree from '@/packages/tree/index.vue'
import validate from '@/packages/validate'
import module from '../config'
import { mapGetters } from 'vuex'

export default {
  name: 'TestKitsTable',
  components: {
    tree,
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable, splitPane],
  props: {
    // 自定义显示列写入Key
    customLocalKey: {
      type: String,
      default: 'testingTestKits'
    }
  },
  data() {
    return {
      moduleName: module.name,
      dropdownItems: [
        { command: 'add', label: '添加', level: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99] },
        { command: 'edit', label: '编辑', level: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100] },
        { command: 'delete', label: '删除', level: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100] },
        { command: 'move', label: '移动至', level: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100] }
      ],
      module,
      validate,
      loading: false,
      currentRow: {},
      selectionArr: [],
      activePagination: true,
      searchView: false,
      // 排序相关
      sortField: '',
      sortOrder: '',
      // 搜索项配置
      searchKeyList: [
        { key: 'suiteName', label: '名称', master: true },
        { key: 'categoryName', label: '分类' },
        { key: 'createByName', label: '创建人' },
        { key: 'createAt', label: '创建时间', type: 'time_range' }
      ],
      // 表格列配置
      columnsObj: {
        'suiteName': {
          title: '名称',
          master: true
        },
        'caseCount': {
          title: '用例数量'
        },
        'categoryName': {
          title: '分类'
        },
        'createByName': {
          title: '创建人'
        },
        'createAt': {
          title: '创建时间'
        }
      },
      // 当前显示列key表
      columnsViewArr: [
        'suiteName',
        'caseCount',
        'categoryName',
        'createByName',
        'createAt'
      ],
      tableData: [],
      // 当前选中的分类ID
      currentNodeId: '0',
      // 记录搜索条件
      searchParams: {},
      requestParams: {}, // 处理后的搜索参数
      // 树结构数据
      treeData: [],
      // 树操作相关
      treeDiaVisible: false,
      treeDiaLoading: false,
      treeDiaTitle: '',
      currentCommand: '',
      currentNodeData: null,
      treeForm: {
        name: '',
        parentId: '',
        id: ''
      },
      treeRules: {
        name: [validate.required(), validate.name_64_char]
      },
      defaultProps: {
        children: 'children',
        label: 'label',
        count: 'count'
      },
      deleteTreeData: [],
      moveTreeData: [],
      moveTreeFilterText: '',
      moveForm: {
        targetId: '',
        targetNodeName: ''
      },
      moveRules: {
        targetId: [
          { required: true, message: '必填项', trigger: 'change' }
        ]
      },
      selectedMoveNodeId: null
    }
  },
  computed: {
    ...mapGetters(['allAuth']),
    'searchBtnShowNum': function() { // 搜索项的数量
      if (this.searchView) return null
      return Object.keys(this.searchParams).length || null
    },
    'treeDropDownControl': function() {
      if (this.allAuth.manage.testing.testing.category.caseCategoryCreate || this.allAuth.manage.testing.testing.category.caseCategoryUpdate || this.allAuth.manage.testing.testing.category.caseCategoryDeleteId || this.allAuth.manage.testing.testing.category.caseCategoryMove) {
        return true
      } else {
        return false
      }
    }
  },
  watch: {
    moveTreeFilterText(val) {
      this.$refs.moveTree && this.$refs.moveTree.filter(val)
    }
  },
  async created() {
    await this.getTreeData()
    // 初始加载数据，使用树的根分类ID
    this.initDefaultTree()
  },
  methods: {
    // 处理树命令点击
    onCommandClick(event, data) {
      this.currentCommand = event
      this.currentNodeData = data

      switch (event) {
        case 'add':
          this.openAddDialog(data)
          break
        case 'edit':
          this.openEditDialog(data)
          break
        case 'delete':
          this.openDeleteDialog(data)
          break
        case 'move':
          this.openMoveDialog(data)
          break
        default:
          break
      }
    },

    // 打开添加分类弹窗
    openAddDialog(data) {
      this.treeDiaTitle = '添加分类'
      this.treeForm = {
        name: '',
        parentId: data.id
      }
      this.treeDiaVisible = true
    },

    // 打开编辑分类弹窗
    openEditDialog(data) {
      this.treeDiaTitle = '编辑分类'
      this.treeForm = {
        name: data.label,
        parentId: data.parentId,
        id: data.id
      }
      this.treeDiaVisible = true
    },

    // 打开删除分类弹窗
    openDeleteDialog(data) {
      this.treeDiaTitle = '删除分类'
      // 构建删除树视图数据
      this.deleteTreeData = [this.extractNodeAndChildren(data)]
      this.treeDiaVisible = true
    },

    // 提取节点及其子节点
    extractNodeAndChildren(node) {
      const result = {
        id: node.id,
        label: node.label
      }

      if (node.children && node.children.length > 0) {
        result.children = node.children.map(child =>
          this.extractNodeAndChildren(child)
        )
      }

      return result
    },

    // 打开移动分类弹窗
    openMoveDialog(data) {
      this.treeDiaTitle = '移动至'
      // 深拷贝树数据，排除当前节点及其子节点
      this.moveTreeData = this.filterMoveTreeData(
        JSON.parse(JSON.stringify(this.treeData)),
        data.id
      )
      this.moveForm.targetId = ''
      this.moveForm.targetNodeName = ''
      this.moveTreeFilterText = ''
      this.treeDiaVisible = true
    },

    // 过滤移动树数据，排除当前节点及其子节点
    filterMoveTreeData(treeData, excludeId) {
      return treeData.filter(node => {
        if (node.id === excludeId) {
          return false
        }

        if (node.children && node.children.length > 0) {
          node.children = this.filterMoveTreeData(node.children, excludeId)
        }

        return true
      })
    },

    // 过滤移动树节点
    filterMoveNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },

    // 处理移动树节点点击
    handleMoveNodeClick(data) {
      this.moveForm.targetId = data.id
      this.moveForm.targetNodeName = data.label
      // 关闭下拉框
      this.$nextTick(() => {
        this.$refs.moveSelect && this.$refs.moveSelect.blur()
      })
    },

    // 清除移动目标
    clearMoveTarget() {
      this.moveForm.targetId = ''
      this.moveForm.targetNodeName = ''
    },

    // 关闭树操作弹窗
    treeDiaClose() {
      this.treeDiaVisible = false
      this.currentCommand = ''
      this.treeForm = {
        name: '',
        parentId: '',
        id: ''
      }
      this.deleteTreeData = []
      this.moveTreeData = []
      this.moveForm.targetId = ''
      this.moveForm.targetNodeName = ''
      this.moveTreeFilterText = ''
    },

    // 处理树操作
    handleTreeOperation() {
      switch (this.currentCommand) {
        case 'add':
          this.handleAddCategory()
          break
        case 'edit':
          this.handleEditCategory()
          break
        case 'delete':
          this.handleDeleteCategory()
          break
        case 'move':
          this.handleMoveCategory()
          break
        default:
          break
      }
    },

    // 处理添加分类
    handleAddCategory() {
      this.$refs.treeForm.validate(async valid => {
        if (valid) {
          try {
            this.treeDiaLoading = true
            const params = {
              name: this.treeForm.name,
              parentId: this.treeForm.parentId,
              type: 2 // 套件分类类型为2
            }
            const res = await caseCategoryCreate(params)
            if (res && res.code === 0) {
              this.$message.success('添加分类成功')
              this.treeDiaClose()
              // 重新获取树结构
              await this.getTreeData()
            } else {
              this.$message.error(res.msg || '添加分类失败')
            }
          } catch (error) {
            console.error('添加分类失败:', error)
          } finally {
            this.treeDiaLoading = false
          }
        }
      })
    },

    // 处理编辑分类
    handleEditCategory() {
      this.$refs.treeForm.validate(async valid => {
        if (valid) {
          try {
            this.treeDiaLoading = true
            const params = {
              id: this.treeForm.id,
              name: this.treeForm.name,
              parentId: this.treeForm.parentId,
              type: 2 // 套件分类类型为2
            }
            const res = await caseCategoryUpdate(params)
            if (res && res.code === 0) {
              this.$message.success('编辑分类成功')
              this.treeDiaClose()
              // 重新获取树结构
              await this.getTreeData()
            } else {
              this.$message.error(res.msg || '编辑分类失败')
            }
          } catch (error) {
            console.error('编辑分类失败:', error)
          } finally {
            this.treeDiaLoading = false
          }
        }
      })
    },

    // 处理删除分类
    async handleDeleteCategory() {
      try {
        this.treeDiaLoading = true
        const res = await caseCategoryDelete(this.currentNodeData.id)
        if (res && res.code === 0) {
          this.$message.success('删除分类成功')
          this.treeDiaClose()
          // 重新获取树结构
          await this.getTreeData()
          // 重置为根分类
          if (this.treeData && this.treeData.length > 0) {
            this.currentNodeId = this.treeData[0].id
          } else {
            this.currentNodeId = '0'
          }
          this.getList()
        } else {
          this.$message.warning(res.msg || '删除分类失败')
        }
      } catch (error) {
        console.error('删除分类失败:', error)
      } finally {
        this.treeDiaLoading = false
      }
    },

    // 处理移动分类
    async handleMoveCategory() {
      this.$refs.moveForm.validate(async valid => {
        if (!valid) {
          this.$message.warning('请选择目标分类')
          return
        }

        try {
          this.treeDiaLoading = true
          const params = {
            sourceId: this.currentNodeData.id,
            targetId: this.moveForm.targetId
          }
          const res = await caseCategoryMove(params)
          if (res && res.code === 0) {
            this.$message.success('移动分类成功')
            this.treeDiaClose()
            // 重新获取树结构
            await this.getTreeData()
          } else {
            this.$message.error(res.msg || '移动分类失败')
          }
        } catch (error) {
          console.error('移动分类失败:', error)
        } finally {
          this.treeDiaLoading = false
        }
      })
    },

    // 初始化默认树选择
    initDefaultTree() {
      // 使用树的根分类ID
      if (this.treeData && this.treeData.length > 0) {
        this.currentNodeId = this.treeData[0].id
        this.currentNodeData = this.treeData[0]
      } else {
        this.currentNodeId = '0' // 如果没有树数据，使用默认值
      }
      // 加载数据
      if (this.$route.query.routeSearch && this.$route.query.moduleName && this.$route.query.moduleName === this.moduleName) {
        this.searchView = true
        const params = {
          [this.$route.query.searchKey]: this.$route.query.searchVal
        }
        this.getList(params)
      } else {
        this.getList()
      }
      // 确保根节点被选中
      this.$nextTick(() => {
        if (this.$refs.treeRef) {
          this.$refs.treeRef.setCurrentKey(this.currentNodeId)
        }
      })
    },

    // 打开搜索
    openSearch() {
      this.searchView = !this.searchView
    },

    // 切换左侧树的折叠状态
    toggleCollapse() {
      this.fold = !this.fold
      if (this.fold) {
        this.percent = this.minPercent
      } else {
        this.percent = 20
      }
    },

    // 自定义表格列
    onChangeCol(arr) {
      this.columnsViewArr = arr
    },

    // 清除当前树节点选择
    clearCurrentNode() {
      // 重置为根分类
      if (this.treeData && this.treeData.length > 0) {
        this.currentNodeId = this.treeData[0].id
      } else {
        this.currentNodeId = '0'
      }
      this.getList()
    },

    // 设置树节点
    setTreeNode(event, data) {
      // 如果需要处理树节点设置
    },

    // 当前树节点变化
    currentTreeNode(data, node) {
      if (data && data.id !== undefined) {
        this.currentNodeData = data
        this.currentNodeId = data.id
        // 保留当前搜索条件，结合树节点进行搜索
        this.getList(this.searchParams)
      }
    },

    // 转换树结构数据，将id作为nodeKey，name作为label
    transformTreeData(data) {
      if (!data || !Array.isArray(data)) return []

      return data.map(item => {
        const node = {
          nodeKey: item.id,
          label: item.name || '未命名分类',
          id: item.id,
          parentId: item.parentId,
          type: item.type,
          count: item.count || ''
        }

        if (
          item.children &&
          Array.isArray(item.children) &&
          item.children.length > 0
        ) {
          node.children = this.transformTreeData(item.children)
        }

        return node
      })
    },

    // 获取分类树结构
    async getTreeData() {
      try {
        this.loading = true
        const params = {
          type: 2 // 套件分类类型为2
        }
        const res = await caseCategoryTree(params)

        if (res && res.data) {
          // 转换树结构数据
          this.treeData = this.transformTreeData(res.data)
        } else {
          this.treeData = []
        }
      } catch (error) {
        console.error('获取分类树结构失败:', error)
        this.treeData = []
      } finally {
        this.loading = false
      }
    },

    /**
     * 获取列表数据
     * @param {Object} params - 搜索参数
     * @param {Boolean} showLoading - 是否显示加载中
     */
    async getList(params = {}, showLoading = true) {
      if (!params || Object.keys(params).length === 0) {
        params = {}
      }

      // 保存当前搜索条件
      this.searchParams = { ...params }

      // 设置loading状态
      if (showLoading) {
        this.loading = true
      }

      try {
        // 构建请求参数
        const requestParams = this.getPostData('page', 'limit')
        requestParams.pageType = 1

        // 处理名称
        if (params.suiteName) {
          requestParams.suiteName = params.suiteName
        }
        // 处理分类
        if (params.categoryName) {
          requestParams.categoryName = params.categoryName
        }

        // 处理描述
        if (params.description) {
          requestParams.description = params.description
        }

        // 处理创建人
        if (params.createByName) {
          requestParams.createByName = params.createByName
        }

        // 处理时间范围
        if (params.createAt) {
          const timeArr = params.createAt.split(',')
          if (timeArr.length === 2) {
            requestParams.createTimeStart = timeArr[0]
            requestParams.createTimeEnd = timeArr[1]
          }
        }

        // 处理排序
        if (this.sortField) {
          requestParams.sortField = this.sortField
          requestParams.sortOrder = this.sortOrder || ''
        }

        // 添加分类ID
        if (this.currentNodeId !== undefined) {
          requestParams.categoryId = this.currentNodeId
        }

        // 调用API获取数据
        const res = await getSuiteList(requestParams)
        this.requestParams = requestParams

        if (res && res.code === 0) {
          this.tableData = res.data.records || []
          this.tableTotal = res.data.total || 0
        } else {
          this.tableData = []
          this.tableTotal = 0
          this.$message.warning(res.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('获取列表数据失败:', error)
        this.tableData = []
        this.tableTotal = 0
      } finally {
        this.loading = false
      }
    },

    // 处理表格选择
    handleSelection(selection) {
      this.selectionArr = selection || []
      this.$emit('on-select', this.selectionArr)
    },

    // 处理行点击
    handleRowClick(row) {
      this.currentRow = row
      this.$emit('on-current', row)
    },

    // 处理搜索
    searchMultiple(params) {
      this.pageSize = 10
      this.pageCurrent = 1
      // 结合当前树节点和搜索条件进行搜索
      this.getList(params)
    },

    // 处理链接点击
    rowLinkClick(row) {
      // 跳转到详情页
      this.$router.push({
        name: 'testKitDetail',
        params: { id: row.id, view: 'overview' }
      })
    },

    // 刷新表格
    'refresh': async function() {
      this.$emit('refresh')
      this.selectItem = []
      if (this.single) {
        this.$emit('on-select', [])
        this.setHighlightRow(null)
      }
      await this.getTreeData()
      this.getList(this.searchParams)
    },

    // 处理排序变化
    onSortChange({ column, prop, order }) {
      // 映射排序字段
      const fieldMapping = {
        '名称': 'suiteName',
        '用例数量': 'caseCount',
        '创建人': 'createByName',
        '创建时间': 'createAt'
      }

      this.sortField = fieldMapping[column.label] || ''
      if (order === 'descending') {
        this.sortOrder = 'desc'
      } else if (order === 'ascending') {
        this.sortOrder = 'asc'
      } else {
        this.sortOrder = ''
      }
      this.getList(this.searchParams)
    },

    // 处理页面大小变化
    onPageSizeChange(pageSize) {
      this.pageSize = pageSize
      this.getList(this.searchParams)
    },

    // 处理页面变化
    changePage(page) {
      this.pageCurrent = page
      this.getList(this.searchParams)
    }
  }
}
</script>

<style lang="scss" scoped>
.resource-table {
  height: 100%;
  padding-left: 0;
  .collapse-btn {
    z-index: 200;
    position: absolute;
    top: calc(50% - 80px);
    left: -10px;
    width: 10px;
    height: 60px;
    background-color: var(--color-600);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .collapse-transition {
    transition: all 0.3s ease;
  }

  .tree-container {
    height: 100%;
    overflow-y: auto;
    border-right: 1px solid var(--neutral-300);
  }

  .operation-wrap {
    flex-shrink: 0;
  }

  .t-table-view {
    flex: 1;
    overflow: hidden;
  }
}

.success {
  color: #67c23a;
}

.warning {
  color: #e6a23c;
}

.primary {
  color: var(--color-600);
}

.danger {
  color: #f56c6c;
}

.delete-tree-view,
.move-tree-view {
  max-height: 300px;
  overflow-y: auto;
  margin-top: 15px;
  border-radius: 4px;
  ::v-deep .el-tree-node__label{
    color: #252525;
  }
  ::v-deep .el-tree-node__expand-icon{
    color: #252525;
  }
  ::v-deep .el-tree-node__expand-icon.is-leaf{
    color: transparent;
  }
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}

.move-form {
  margin-top: 15px;
}

.move-select {
  width: 100%;
}

.tree-select-option {
  padding: 0;
  height: auto;
}

.tree-select-container {
  display: flex;
  flex-direction: column;
  max-height: 255px;
}

.tree-filter-fixed {
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: #fff;
  padding: 5px;
}

.tree-filter-input {
  width: 100%;
  margin-bottom: 5px;
}

.tree-container-scroll {
  width: 372px;
  flex: 1;
  overflow-y: auto;
  padding: 0 5px;
  .move-tree {
    ::v-deep .el-tree-node__label{
      width: 300px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      font-weight: 500;
    }
    .tree-info {
      width: 300px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      font-size: 14px;
      font-weight: 500;
    }
  }
}
</style>

