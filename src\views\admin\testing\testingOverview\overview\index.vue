<template>
  <div :class="{'edit-mode': editModel}" class="overview-wrap dark scroll">
    <div class="overview-title">
      <div v-if="editModel" class="edit-toolbar">
        <div class="edit-header">
          <h3>自定义概览</h3>
          <div style="float: right;">
            <el-button type="text" @click="cancelCustomView">取消</el-button>
            <el-button type="default" @click="reloadView">恢复默认</el-button>
            <el-button type="primary" @click="saveView">保存</el-button>
          </div>
        </div>
        <div class="edit-bar">
          <i class="el-icon-warning-outline" size="16" style="color:#2d8cf0;position: relative;top: 2px;" /><span class="font-14 ml-5" style="vertical-align: middle;">您可按需添加、移除模块，以及拖动卡片进行自定义布局。</span>
          <el-button type="primary" icon="el-icon-plus" class="add-btn" @click="modalAddOpen">添加模块</el-button>
        </div>
      </div>
      <div v-else class="overview-toolbar">
        <div>
          <span>当前身份：</span>
          <el-select v-model="model" placeholder="当前身份" style="width: 140px;" @change="handleRoleChange">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
          <el-select v-model="processId" class="ml-10" placeholder="全部项目" clearable filterable @change="processIdChange">
            <el-option
              v-for="item in processListOptions"
              :key="item.id"
              :label="item.processName"
              :value="item.id"/>
          </el-select>
          <el-button class="mr-15" type="text" icon="el-icon-edit" style="float: right;color: #000;" @click="customView">自定义</el-button>
        </div>

      </div>
    </div>
    <div :class="{'edit-mode': editModel}" class="overview-content dark scroll">
      <div class="overview-view">
        <div v-if="!editModel && layout.length === 0" class="no-data-view">
          <div class="con">
            <div class="help-text">暂未添加内容模块</div>
            <el-button type="primary" icon="md-create" @click="customView">自定义概览</el-button>
          </div>
        </div>
        <grid-layout
          ref="gridlayout"
          :layout.sync="layoutReverse"
          :col-num="24"
          :row-height="15"
          :is-draggable="editModel"
          :is-resizable="editModel"
          :is-mirrored="false"
          :vertical-compact="true"
          :margin="[20, 20]"
          :use-css-transforms="false"
          @layout-updated="layoutUpdatedEvent"
        >
          <grid-item
            v-for="(item) in layoutReverse"
            :x="item.x"
            :y="item.y"
            :w="item.w"
            :h="item.h"
            :i="item.i"
            :is-resizable="false"
            :key="item.pluginConfig.pluginApiKey"
          >
            <div class="plugin-wrap">
              <a href="javascript:;" class="handler-btn delete" @click="deletePlugin(item.i)"><i class="el-icon-close" size="16"/></a>
              <component :is="item.plugin" :process-id="processId" :role-ids="roleIds" :current-role="currentRole" :data="item"/>
            </div>
          </grid-item>
        </grid-layout>
      </div>
      <el-dialog
        :visible.sync="modalAdd"
        v-model="modalAdd"
        width="30%"
        title="添加模块"
        @close="addCancel">
        <div class="modal-wrap">
          <div
            v-for="item in pluginListModal"
            :class="{'active': addPlugin === item.plugin }"
            :key="item.plugin"
            class="overview-plugin-item"
            @click="selectPlugin(item)">
            <i :class="{ 'el-icon-star-on': addPlugin === item.plugin, 'el-icon-star-off': addPlugin !== item.plugin }" class="item-radio" size="18" />
            <h3>{{ item.pluginCnName }}</h3>
            <div class="info">{{ item.pluginExplain }}</div>
            <div v-if="item.pluginConfig && addPlugin === item.plugin" class="data-config">
              <div
                v-for="configItem in item.pluginConfig"
                :key="configItem.key"
                class="data-config-item">
                <span class="data-config-item-title">{{ configItem.label }}</span>
                <el-select v-model="configItem.value" class="data-config-item-select">
                  <el-option
                    v-for="(selectItemVal, selectItemKey) in configItem.list"
                    :disabled="addedKeys.includes(selectItemKey)"
                    :value="selectItemKey"
                    :label="typeof selectItemVal === 'object' ? selectItemVal.name : selectItemVal"
                    :key="selectItemKey">{{ typeof selectItemVal === 'object' ? selectItemVal.name : selectItemVal }}</el-option>
                </el-select>
              </div>
            </div>
          </div>
          <div class="dialog-footer" style="margin: 24px -14px -24px;">
            <el-button type="text" size="large" @click="addCancel">取消</el-button>
            <el-button :disabled="addDisabled" type="primary" size="large" @click="addOk">确定</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<style lang="scss">
  .overview-wrap {
    margin: 0;
    background-color: #f0f4f9;
    height: 100%;
    display: flex;
    flex-direction: column;
    .overview-title {
      overflow: hidden;
    }
    .overview-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;
    }
    .edit-toolbar {
      .edit-bar {
        height: 44px;
        line-height: 30px;
        padding: 5px 20px 5px 25px;
        background-color: #fff;
        border-bottom: solid 1px #DBDDE0;
        .add-btn {
          font-size: 14px;
          float: right;
          span {
            vertical-align: middle;
          }
        }
      }
      .edit-header {
        background-color: #fff;
        border-bottom: solid 1px #f0f4f9;
        border-top: solid 1px #f0f4f9;
        padding: 10px 20px 10px 25px;
        h3 {
          line-height: 32px;
          display: inline-block;
        }
      }
    }
    .overview-toolbar {
      font-size: 14px;
      color: #000;
      padding: 8px 5px 8px 25px;
      background-color: #fff;
      border-bottom: solid 1px #DBDDE0;
      >span {
        display: inline-block;
        height: 32px;
        line-height: 32px;
      }
    }
    .overview-view {
      flex: 1;
      margin-top: -5px;
      position: relative;
      min-width: 1200px;
    }
    .no-data-view {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 8;
      width: 100%;
      height: 100%;
      .con {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
        text-align: center;
        .help-text {
          margin-bottom: 10px;
          font-size: 14px;
        }
      }
    }
    .plugin-wrap {
      height: 100%;
      width: 100%;
      position: relative;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0px 2px 4px 0px rgba(0,0,0,.08);
      .delete {
        position: absolute;
        right: 10px;
        top: 10px;
        z-index: 10;
        visibility: hidden;
      }
    }
    .plugin-view {
      height: 100%;
      padding: 20px;
      position: relative;
      display: flex;
      flex-direction: column;
      .plugin-title {
        font-size: 16px;
        color: #333;
        font-weight: normal;
        margin-bottom: 10px;
        white-space:nowrap;
        text-overflow:ellipsis;
      }
      .plugin-loading {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .flex-1 {
        flex: 1;
      }
      .flex-center {
        display: flex;
        justify-content: center;
        align-items: center;
        color: #909399;
        font-size: 14px;
      }
      .nodata-spin {
        background-color: rgba(255,255,255,0);
        .ivu-spin-dot {
          display: none;
        }
        .nodata-text {
          color: #999;
          font-size: 14px;
        }
      }
    }
    &.edit-mode {
      .plugin-wrap {
        &:hover {
          .delete {
            visibility: visible;
          }
        }
      }
    }
  }
  .overview-plugin-item {
    position: relative;
    width: 100%;
    padding: 12px 20px;
    border: 1px solid #DBDDE0;
    border-radius: 2px;
    margin-bottom: 10px;
    cursor: pointer;
    .item-radio {
      position: absolute;
      right: 10px;
      top: 10px;
    }
    h3 {
      color: #333;
    }
    &.active {
      border-color: var(--menu-item-active);
      box-shadow: 0 0 0 2px #E6F6FF;
    }
    &.disabled {
      pointer-events: none;
      background-color: #f7f7f7;
      .data-config {
        display: none;
      }
      h3,.info {
        color: #999;
      }
    }
    .data-config {
      margin-top: 10px;
      border-top: solid 1px #DBDDE0;
      .data-config-item {
        display: flex;
        margin-top: 10px;
        flex-flow: row wrap;
        .data-config-item-title {
          flex-grow: 0;
          font-size: 13px;
          color: #333;
          line-height: 32px;
          width: 120px;
        }
        .data-config-item-select {
          flex: 1;
        }
      }
    }
  }
  .modal-footer {
    display: flex;
    justify-content: end;
  }
</style>
<script>
import VueGridLayout from 'vue-grid-layout'
import { mapGetters } from 'vuex'
import mixinAdd from './mixin_add'
import pluginBarChart from './plugin/plugin_bar_chart.vue'
import pluginIssuesRanking from './plugin/plugin_issues_ranking.vue'
import pluginIssuesStats from './plugin/plugin_issues_stats.vue'
import pluginProgress from './plugin/plugin_progress.vue'
import pluginQuantity from './plugin/plugin_quantity.vue'
import pluginResourcesPercent from './plugin/plugin_resources_percent.vue'
import pluginTestCase from './plugin/plugin_test_case.vue'
import pluginTopN from './plugin/plugin_top_n.vue'
import pluginUnclosedIssues from './plugin/plugin_unclosed_issues.vue'
// import { getLayout, saveLayoutConfig } from '@/api/simulationTraining/overview'
import { configLayoutInfo, configLayoutSave, processList } from '@/api/testing/testingOverview'

export default {
  components: {
    GridLayout: VueGridLayout.GridLayout,
    GridItem: VueGridLayout.GridItem,
    pluginResourcesPercent,
    pluginQuantity,
    pluginTopN,
    pluginBarChart,
    pluginProgress,
    pluginUnclosedIssues,
    pluginTestCase,
    pluginIssuesStats,
    pluginIssuesRanking
  },
  mixins: [
    mixinAdd
  ],
  data() {
    return {
      currentRole: '',
      roleIds: [],
      roleMap: {
        '180162': '超级管理员',
        '181251': '检测项目负责人',
        '181252': '检测主管',
        '181253': '检测人员',
        '181254': '检测厂商'
      },
      model: '', // 新增，select绑定的当前身份
      options: [], // 新增，select下拉身份列表
      processId: '',
      processListOptions: [],
      index: null,
      editModel: false,
      // 组件注册列表
      pluginList: [
        { 'w': 8, 'h': 7,
          'plugin': 'pluginQuantity',
          'pluginCnName': '资源数量统计',
          'pluginExplain': '从不同维度展示各类资源的数量',
          'pluginConfig': {
            'type': {
              'label': '资源类型',
              'value': null,
              'list': {
                'test_project_count': '检测项目数量统计',
                'test_pass_rate': '问题数量统计',
                'test_application': '检测申请',
                'vendor_test_project': '检测项目',
                'unfinished_test_project': '未完成检测项目',
                'participate': '我参与的'
              }
            }
            // 'times': {
            //   'label': '时间范围',
            //   'value': null,
            //   'list': {
            //     'no_limit': '不限'
            //     // 'Last_month': '近1个月',
            //     // 'Last3_months': '近3个月',
            //     // 'Last6_months': '近6个月',
            //     // 'one_year': '近1年'
            //   }
            // }
          }
        },
        { 'w': 8, 'h': 14,
          'plugin': 'pluginResourcesPercent',
          'pluginCnName': '我的待办',
          'pluginExplain': '各类资源总数量及不同状态资源数量的统计信息',
          'pluginConfig': {
            'type': {
              'label': '资源类型',
              'value': null,
              'list': {
                'my_todo': '我的待办'
              }
            }
          }
        },
        { 'w': 16, 'h': 15,
          'plugin': 'pluginBarChart',
          'pluginCnName': '资源趋势',
          'pluginExplain': '从不同维度展示各类资源的趋势',
          'pluginConfig': {
            'type': {
              'label': '资源类型',
              'value': null,
              'list': {
                'testing_project_progress': { name: '在测项目进度', component: 'pluginProgress', h: 15, w: 16 },
                'unclosed_issues': { name: '未关闭问题', component: 'pluginUnclosedIssues', h: 15, w: 16 },
                'test_cases': { name: '执行测试用例', component: 'pluginTestCase', h: 10, w: 8 },
                'issue_statistics': { name: '问题统计', component: 'pluginIssuesStats', h: 10, w: 8 }
              }
            }
          }
        },
        { 'w': 8, 'h': 10,
          'plugin': 'pluginTopN',
          'pluginCnName': '资源排行',
          'pluginExplain': '从不同维度展示各类资源的Top排行',
          'pluginConfig': {
            'type': {
              'key': 'type',
              'label': '资源类型',
              'value': null,
              'list': {
                'project_issue_count_rank': { name: '项目问题数量排行', component: 'pluginIssuesRanking', h: 10, w: 8 }
              }
            }
          }
        }
      ],
      layout: [],
      oldLayout: [],
      pluginListModal: []
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    'layoutReverse': function() {
      return this.layout
    },
    'layoutIndexMax': function() {
      const out = []
      this.layout.forEach((item) => {
        out.push(item['i'])
      })
      return out.length ? Math.max(...out) : 0
    }
  },
  created() {
    this.getProcessList()
  },
  mounted() {
    if (this.userInfo && this.userInfo.roleIds.indexOf('180162') != -1) {
      this.roleIds.push('180162')
    }
    if (this.userInfo && this.userInfo.roleIds.indexOf('181252') != -1) {
      this.roleIds.push('181252')
    }
    if (this.userInfo && this.userInfo.roleIds.indexOf('181253') != -1) {
      this.roleIds.push('181253')
    }
    if (this.userInfo && this.userInfo.roleIds.indexOf('181254') != -1) {
      this.roleIds.push('181254')
    }
    if (this.userInfo && this.userInfo.roleIds.indexOf('181251') != -1) {
      this.roleIds.push('181251')
    }
    // 生成options
    this.options = this.roleIds.map(id => ({ label: this.roleMap[id] || id, value: id }))
    // 默认选中第一个
    this.model = this.roleIds[0]
    this.currentRole = this.model
    // 初始化时获取布局数据
    this.$nextTick(() => {
      this.getView()
    })
  },
  methods: {
    // 新增：select切换身份
    handleRoleChange(val) {
      this.currentRole = val
      this.getView()
    },
    // 添加根据角色过滤插件列表的方法
    filterPluginsByRole() {
      // 深拷贝原始插件列表
      let filteredPlugins = JSON.parse(JSON.stringify(this.pluginList))

      // 根据不同角色过滤插件
      if (this.currentRole === '180162') { // 超级管理员
        // 不展示我参与的、我的待办
        filteredPlugins.forEach(plugin => {
          if (plugin.plugin === 'pluginQuantity') {
            // 过滤掉我参与的
            const newList = {}
            Object.keys(plugin.pluginConfig.type.list).forEach(key => {
              if (key !== 'participate') {
                newList[key] = plugin.pluginConfig.type.list[key]
              }
            })
            plugin.pluginConfig.type.list = newList
          } else if (plugin.plugin === 'pluginResourcesPercent') {
            // 过滤掉我的待办
            const newList = {}
            Object.keys(plugin.pluginConfig.type.list).forEach(key => {
              if (key !== 'my_todo') {
                newList[key] = plugin.pluginConfig.type.list[key]
              }
            })
            plugin.pluginConfig.type.list = newList
          }
        })
      } else if (this.currentRole === '181252') { // 检测主管
        // 仅展示指定模块
        const allowedQuantityModules = ['test_project_count', 'test_pass_rate', 'unfinished_test_project', 'test_application']
        const allowedTrendModules = ['testing_project_progress', 'unclosed_issues']
        const allowedRankModules = ['project_issue_count_rank']

        filteredPlugins.forEach(plugin => {
          if (plugin.plugin === 'pluginQuantity') {
            const newList = {}
            Object.keys(plugin.pluginConfig.type.list).forEach(key => {
              if (allowedQuantityModules.includes(key)) {
                newList[key] = plugin.pluginConfig.type.list[key]
              }
            })
            plugin.pluginConfig.type.list = newList
          } else if (plugin.plugin === 'pluginBarChart') {
            const newList = {}
            Object.keys(plugin.pluginConfig.type.list).forEach(key => {
              if (allowedTrendModules.includes(key)) {
                newList[key] = plugin.pluginConfig.type.list[key]
              }
            })
            plugin.pluginConfig.type.list = newList
          } else if (plugin.plugin === 'pluginTopN') {
            const newList = {}
            Object.keys(plugin.pluginConfig.type.list).forEach(key => {
              if (allowedRankModules.includes(key)) {
                newList[key] = plugin.pluginConfig.type.list[key]
              }
            })
            plugin.pluginConfig.type.list = newList
          } else if (plugin.plugin === 'pluginResourcesPercent') {
            const newList = {}
            Object.keys(plugin.pluginConfig.type.list).forEach(key => {
              if (key === 'my_todo') {
                newList[key] = plugin.pluginConfig.type.list[key]
              }
            })
            plugin.pluginConfig.type.list = newList
          }
        })
      } else if (this.currentRole === '181253') { // 检测人员
        // 仅展示指定模块
        const allowedQuantityModules = ['test_project_count', 'test_pass_rate', 'unfinished_test_project', 'participate']
        const allowedTrendModules = ['testing_project_progress', 'unclosed_issues']

        filteredPlugins.forEach(plugin => {
          if (plugin.plugin === 'pluginQuantity') {
            const newList = {}
            Object.keys(plugin.pluginConfig.type.list).forEach(key => {
              if (allowedQuantityModules.includes(key)) {
                newList[key] = plugin.pluginConfig.type.list[key]
              }
            })
            plugin.pluginConfig.type.list = newList
          } else if (plugin.plugin === 'pluginBarChart') {
            const newList = {}
            Object.keys(plugin.pluginConfig.type.list).forEach(key => {
              if (allowedTrendModules.includes(key)) {
                newList[key] = plugin.pluginConfig.type.list[key]
              }
            })
            plugin.pluginConfig.type.list = newList
          } else if (plugin.plugin === 'pluginTopN') {
            // 检测人员不展示排行
            plugin.pluginConfig.type.list = {}
          } else if (plugin.plugin === 'pluginResourcesPercent') {
            const newList = {}
            Object.keys(plugin.pluginConfig.type.list).forEach(key => {
              if (key === 'my_todo') {
                newList[key] = plugin.pluginConfig.type.list[key]
              }
            })
            plugin.pluginConfig.type.list = newList
          }
        })
      } else if (this.currentRole === '181251') { // 检测项目负责人
        // 仅展示指定模块
        const allowedQuantityModules = ['test_project_count', 'test_pass_rate', 'unfinished_test_project', 'participate']
        const allowedTrendModules = ['testing_project_progress', 'unclosed_issues']

        filteredPlugins.forEach(plugin => {
          if (plugin.plugin === 'pluginQuantity') {
            const newList = {}
            Object.keys(plugin.pluginConfig.type.list).forEach(key => {
              if (allowedQuantityModules.includes(key)) {
                newList[key] = plugin.pluginConfig.type.list[key]
              }
            })
            plugin.pluginConfig.type.list = newList
          } else if (plugin.plugin === 'pluginBarChart') {
            const newList = {}
            Object.keys(plugin.pluginConfig.type.list).forEach(key => {
              if (allowedTrendModules.includes(key)) {
                newList[key] = plugin.pluginConfig.type.list[key]
              }
            })
            plugin.pluginConfig.type.list = newList
          } else if (plugin.plugin === 'pluginTopN') {
            // 检测人员不展示排行
            plugin.pluginConfig.type.list = {}
          } else if (plugin.plugin === 'pluginResourcesPercent') {
            const newList = {}
            Object.keys(plugin.pluginConfig.type.list).forEach(key => {
              if (key === 'my_todo') {
                newList[key] = plugin.pluginConfig.type.list[key]
              }
            })
            plugin.pluginConfig.type.list = newList
          }
        })
      } else if (this.currentRole === '181254') { // 检测厂商
        // 仅展示指定模块
        const allowedQuantityModules = ['vendor_test_project', 'unfinished_test_project']
        const allowedTrendModules = ['testing_project_progress', 'unclosed_issues']
        const allowedRankModules = ['project_issue_count_rank']

        filteredPlugins.forEach(plugin => {
          if (plugin.plugin === 'pluginQuantity') {
            const newList = {}
            Object.keys(plugin.pluginConfig.type.list).forEach(key => {
              if (allowedQuantityModules.includes(key)) {
                newList[key] = plugin.pluginConfig.type.list[key]
              }
            })
            plugin.pluginConfig.type.list = newList
          } else if (plugin.plugin === 'pluginBarChart') {
            const newList = {}
            Object.keys(plugin.pluginConfig.type.list).forEach(key => {
              if (allowedTrendModules.includes(key)) {
                newList[key] = plugin.pluginConfig.type.list[key]
              }
            })
            plugin.pluginConfig.type.list = newList
          } else if (plugin.plugin === 'pluginTopN') {
            const newList = {}
            Object.keys(plugin.pluginConfig.type.list).forEach(key => {
              if (allowedRankModules.includes(key)) {
                newList[key] = plugin.pluginConfig.type.list[key]
              }
            })
            plugin.pluginConfig.type.list = newList
          } else if (plugin.plugin === 'pluginResourcesPercent') {
            const newList = {}
            Object.keys(plugin.pluginConfig.type.list).forEach(key => {
              if (key === 'my_todo') {
                newList[key] = plugin.pluginConfig.type.list[key]
              }
            })
            plugin.pluginConfig.type.list = newList
          }
        })
      }

      // 过滤掉没有可用选项的插件
      filteredPlugins = filteredPlugins.filter(plugin => {
        return Object.keys(plugin.pluginConfig.type.list).length > 0
      })

      return filteredPlugins
    },
    updateLayoutByRole() {
      // 定义各角色的布局配置
      const layoutConfigs = {
        // 超级管理员布局：除我的待办、我参与的外，其他全展示
        '180162': [{ 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '检测项目数量统计', 'pluginApiKey': 'test_project_count', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 0, 'i': 1, 'moved': false }, { 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '问题数量统计', 'pluginApiKey': 'test_pass_rate', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 8, 'y': 7, 'i': 2, 'moved': false }, { 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '检测项目', 'pluginApiKey': 'vendor_test_project', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 8, 'y': 0, 'i': 2.5, 'moved': false }, { 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '未完成检测项目', 'pluginApiKey': 'unfinished_test_project', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 16, 'y': 0, 'i': 3, 'moved': false }, { 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '检测申请', 'pluginApiKey': 'test_application', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 7, 'i': 3.5, 'moved': false }, { 'h': 15, 'w': 16, 'plugin': 'pluginProgress', 'pluginConfig': { 'pluginName': '在测项目进度', 'pluginApiKey': 'testing_project_progress', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 14, 'i': 6, 'moved': false }, { 'h': 15, 'w': 16, 'plugin': 'pluginUnclosedIssues', 'pluginConfig': { 'pluginName': '未关闭问题', 'pluginApiKey': 'unclosed_issues', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 29, 'i': 7, 'moved': false }, { 'h': 10, 'w': 8, 'plugin': 'pluginTestCase', 'pluginConfig': { 'pluginName': '执行测试用例', 'pluginApiKey': 'test_cases', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 16, 'y': 17, 'i': 9, 'moved': false }, { 'h': 10, 'w': 8, 'plugin': 'pluginIssuesStats', 'pluginConfig': { 'pluginName': '问题统计', 'pluginApiKey': 'issue_statistics', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 16, 'y': 7, 'i': 8, 'moved': false }, { 'h': 10, 'w': 8, 'plugin': 'pluginIssuesRanking', 'pluginConfig': { 'pluginName': '项目问题数量排行', 'pluginApiKey': 'project_issue_count_rank', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 16, 'y': 27, 'i': 10, 'moved': false }],
        // 检测项目负责人布局
        '181251': [{ 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '检测项目数量统计', 'pluginApiKey': 'test_project_count', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 0, 'i': 1, 'moved': false }, { 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '问题数量统计', 'pluginApiKey': 'test_pass_rate', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 8, 'y': 0, 'i': 2, 'moved': false }, { 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '未完成检测项目', 'pluginApiKey': 'unfinished_test_project', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 7, 'i': 3, 'moved': false }, { 'h': 14, 'w': 8, 'plugin': 'pluginResourcesPercent', 'pluginConfig': { 'pluginName': '我的待办', 'pluginApiKey': 'my_todo', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 16, 'y': 0, 'i': 5, 'moved': false }, { 'h': 15, 'w': 16, 'plugin': 'pluginUnclosedIssues', 'pluginConfig': { 'pluginName': '未关闭问题', 'pluginApiKey': 'unclosed_issues', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 29, 'i': 7, 'moved': false }, { 'h': 15, 'w': 16, 'plugin': 'pluginProgress', 'pluginConfig': { 'pluginName': '在测项目进度', 'pluginApiKey': 'testing_project_progress', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 14, 'i': 6, 'moved': false }, { 'h': 10, 'w': 8, 'plugin': 'pluginIssuesStats', 'pluginConfig': { 'pluginName': '问题统计', 'pluginApiKey': 'issue_statistics', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 16, 'y': 24, 'i': 8, 'moved': false }, { 'h': 10, 'w': 8, 'plugin': 'pluginTestCase', 'pluginConfig': { 'pluginName': '执行测试用例', 'pluginApiKey': 'test_cases', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 16, 'y': 14, 'i': 9, 'moved': false }, { 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '我参与的', 'pluginApiKey': 'participate', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 8, 'y': 7, 'i': 4, 'moved': false }],
        // 检测主管布局
        '181252': [{ 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '检测项目数量统计', 'pluginApiKey': 'test_project_count', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 0, 'i': 1, 'moved': false }, { 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '问题数量统计', 'pluginApiKey': 'test_pass_rate', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 8, 'y': 0, 'i': 2, 'moved': false }, { 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '未完成检测项目', 'pluginApiKey': 'unfinished_test_project', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 8, 'y': 7, 'i': 3, 'moved': false }, { 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '检测申请', 'pluginApiKey': 'test_application', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 7, 'i': 3.5, 'moved': false }, { 'h': 20, 'w': 8, 'plugin': 'pluginResourcesPercent', 'pluginConfig': { 'pluginName': '我的待办', 'pluginApiKey': 'my_todo', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 16, 'y': 0, 'i': 5, 'moved': false }, { 'h': 15, 'w': 16, 'plugin': 'pluginUnclosedIssues', 'pluginConfig': { 'pluginName': '未关闭问题', 'pluginApiKey': 'unclosed_issues', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 29, 'i': 7, 'moved': false }, { 'h': 15, 'w': 16, 'plugin': 'pluginProgress', 'pluginConfig': { 'pluginName': '在测项目进度', 'pluginApiKey': 'testing_project_progress', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 14, 'i': 6, 'moved': false }, { 'h': 10, 'w': 8, 'plugin': 'pluginIssuesStats', 'pluginConfig': { 'pluginName': '问题统计', 'pluginApiKey': 'issue_statistics', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 16, 'y': 31.5, 'i': 8, 'moved': false }, { 'h': 10, 'w': 8, 'plugin': 'pluginIssuesRanking', 'pluginConfig': { 'pluginName': '项目问题数量排行', 'pluginApiKey': 'project_issue_count_rank', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 16, 'y': 41.5, 'i': 10, 'moved': false }, { 'h': 10, 'w': 8, 'plugin': 'pluginTestCase', 'pluginConfig': { 'pluginName': '执行测试用例', 'pluginApiKey': 'test_cases', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 16, 'y': 21.5, 'i': 9, 'moved': false }],
        // 检测人员布局
        '181253': [{ 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '检测项目数量统计', 'pluginApiKey': 'test_project_count', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 0, 'i': 1, 'moved': false }, { 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '问题数量统计', 'pluginApiKey': 'test_pass_rate', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 8, 'y': 0, 'i': 2, 'moved': false }, { 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '未完成检测项目', 'pluginApiKey': 'unfinished_test_project', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 7, 'i': 3, 'moved': false }, { 'h': 11, 'w': 8, 'plugin': 'pluginResourcesPercent', 'pluginConfig': { 'pluginName': '我的待办', 'pluginApiKey': 'my_todo', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 16, 'y': 0, 'i': 5, 'moved': false }, { 'h': 15, 'w': 16, 'plugin': 'pluginUnclosedIssues', 'pluginConfig': { 'pluginName': '未关闭问题', 'pluginApiKey': 'unclosed_issues', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 29, 'i': 7, 'moved': false }, { 'h': 15, 'w': 16, 'plugin': 'pluginProgress', 'pluginConfig': { 'pluginName': '在测项目进度', 'pluginApiKey': 'testing_project_progress', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 14, 'i': 6, 'moved': false }, { 'h': 10, 'w': 8, 'plugin': 'pluginIssuesStats', 'pluginConfig': { 'pluginName': '问题统计', 'pluginApiKey': 'issue_statistics', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 16, 'y': 24, 'i': 8, 'moved': false }, { 'h': 10, 'w': 8, 'plugin': 'pluginTestCase', 'pluginConfig': { 'pluginName': '执行测试用例', 'pluginApiKey': 'test_cases', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 16, 'y': 14, 'i': 9, 'moved': false }, { 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '我参与的', 'pluginApiKey': 'participate', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 8, 'y': 7, 'i': 4, 'moved': false }],
        // 检测厂商布局
        '181254': [{ 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '检测项目', 'pluginApiKey': 'vendor_test_project', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 0, 'i': 2.5, 'moved': false }, { 'h': 7, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '未完成检测项目', 'pluginApiKey': 'unfinished_test_project', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 8, 'y': 0, 'i': 3, 'moved': false }, { 'h': 12, 'w': 8, 'plugin': 'pluginResourcesPercent', 'pluginConfig': { 'pluginName': '我的待办', 'pluginApiKey': 'my_todo', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 16, 'y': 0, 'i': 5, 'moved': false }, { 'h': 15, 'w': 16, 'plugin': 'pluginProgress', 'pluginConfig': { 'pluginName': '在测项目进度', 'pluginApiKey': 'testing_project_progress', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 7, 'i': 6, 'moved': false }, { 'h': 15, 'w': 16, 'plugin': 'pluginUnclosedIssues', 'pluginConfig': { 'pluginName': '未关闭问题', 'pluginApiKey': 'unclosed_issues', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 0, 'y': 22, 'i': 7, 'moved': false }, { 'h': 10, 'w': 8, 'plugin': 'pluginIssuesStats', 'pluginConfig': { 'pluginName': '问题统计', 'pluginApiKey': 'issue_statistics', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 16, 'y': 14, 'i': 8, 'moved': false }, { 'h': 10, 'w': 8, 'plugin': 'pluginIssuesRanking', 'pluginConfig': { 'pluginName': '项目问题数量排行', 'pluginApiKey': 'project_issue_count_rank', 'timeName': 'undefined', 'timeApiKey': null }, 'x': 16, 'y': 24, 'i': 10, 'moved': false }]
      }

      // 返回当前角色的默认布局
      if (layoutConfigs[this.currentRole]) {
        return JSON.parse(JSON.stringify(layoutConfigs[this.currentRole]))
      } else {
        // 如果没有找到对应角色的布局，使用默认布局
        return []
      }
    },
    getProcessList() {
      processList({ processName: '' }).then(res => {
        if (res.code == 0) {
          this.processListOptions = res.data
        }
      })
    },
    processIdChange(val) {
      this.processId = val
    },
    'getView': function() {
      const roleId = this.currentRole
      configLayoutInfo({ roleId: roleId }).then(res => {
        if (res.code == 0 && res.data && res.data.layout === null) {
          this.layout = this.updateLayoutByRole()
          this.index = this.layout.length
          this.$forceUpdate()
          return
        }
        if (res.code == 0 && res.data && res.data.layout) {
          // 如果接口返回了布局数据，使用接口返回的数据
          if (res.data.layout) {
            this.layout = JSON.parse(res.data.layout)
            this.index = this.layout.length
          } else {
            this.layout = []
            this.index = 0
          }
          this.$forceUpdate()
        } else {
          // 如果接口没有返回布局数据或返回为空，不使用默认布局
          this.layout = []
          this.index = 0
          this.$forceUpdate()
        }
      }).catch(() => {
        // 发生错误时，不使用默认布局
        this.layout = []
        this.index = 0
        this.$forceUpdate()
      })
    },
    'saveView': function() {
      this.editModel = !this.editModel
      const postData = {
        roleId: this.currentRole,
        layout: JSON.stringify(this.layout)
      }
      configLayoutSave(postData)
        .then(res => {
          if (res.code == 0) {
            this.$message.success('自定义概览保存成功')
          } else {
            this.$message.error('自定义概览保存失败')
          }
        })
        .catch(() => {
          this.$message.error('自定义概览保存失败')
        })
    },
    'reloadView': function() {
      // 使用当前角色的默认布局
      this.layout = this.updateLayoutByRole()
      this.index = this.layout.length
    },
    'cancelCustomView': function() {
      this.editModel = !this.editModel
      this.layout = JSON.parse(JSON.stringify(this.oldLayout))
    },
    'customView': function() {
      this.editModel = !this.editModel
      this.oldLayout = JSON.parse(JSON.stringify(this.layout))
    },
    'layoutUpdatedEvent': function() {
    },
    'deletePlugin': function(id) {
      for (let i = 0; i <= this.layout.length; i++) {
        if (this.layout[i].i === id) {
          this.layout.splice(i, 1)
          break
        }
      }
    },
    'addPluginHandel': function(item) {
      // 处理新的组件映射关系
      if (item.pluginConfig.pluginApiKey) {
        const apiKey = item.pluginConfig.pluginApiKey
        // 处理资源趋势类组件
        const trendComponents = this.pluginList[2].pluginConfig.type.list
        if (trendComponents[apiKey] && trendComponents[apiKey].component) {
          item.plugin = trendComponents[apiKey].component
          // 不再在这里覆盖h和w值，因为在addOk方法中已经正确设置了
        }
        // 处理排行类组件
        const rankComponents = this.pluginList[3].pluginConfig.type.list
        if (rankComponents[apiKey] && rankComponents[apiKey].component) {
          item.plugin = rankComponents[apiKey].component
          // 不再在这里覆盖h和w值，因为在addOk方法中已经正确设置了
        }
      }

      this.layout.unshift(Object.assign(item, {
        x: 0,
        y: 0,
        i: this.layoutIndexMax + 1
      }))
      this.index++
    },
    filterPluginList() {
      this.pluginListModal = this.pluginList.filter(item => {
        return item.pluginConfig.type.list.some(type => {
          return type.component === item.plugin
        })
      })
    },
    selectPlugin(item) {
      this.addPlugin = item.plugin
      this.addDisabled = false
      this.addedKeys = []
      this.modalAdd = true
    }
  }
}
</script>
