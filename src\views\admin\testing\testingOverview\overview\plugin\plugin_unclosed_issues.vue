<template>
  <div class="plugin-view">
    <h3 class="plugin-title">{{ pluginTitle }}</h3>
    <div v-loading="true" v-if="loading" class="plugin-loading" />
    <el-empty v-else-if="!apiData" :image="noDataImg" :image-size="150" style="padding: 0;height: 100%;" description="暂无数据" />
    <el-empty v-else-if="apiData.issuesList.length === 0" :image="noDataImg" :image-size="150" style="padding: 0;height: 100%;" description="暂无数据" />
    <div v-else class="unclosed-issues-wrap">
      <div class="issues-header">
        <div class="total-issues">未关闭问题：{{ apiData.totalIssues || 0 }}</div>
      </div>
      <div class="issues-chart">
        <div ref="issuesChart" class="chart-container"/>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.unclosed-issues-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .issues-header {
    margin-bottom: 15px;
    .total-issues {
      font-size: 14px;
      color: #606266;
      font-weight: 500;
    }
  }

  .issues-chart {
    flex: 1;
    position: relative;
    overflow: hidden;

    .chart-container {
      width: 100%;
      height: 100%;
      min-height: 250px;
    }
  }
}
</style>

<script>
import { unclosedIssues } from '@/api/testing/testingOverview'
import * as echarts from 'echarts'
import pluginMixin from './mixin_plugin.js'

export default {
  mixins: [pluginMixin],
  props: {
    processId: {
      type: String,
      default: ''
    },
    currentRole: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      noDataImg: require('@/packages/table-view/nodata.png'),
      chart: null,
      apiData: null
    }
  },
  watch: {
    'pluginApiType': {
      handler() {
        this.$nextTick(() => {
          this.resizeChart()
        })
      }
    },
    'processId': {
      handler(val) {
        this.getData()
      },
      immediate: true
    },
    'currentRole': {
      handler(val) {
        this.getData()
      },
      immediate: true
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.apiData || !this.apiData.issuesList || !this.apiData.issuesList.length) return

      // 初始化图表
      this.chart = echarts.init(this.$refs.issuesChart)

      // 准备数据
      const labels = this.apiData.issuesList.map(item => item.projectName)
      const pendingData = this.apiData.issuesList.map(item => item.pending)
      const waitUpdateData = this.apiData.issuesList.map(item => item.waitUpdate)
      const activationData = this.apiData.issuesList.map(item => item.activation)
      const fixedData = this.apiData.issuesList.map(item => item.fixed)
      const rejectedData = this.apiData.issuesList.map(item => item.rejected)

      // 设置图表选项
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['待审核', '待更新', '激活', '已修复', '已拒绝'],
          right: 0,
          top: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '40px',
          top: '40px',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: labels,
          axisLabel: {
            formatter: function(value) {
              let res = '' // 拼接加\n返回的类目项
              const maxLength = 6 // 每项显示文字个数  数字设置几，就一行显示几个文字
              const valLength = value.length // X轴上的文字个数
              const rowN = Math.ceil(valLength / maxLength) // 需要换行的行数
              // 换行的行数大于1,
              if (rowN > 1) {
                for (let i = 0; i < rowN; i++) {
                  let temp = '' // 每次截取的字符串
                  const start = i * maxLength // 开始截取的位置
                  const end = start + maxLength // 结束截取的位置
                  temp = value.substring(start, end) + '\n'
                  res += temp // 拼接字符串
                }
                return res
              } else {
                return value
              }
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '问题数量'
        },
        dataZoom: [
          {
            type: 'slider',
            show: labels.length > 5,
            start: 0,
            end: labels.length <= 5 ? 100 : (5 / labels.length) * 100,
            xAxisIndex: [0],
            handleSize: 0,
            height: 7,
            bottom: 30,
            borderColor: '#efefef',
            fillerColor: '#c7d1da',
            borderRadius: 10,
            backgroundColor: '#efefef',
            showDataShadow: false,
            showDetail: false,
            realtime: true,
            filterMode: 'filter',
            zlevel: -10
          },
          {
            type: 'inside',
            xAxisIndex: [0],
            zoomOnMouseWheel: false,
            moveOnMouseMove: true,
            moveOnMouseWheel: true
          }
        ],
        series: [
          {
            name: '待审核',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: pendingData,
            itemStyle: {
              color: '#fac858 '
            },
            barWidth: '20%',
            barGap: '10%'
          },
          {
            name: '待更新',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: waitUpdateData,
            itemStyle: {
              color: '#73c0de'
            }
          },
          {
            name: '激活',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: activationData,
            itemStyle: {
              color: '#5470c6'
            }
          },
          {
            name: '已修复',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: fixedData,
            itemStyle: {
              color: '#91cc75'
            }
          },
          {
            name: '已拒绝',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: rejectedData,
            itemStyle: {
              color: '#ee6666'
            }
          }
        ]
      }

      // 设置图表选项
      this.chart.setOption(option)

      // 添加窗口大小变化监听器
      window.addEventListener('resize', this.resizeChart)
    },
    resizeChart() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    /**
     * 获取未关闭问题数据
     *
     * @param {boolean} hideLoading - 是否隐藏加载状态
     * @API GET /api/testing/unclosed-issues
     * @response {
     *   totalIssues: number,
     *   issuesList: Array<{
     *     projectName: string,
     *     pending: number,
     *     waitUpdate: number,
     *     activation: number,
     *     fixed: number,
     *     rejected: number
     *   }>
     * }
     */
    getData(hideLoading) {
      if (!hideLoading) {
        this.loading = true
      }
      const params = {
        processId: this.processId,
        roleId: this.currentRole
      }
      unclosedIssues(params).then(res => {
        if (res.code === 0 && res.data) {
          this.apiData = res.data
        } else {
          this.apiData = null
        }
        this.loading = false
        this.$nextTick(() => {
          this.initChart()
        })
      }).catch(() => {
        this.apiData = null
        this.loading = false
      })
    }
  }
}
</script>
