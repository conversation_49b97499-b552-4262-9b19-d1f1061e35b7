<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索项目名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column v-for="item in columnsViewArr" :key="item" :min-width="colMinWidth" :width="columnsObj[item].colWidth" :label="columnsObj[item].title" :fixed="columnsObj[item].master ? 'left' : false" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <template v-if="item === 'status'">
            <span>
              <el-badge :type="getStatusClass(scope.row[item])" is-dot/>
              {{ getStatusInfo(scope.row[item], 'label') }}
              <el-tooltip v-if="scope.row.pendingStatus === 9" transfer>
                <i
                  style="color: #E7A502; position: absolute; right: 7px; top: 11px; font-size: 16px; cursor: pointer;"
                  class="el-icon-warning-outline"
                />
                <div slot="content">
                  <div>{{ scope.row.pendingComment || '项目已挂起' }}</div>
                </div>
              </el-tooltip>
            </span>
          </template>
          <template v-else-if="item === 'name'">
            <a href="javascript:;" @click="linkEvent('testing_detail', scope.row, { id: scope.row.id, view: 'overview' })">
              {{ scope.row[item] || '-' }}
            </a>
          </template>
          <template v-else>
            {{ scope.row[item] || '-' }}
          </template>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { testingItemsQueryPageAPI, getLatestResourceApplication } from '@/api/testing/index'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      statusArr: module.statusArr,
      // 搜索配置项
      searchKeyList: [
        { key: 'name', label: '项目名称', master: true },
        { key: 'productName', label: '检测产品' },
        { key: 'productVersion', label: '版本号' },
        { key: 'statusList', label: '状态', type: 'select', valueList: [
          // 项目状态 projectStatus
          { label: '待测试', value: '0', type: 'info' },
          { label: '测试中', value: '1', type: 'warning' },
          { label: '测试通过', value: '2', type: 'success' },
          { label: '测试不通过', value: '3', type: 'danger' },
          // 项目状态 projectStatus
          { label: '待送审资料', value: '4', type: 'info' },
          { label: '待审核资料', value: '5', type: 'info' },
          { label: '待部署环境', value: '6', type: 'info' },
          // 挂起状态 pendingStatus
          { label: '已挂起', value: '9', type: 'info' }
        ] },
        { key: 'round', label: '测试轮次' },
        { key: 'vendorName', label: '厂商名称' },
        { key: 'planDate', label: '计划开始日期', format: 'yyyy-MM-dd', type: 'time_range' },
        { key: 'actualBeginTime', label: '实际开始时间', type: 'time_range' },
        { key: 'actualEndTime', label: '实际结束时间', type: 'time_range' },
        { key: 'createTime', label: '创建时间', type: 'time_range' }
      ],
      // 所有可配置显示列
      columnsObj: {
        'name': {
          title: '项目名称', master: true
        },
        'productName': {
          title: '检测产品'
        },
        'productVersion': {
          title: '版本号',
          colWidth: 100
        },
        'status': {
          title: '状态',
          colWidth: 130
        },
        'round': {
          title: '测试轮次',
          colWidth: 100
        },
        'vendorName': {
          title: '厂商名称'
        },
        'planBeginDate': {
          title: '计划开始日期',
          colWidth: 100
        },
        'actualBeginTime': {
          title: '实际开始时间',

          colWidth: 145
        },
        'actualEndTime': {
          title: '实际结束时间',
          colWidth: 145
        },
        'createAt': {
          title: '创建时间',
          colWidth: 145
        }
      },
      // 当前显示列
      columnsViewArr: [
        'name',
        'productName',
        'productVersion',
        'status',
        'round',
        'vendorName',
        'planBeginDate',
        'actualBeginTime',
        'actualEndTime',
        'createAt'
      ]

    }
  },
  methods: {
    getStatusInfo(status, key) {
      const statusItem = this.statusArr.find(item => item.value === String(status))
      if (statusItem) {
        return statusItem[key]
      }
      return key === 'label' ? '-' : 'info'
    },
    getStatusClass(status) {
      const statusMap = {
        '0': 'info', // 待测试
        '1': 'warning', // 进行中
        '2': 'success', // 通过
        '3': 'danger', // 不通过
        '4': 'info', // 待送审资料
        '5': 'info', // 待审核
        '6': 'info', // 待部署
        '8': 'info', // 取消挂起
        '9': 'info' // 挂起
      }
      return statusMap[String(status)] || 'info'
    },
    getList(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      // 使用mixins中的getPostData方法构建请求参数
      const params = this.getPostData('page', 'limit')
      // 处理特殊参数
      if (params.statusList) {
        params.statusList = params.statusList.map(Number)
      }
      if (params.planDate) {
        params.planBeginDate = params.planDate.split(',')[0]
        params.planEndDate = params.planDate.split(',')[1]
        delete params.planDate
      }
      if (params.actualBeginTime) {
        params.actualBeginTimeStart = params.actualBeginTime.split(',')[0]
        params.actualBeginTimeEnd = params.actualBeginTime.split(',')[1]
        delete params.actualBeginTime
      }
      if (params.actualEndTime) {
        params.actualEndTimeStart = params.actualEndTime.split(',')[0]
        params.actualEndTimeEnd = params.actualEndTime.split(',')[1]
        delete params.actualEndTime
      }
      if (params.createTime) {
        params.createTimeStart = params.createTime.split(',')[0]
        params.createTimeEnd = params.createTime.split(',')[1]
        delete params.createTime
      }
      // 调用API获取数据
      testingItemsQueryPageAPI(params).then(res => {
        if (res.data.code === 0) {
          const records = (res.data.data && Array.isArray(res.data.data.records)) ? res.data.data.records : []
          this.tableTotal = res.data.data && res.data.data.total ? res.data.data.total : 0
          // 项目状态projectStatus 4:待送审资料 5:待审核 6:待部署 0:待测试 1:测试中 2:测试通过 3:测试不通过
          // 是否挂起pendingStatus 8:否 9:是
          // 当pendingStatus是9时，展示挂起状态
          // 其他情况统一使用项目状态projectStatus
          // 如果有记录，获取项目状态
          if (records && records.length > 0) {
            records.forEach((record) => {
              // 处理状态显示逻辑
              if (record.pendingStatus === 9) {
              // 如果是挂起状态，设置为挂起状态码
                record.status = 9
              } else {
                record.status = record.projectStatus
              }
            })
            // 提取项目ID列表
            const projectIds = records.map(item => item.id)
            // 并行请求：1. 批量获取状态 2. 获取最新的资源申请记录
            Promise.all([
              getLatestResourceApplication(projectIds)
            ]).then(([applyRes]) => {
              // 处理资源申请记录数据
              if (applyRes.data && applyRes.data.code === 0 && applyRes.data.data) {
                records.forEach(record => {
                  record.auditStatus = 0
                  const applyData = applyRes.data.data.find(item => item.projectId === record.id)
                  if (applyData) {
                    record.auditStatus = applyData.auditStatus // 设置审核状态
                    record.applyId = applyData.id // 设置审核状态
                  }
                })
              }
              this.tableData = records
              this.tableLoading = false
              this.handleSelection()
            }).catch(() => {
              this.tableData = []
              this.tableLoading = false
              this.handleSelection()
            })
          } else {
            // 无数据时的处理
            this.tableData = []
            this.tableLoading = false
            this.handleSelection()
          }
        }
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.success {
  color: #67C23A;
}
.warning {
  color: #E6A23C;
}
.primary {
  color: var(--color-600);
}
.danger {
  color: #F56C6C;
}
</style>
