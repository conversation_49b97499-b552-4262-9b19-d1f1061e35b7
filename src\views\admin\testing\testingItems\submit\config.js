export default {
  // 虚拟机列表
  virtualColumnsObj: {
    deviceName: {
      title: '设备名称',
      master: true
    },
    workstation: {
      title: '是否为工作站',
      colWidth: 100
    },
    imageName: {
      title: '镜像名称'
    },
    cpu: {
      title: 'CPU (核)',
      colWidth: 100
    },
    memory: {
      title: '内存',
      colWidth: 100
    },
    system: {
      title: '系统盘 (GB)'
    },
    data: {
      title: '数据盘 (GB)'
    },
    handle: {
      title: '操作',
      colWidth: 120
    }
  },
  virtualColumnsViewArr: [
    'deviceName',
    'workstation',
    'imageName',
    'cpu',
    'memory',
    'system',
    'data',
    'handle'
  ],
  //  设备列表
  deviceColumnsObj: {
    index: {
      title: '序号',
      master: true,
      colWidth: 70
    },
    name: {
      title: '设备名称',
      master: true,
      showOverflowTooltip: true
    },
    target: {
      title: 'IP地址/URL',
      showOverflowTooltip: true
    },
    targetPort: {
      title: '端口号',
      showOverflowTooltip: true
    },
    deviceAccountBOList: {
      title: '用户名/密码'
    },
    remark: {
      title: '备注',
      showOverflowTooltip: true
    },
    handle: {
      title: '操作',
      colWidth: 120
    }
  },
  deviceColumnsViewArr: [
    'index',
    'name',
    'target',
    'targetPort',
    'deviceAccountBOList',
    'remark',
    'handle'
  ],
  // 附件列表
  fileColumnsObj: {
    index: {
      title: '序号',
      master: true,
      colWidth: 70
    },
    fileName: {
      title: '文件名称',
      master: true
    },
    typeName: {
      title: '关联任务'
    },
    fileSize: {
      title: '文件大小'
    },
    handle: {
      title: '操作',
      colWidth: 150
    }
  },
  fileColumnsViewArr: ['index', 'fileName', 'typeName', 'fileSize', 'handle']
}
