<template>
  <div class="plugin-view">
    <h3 class="plugin-title">{{ pluginTitle }}</h3>
    <div v-loading="true" v-if="loading" class="plugin-loading" />
    <el-empty v-else-if="!apiData" :image="noDataImg" :image-size="150" style="padding: 0;height: 100%;" description="暂无数据" />
    <el-empty v-else-if="apiData.projectList.length === 0" :image="noDataImg" :image-size="150" style="padding: 0;height: 100%;" description="暂无数据" />
    <div v-else class="test-progress-wrap">
      <div class="progress-header">
        <div class="project-count">在测项目：{{ apiData.totalProjects || 0 }}</div>
      </div>
      <div class="progress-chart">
        <div ref="progressChart" class="chart-container"/>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.test-progress-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .progress-header {
    margin-bottom: 15px;
    .project-count {
      font-size: 14px;
      color: #606266;
      font-weight: 500;
    }
  }

  .progress-chart {
    flex: 1;
    position: relative;
    overflow: hidden;

    .chart-container {
      width: 100%;
      height: 100%;
      min-height: 250px;
    }
  }
}
</style>

<script>
import { projectProgress } from '@/api/testing/testingOverview'
import * as echarts from 'echarts'
import pluginMixin from './mixin_plugin.js'

export default {
  mixins: [pluginMixin],
  props: {
    processId: {
      type: String,
      default: ''
    },
    currentRole: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      noDataImg: require('@/packages/table-view/nodata.png'),
      chart: null,
      apiData: null
    }
  },
  watch: {
    'pluginApiType': {
      handler() {
        this.$nextTick(() => {
          this.resizeChart()
        })
      }
    },
    'processId': {
      handler(val) {
        this.getData()
      },
      immediate: true
    },
    'currentRole': {
      handler(val) {
        this.getData()
      },
      immediate: true
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.apiData || !this.apiData.projectList || !this.apiData.projectList.length) return

      // 初始化图表
      this.chart = echarts.init(this.$refs.progressChart)

      // 准备数据
      const labels = this.apiData.projectList.map(item => item.taskName)
      const notStartedData = this.apiData.projectList.map(item => item.unStart)
      const inProgressData = this.apiData.projectList.map(item => item.inProgress)
      const testPassedData = this.apiData.projectList.map(item => item.testPass)
      const testFailedData = this.apiData.projectList.map(item => item.testUnPass)

      // 设置图表选项
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['未开始', '进行中', '测试通过', '测试不通过'],
          right: 0,
          top: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '40px',
          top: '40px',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: labels,
          axisLabel: {
            formatter: function(value) {
              let res = '' // 拼接加\n返回的类目项
              const maxLength = 6 // 每项显示文字个数  数字设置几，就一行显示几个文字
              const valLength = value.length // X轴上的文字个数
              const rowN = Math.ceil(valLength / maxLength) // 需要换行的行数
              // 换行的行数大于1,
              if (rowN > 1) {
                for (let i = 0; i < rowN; i++) {
                  let temp = '' // 每次截取的字符串
                  const start = i * maxLength // 开始截取的位置
                  const end = start + maxLength // 结束截取的位置
                  temp = value.substring(start, end) + '\n'
                  res += temp // 拼接字符串
                }
                return res
              } else {
                return value
              }
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '任务数量'
        },
        dataZoom: [
          {
            type: 'slider',
            show: labels.length > 5,
            start: 0,
            end: labels.length <= 5 ? 100 : (5 / labels.length) * 100,
            xAxisIndex: [0],
            handleSize: 0,
            height: 7,
            bottom: 30,
            borderColor: '#efefef',
            fillerColor: '#c7d1da',
            borderRadius: 10,
            backgroundColor: '#efefef',
            showDataShadow: false,
            showDetail: false,
            realtime: true,
            filterMode: 'filter',
            zlevel: -10
          },
          {
            type: 'inside',
            xAxisIndex: [0],
            zoomOnMouseWheel: false,
            moveOnMouseMove: true,
            moveOnMouseWheel: true
          }
        ],
        series: [
          {
            name: '未开始',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: notStartedData,
            itemStyle: {
              color: '#fac858'
            },
            barWidth: '20%',
            barGap: '10%'
          },
          {
            name: '进行中',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: inProgressData,
            itemStyle: {
              color: '#91cc75'
            }
          },
          {
            name: '测试通过',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: testPassedData,
            itemStyle: {
              color: '#73c0de'
            }
          },
          {
            name: '测试不通过',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: testFailedData,
            itemStyle: {
              color: '#ee6666'
            }
          }
        ]
      }

      // 设置图表选项
      this.chart.setOption(option)

      // 添加窗口大小变化监听器
      window.addEventListener('resize', this.resizeChart)
    },
    resizeChart() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    /**
     * 获取测试进度数据
     *
     * @param {boolean} hideLoading - 是否隐藏加载状态
     * @API GET /api/testing/progress-summary
     * @response {
     *   totalProjects: number,
     *   projectList: Array<{
     *     taskName: string,
     *     unStart: number,
     *     inProgress: number,
     *     testPass: number,
     *     testUnPass: number
     *   }>
     * }
     */
    getData(hideLoading) {
      if (!hideLoading) {
        this.loading = true
      }

      const params = {
        processId: this.processId,
        roleId: this.currentRole
      }
      projectProgress(params).then(res => {
        if (res.code === 0 && res.data) {
          this.apiData = res.data
        } else {
          this.apiData = null
        }
        this.loading = false
        this.$nextTick(() => {
          this.initChart()
        })
      }).catch(() => {
        this.apiData = null
        this.loading = false
      })
    }
  }
}
</script>
