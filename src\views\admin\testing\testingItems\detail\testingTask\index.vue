<template>
  <div class="content-wrap-layout" style="padding: 0;">
    <page-table
      ref="table"
      :default-selected-arr="defaultSelectedArr"
      :cache-pattern="true"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
    >
      <action-menu
        slot="action"
        :data="data"
        :module-name="moduleName"
        :select-item="selectItem"
        @call="actionHandler"
      />
    </page-table>
    <el-drawer
      :visible.sync="detailShow"
      :modal="false"
      size="75%"
      append-to-body
      class="detail-view"
      @close="closeDetail"
    >
      <t-detail v-if="detailShow" />
    </el-drawer>
  </div>
</template>

<script>
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
import moduleConf from './config.js'
import tDetail from '@/views/admin/testing/testingItems/detail/testingTask/detail/index.vue'
import moduleMixin from '@/packages/mixins/module_list'

export default {
  name: 'TestingTask',
  components: {
    pageTable,
    actionMenu,
    tDetail
  },
  mixins: [moduleMixin],
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      detailShowOfName: ['testingTask_detail'],
      listRouterName: 'testing_detail',
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: []
    }
  },
  computed: {
    listRouterParams() {
      return {
        id: this.$route.params.projectId,
        view: 'tasks'
      }
    }
  },
  watch: {
    '$route': function(to, from) {
      // 从测试任务的测试用例页面过来
      if (from.path.includes('testingCase') && from.name == 'testingTask_detail') {
        this.$refs['table'].getList()
      }
    }
  },
  methods: {
    // 列表点击
    linkEvent({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
          break
        case 'create':
          break
        case 'edit':
          break
        case 'delete':
          // 调用删除接口
          break
      }
    },
    refresh() {
    }
  }
}
</script>
