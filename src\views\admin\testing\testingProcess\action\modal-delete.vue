<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        <p>删除后将无法恢复，请谨慎操作。</p >
      </div>
    </el-alert>
    <batch-template
      :data="data"
      :available-data="data"
      view-key="processName"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!data.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { deleteTestProcessesApi } from '@/api/testing/testProcesses.js'
export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const postData = this.data.map(item => {
        return item.id
      })
      deleteTestProcessesApi(postData).then((res) => {
        if ([0, 200].includes(res.code)) {
          this.$message.success('删除成功')
          this.$emit('call', 'refresh')
          this.close()
        }
      }).catch(() => {
        this.close()
        this.loading = false
      })
    }
  }
}
</script>
