<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索设备名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'nodeName'">
            <a
              v-if="scope.row.nodeName"
              @click.prevent="handleDetail(scope.row, 'deviceDetail')"
            >
              {{ scope.row[item] }}
            </a>
            <span v-else>-</span>
          </span>
          <span v-else-if="item == 'status'">
            <el-badge :type="module.statusObj[scope.row[item]].type" is-dot />
            {{ module.statusObj[scope.row[item]].label || '-' }}
          </span>
          <span v-else-if="item == 'snapshotCount'">
            <a
              v-if="scope.row.snapshotCount"
              style="color: var(--color-600); cursor: pointer"
              @click.prevent="getSnapshotsList(scope.row, 'snapshotsList')"
            >
              {{ scope.row[item] }}
            </a>
            <span v-else>-</span>
          </span>
          <span v-else-if="item == 'option'">
            <el-link :disabled="!['running'].includes(scope.row.status)" :underline="false" type="primary" @click.stop="jumpToOptionPage(scope.row)">进入</el-link>
          </span>
          <span v-else-if="item == 'virtualType'">
            {{ (module.deviceTypeObj[scope.row[item]] && module.deviceTypeObj[scope.row[item]].label) || '-' }}
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          :topology-id="topologyId"
          :resource-id="resourceId"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
    <!-- 侧拉弹窗 start -->
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      :destroy-on-close="true"
      append-to-body
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          :data="selectItem"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>
    <!-- 侧拉弹窗 end -->
  </div>
</template>
<script>
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import snapshotsList from '../action/modal-snapshot.vue'
import modalResumeSnapshot from '../action/modal_resume_snapshot.vue'
import deviceDetail from '../action/modal-deviceDetail.vue'
import module from '../config.js'
import { getTopologyNodes, getSceneInfoTopo } from '@/api/testing/index'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol,
    snapshotsList,
    modalResumeSnapshot,
    deviceDetail
  },
  mixins: [mixinsPageTable, mixinsActionMenu],
  props: {
    resourceApplyFlag: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      module,
      moduleName: module.name,
      searchKeyList: [
        { key: 'nodeName', label: '设备名称', placeholder: '请输入设备名称', master: true },
        { key: 'deviceIp', label: '设备IP', placeholder: '请输入设备IP' },
        { key: 'status', label: '运行状态', type: 'radio', placeholder: '请选择运行状态', valueList: [
          { label: '运行中', value: 'running' },
          { label: '关机', value: 'shutoff' },
          { label: '处理中', value: 'pending' },
          { label: '启动中', value: 'starting' },
          { label: '错误', value: 'error' }
        ] }
      ],
      columnsObj: {
        'nodeName': {
          title: '设备名称', master: true
        },
        'deviceIp': {
          title: '设备IP'
        },
        'virtualType': {
          title: '类型'
        },
        'status': {
          title: '运行状态'
        },
        'snapshotCount': {
          title: '快照数量'
        },
        'option': {
          title: '操作'
        }
      },
      columnsViewArr: [
        'nodeName',
        'deviceIp',
        'virtualType',
        'status',
        'snapshotCount',
        'option'
      ],
      drawerAction: ['deviceDetail'], // 需要侧拉打开的操作
      // 弹窗title映射
      titleMapping: {
        'snapshotsList': '快照列表',
        'deviceDetail': '设备详情'
      },
      topologyId: '',
      resourceId: ''
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (!this.resourceApplyFlag) {
        if (showLoading) {
          this.tableLoading = true
        }
        getSceneInfoTopo(this.$route.params.projectId || this.$route.params.id).then(res => {
          const data = { ...res.data.data }
          this.topologyId = data.topologyTemplateId
          const params = this.getPostData('pageNum', 'pageSize')
          getTopologyNodes(this.topologyId, params).then((res) => {
            if (res.data.code === 0 && res.data) {
              const data = { ...res.data.data }
              this.tableData = data.records
              this.tableTotal = data.total || 0
            }
            this.tableLoading = false
          }).catch(() => {
            this.tableLoading = false
          })
        }).catch(error => {
          console.error('获取拓扑信息出错:', error)
          this.$message.error('获取拓扑信息出错')
        })
      }
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    drawerConfirmCall: function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    getSnapshotsList(row, name) {
      this.selectItem = [row]
      this.resourceId = row.id
      this.modalName = name
      this.modalWidth = '800px'
      this.modalShow = true
    },
    jumpToOptionPage: function(data) {
      this.$emit('jumpToOption', data)
    },
    handleDetail: function(row, name) {
      this.selectItem = [row]
      this.drawerName = name
      this.drawerWidth = '720px'
      this.drawerShow = true
    }
  }
}
</script>
<style lang="scss" scoped>
.resource-table {
  height: 100%;
  padding: 15px 0 0 0;
}
</style>
