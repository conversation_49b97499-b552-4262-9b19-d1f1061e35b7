<template>
  <div class="drawer-wrap">
    <div class="resource-table">
      <el-alert :closable="false" type="warning">
        <div slot="title">
          <p>仅支持选择<q>审批状态</q>为<q>已通过</q>、<q>是否确认</q>为<q>已确认</q>且未关联检测项目的检测申请。</p >
        </div>
      </el-alert>
      <!-- 操作区 -->
      <div class="operation-wrap">
        <div class="operation-left">
          <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
        </div>
        <div class="operation-right">
          <el-badge :value="searchBtnShowNum">
            <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
          </el-badge>
          <t-table-config
            :data="columnsObj"
            :active-key-arr="columnsViewArr"
            @on-change-col="onChangeCol"
          />
        </div>
      </div>
      <!-- 搜索区 -->
      <t-search-box
        v-show="searchView"
        :search-key-list="searchKeyListView"
        default-placeholder="请输入申请编号"
        @search="searchMultiple"
      />
      <!-- 列表 -->
      <t-table-view
        ref="tableView"
        :single="true"
        :loading="loading"
        :data="tableData"
        :total="tableTotal"
        :page-size="pageSize"
        :current="pageCurrent"
        @on-current="onCurrent"
        @on-change="changePage"
        @on-sort-change="onSortChange"
        @on-page-size-change="onPageSizeChange"
      >
        <el-table-column v-for="item in columnsViewArr" :key="item" :label="columnsObj[item].title" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            {{ scope.row[item] || '-' }}
          </template>
        </el-table-column>
      </t-table-view>
    </div>
    <div class="drawer-footer">
      <el-button :disabled="!selectedRow" type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script>
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { detectionApplicationFinishPage } from '@/api/testing/testingApplication'

export default {
  name: 'SelectedApplicationTesting',
  components: {
    tSearchBox,
    tTableView,
    tTableConfig
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      loading: false,
      selectedRow: null,
      tableData: [],
      tableTotal: 0,
      // 搜索配置项
      searchKeyList: [
        { key: 'applicationNumber', label: '申请编号', master: true },
        { key: 'productName', label: '检测产品' },
        { key: 'version', label: '版本号' },
        { key: 'vendorName', label: '厂商名称' },
        { key: 'createByName', label: '申请人' }
      ],
      // 所有可配置显示列
      columnsObj: {
        'applicationNumber': {
          title: '申请编号', master: true
        },
        'productName': {
          title: '检测产品'
        },
        'version': {
          title: '版本号'
        },
        'vendorName': {
          title: '厂商名称'
        },
        'createByName': {
          title: '申请人'
        }
      },
      // 当前显示列
      columnsViewArr: [
        'applicationNumber',
        'productName',
        'version',
        'vendorName',
        'createByName'
      ]
    }
  },
  created() {
    this.getList()
  },
  mounted() {

  },
  methods: {
    getList(showLoading = true) {
      if (showLoading) {
        this.loading = true
      }
      const params = this.getPostData('page', 'limit')
      params.status = '1'
      params.confirm = 1
      params.detectionProjectIsNull = 0
      detectionApplicationFinishPage(params)
        .then(res => {
          if (res.code === 0) {
            this.tableData = res.data.records
            this.tableTotal = res.data.total
            this.loading = false
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 取消
    cancel() {
      this.$emit('call', 'close')
    },
    onCurrent(row) {
      this.selectedRow = row
    },
    // 确认
    confirm() {
      if (this.selectedRow) {
        this.$emit('call', 'confirm_application', this.selectedRow)
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
