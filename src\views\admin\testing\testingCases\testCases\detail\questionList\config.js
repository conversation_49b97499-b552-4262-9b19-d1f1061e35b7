export default {
  name: 'QuestionList',
  // 问题类型映射
  issueTypeArr: [
    { label: '安全漏洞', value: 1 },
    { label: '功能问题', value: 2 },
    { label: '性能问题', value: 3 }
  ],
  // 影响程度映射
  impactLevelArr: [
    { label: '轻微', value: 1, type: 'info' },
    { label: '一般', value: 2, type: 'primary' },
    { label: '严重', value: 3, type: 'warning' },
    { label: '致命', value: 4, type: 'danger' }
  ],
  // 状态映射
  statusArr: [
    { label: '待审核', value: '0', type: 'warning' },
    { label: '激活', value: '1', type: 'danger' },
    { label: '待更新', value: '3', type: 'info' },
    { label: '已修复', value: '4', type: 'success' },
    { label: '已关闭', value: '2', type: 'info' },
    { label: '已拒绝', value: '5', type: 'danger' },
    { label: '已关闭', value: '6', type: 'info' }
  ],
  get searchStatusArr() {
    return this.statusArr.filter(item => {
      return item.value != '2'
    })
  },
  get typeStrArr() {
    return this.issueTypeArr.map(item => {
      return { label: item.label, value: String(item.value), type: item.type }
    })
  },
  get levelStrArr() {
    return this.impactLevelArr.map(item => {
      return { label: item.label, value: String(item.value), type: item.type }
    })
  },
  // 将数组转换为对象形式，方便查找
  get impactObj() {
    return this.impactLevelArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  },
  get issueTypeObj() {
    return this.issueTypeArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  },
  get statusObj() {
    return this.statusArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  },
  get typeObj() {
    return this.issueTypeArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  }
}
