export default {
  name: 'testingCase',
  // 测试状态映射
  statusArr: [
    { label: '待测试', value: '1', type: 'info' },
    { label: '测试中', value: '2', type: 'warning' },
    { label: '测试通过', value: '3', type: 'success' },
    { label: '测试不通过', value: '4', type: 'danger' }
  ],
  levelArr: [
    { label: '低', value: '1', type: 'info' },
    { label: '中', value: '2', type: 'primary' },
    { label: '高', value: '3', type: 'warning' }
  ],
  // 执行结果映射
  resultArr: [
    { label: '通过', value: '1', type: 'success' },
    { label: '失败', value: '2', type: 'danger' },
    { label: '阻塞', value: '3', type: 'warning' },
    { label: '未执行', value: '4', type: 'info' }
  ],
  // 优先级
  priorityArr: [
    { label: '低', value: '1', type: 'info' },
    { label: '中', value: '2', type: 'warning' },
    { label: '高', value: '3', type: 'danger' }
  ],
  typeArr: [
    { label: '功能测试', value: '1' },
    { label: '性能测试', value: '2' },
    { label: '安全测试', value: '3' }
  ],
  // 安全风险等级
  riskLevelArr: [
    { label: '低', value: '1', type: 'info' },
    { label: '中', value: '2', type: 'warning' },
    { label: '高', value: '3', type: 'danger' }
  ],

  get riskLevelObj() {
    return this.riskLevelArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  },
  // 将数组转换为对象形式，方便查找
  get statusObj() {
    return this.statusArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  },
  get levelObj() {
    return this.levelArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  },
  get resultObj() {
    return this.resultArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  },
  get priorityObj() {
    return this.priorityArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  },
  get typeObj() {
    return this.typeArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  }
}
