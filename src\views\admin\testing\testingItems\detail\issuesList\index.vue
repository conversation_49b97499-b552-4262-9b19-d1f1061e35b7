<template>
  <page-table
    ref="table"
    :default-selected-arr="defaultSelectedArr"
    :cache-pattern="true"
    @refresh="refresh"
    @link-event="linkEvent"
    @on-select="tabelSelect"
    @on-current="tabelCurrent"
  >
    <action-menu
      slot="action"
      :module-name="moduleName"
      :data="data"
      :detail-data="detailData"
      :select-item="selectItem"
      @call="actionHandler"
    />
  </page-table>
</template>

<script>
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
import moduleConf from './config.js'
import { testingItemsDetailAPI } from '@/api/testing/index'

export default {
  name: 'IssuesList',
  components: {
    pageTable,
    actionMenu
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      detailData: {}
    }
  },
  created() {
    this.getData()
  },
  methods: {
    // 根据id获取详情数据
    'getData': function() {
      this.loading = true
      this.id = this.$route.name === 'testingTask_detail' ? this.$route.params.projectId : this.$route.params.id
      testingItemsDetailAPI(this.id).then(res => {
        if (res.data && res.data.code === 0) {
          this.detailData = res.data.data
        } else {
          this.$message.error(res.data.msg || '获取项目详情失败')
        }
        this.loading = false
      })
    },
    // 列表点击
    linkEvent({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
          break
        case 'create':
          break
        case 'edit':
          break
        case 'delete':
          // 调用删除接口
          break
      }
    },
    refresh() {
    }
  }
}
</script>
