<template>
  <div class="buttons-wrap">
    <el-button v-permission="'manage.testing.project.projectDetail.projectAttachment.attachmentCreate'" :disabled="data.pendingStatus == 9" style="margin-right: 5px;" type="primary" @click="clickDrop('upload')">上传附件</el-button>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          :process-tasks="processTasks"
          :active-task-id="activeTaskId"
          :project-id="projectId"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import upload from './upload.vue'

export default {
  components: {
    upload
  },
  mixins: [mixinsActionMenu],
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    selectItem: {
      type: Array,
      default: () => []
    },
    processTasks: {
      type: Array,
      default: () => []
    },
    activeTaskId: {
      type: [Number, String]
    }
  },
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'upload': '上传附件'
      },
      projectId: '' // 项目ID
    }
  },
  created() {
    // 获取项目ID
    this.projectId = this.$parent.projectId
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh' || type === 'upload') {
        this.$emit('call', type)
      } else if (type === 'changeTask') {
        this.$emit('call', type, data)
      }
    },
    'clickDrop': function(name) {
      if (name === 'upload') {
        // 显示上传附件弹窗
        this.modalName = name
      } else {
        // 其他操作可能需要弹窗
        this.modalName = name
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.buttons-wrap {
  display: flex;
  align-items: center;
}
</style>
