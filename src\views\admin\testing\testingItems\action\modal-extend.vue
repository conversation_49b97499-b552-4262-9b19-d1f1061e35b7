<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-width="110px" label-position="left">
      <el-form-item label="检测项目">
        <span>{{ data[0].name || '-' }}</span>
      </el-form-item>
      <el-form-item label="延期事项" prop="delayItem">
        <el-radio-group v-model="formData.delayItem" size="small">
          <el-radio-button label="0">提交资料</el-radio-button>
          <el-radio-button label="1">部署环境</el-radio-button>
          <el-radio-button label="2">整改复测</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="预计延期至" prop="delayAt">
        <el-date-picker
          v-model="formData.delayAt"
          :picker-options="datePickerOptions"
          type="date"
          placeholder="请选择日期"
          value-format="yyyy-MM-dd"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="延期理由" prop="reason">
        <el-input
          v-model.trim="formData.reason"
          :rows="4"
          type="textarea"
          placeholder="请输入"
        />
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import { delayApplyAPI } from '@/api/testing/index'
import validate from '@/packages/validate'
export default {
  name: 'ActionExtend',
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      validate,
      loading: false,
      formData: {
        delayItem: '0',
        delayAt: '',
        reason: ''
      },
      rules: {
        delayItem: [
          { required: true, message: '请选择延期事项', trigger: 'change' }
        ],
        reason: [
          validate.required(),
          { validator: (rule, value, callback) => {
            if (value && value.length > 255) {
              callback(new Error('1-255个字符'))
            } else {
              callback()
            }
          }, trigger: 'blur' }
        ]
      },
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() <= (Date.now() - 24 * 60 * 60 * 1000)
        }
      }
    }
  },
  computed: {
    // 筛选返回可操作数据
    availableArr() {
      // 只有进行中的项目才能申请延期
      return this.data.filter(item => item.status === '2')
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm() {
      this.$refs.form.validate(valid => {
        if (!valid) return

        if (!this.data || !this.data.length) {
          this.$message.warning('没有可操作的项目')
          return
        }

        this.loading = true
        const id = this.data[0].id
        const params = {
          delayItem: this.formData.delayItem,
          delayAt: this.formData.delayAt,
          delayReason: this.formData.reason
        }

        // 调用API
        delayApplyAPI(id, params).then(res => {
          if (res.data.code === 0) {
            this.$message.success('申请延期成功')
            this.$emit('call', 'refresh')
            this.close()
          } else {
            this.$message.error(res.data.msg || '申请延期失败')
          }
        }).finally(() => {
          this.loading = false
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-form {
  font-size: 14px;
  .el-radio-button__inner {
    font-size: 14px;
  }
}
</style>
