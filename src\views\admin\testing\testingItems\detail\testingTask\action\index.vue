<template>
  <div class="buttons-wrap">
    <!-- 检测人员才显示 -->
    <div v-if="roleIds.includes(181253) && !manage.testing.project.projectDetail.projectTask.taskAssign" style="display: inline-block;">
      <el-button :disabled="!testerButtonDisabled || (singleDisabled || selectItem[0].status == 9 || data.pendingStatus == 9 || disabledOfDetailPage)" type="primary" @click="clickDrop('caseTesting')">测试用例</el-button>
      <el-button :disabled="!testerButtonDisabled || (singleDisabled || selectItem[0].status == 9 || data.pendingStatus == 9 || disabledOfDetailPage)" type="primary" @click="clickDrop('testingReport')">测试报告</el-button>
      <el-button :disabled="!testerButtonDisabled || (singleDisabled || selectItem[0].status == 0 || selectItem[0].status == 9 || data.pendingStatus == 9 || disabledOfDetailPage)" type="primary" @click="clickDrop('createQuestion')">提交问题</el-button>
    </div>
    <el-button
      v-permission="'manage.testing.project.projectDetail.projectTask.taskAssign'"
      :disabled="singleDisabled || (selectItem[0].status == 2 || selectItem[0].status == 3 || selectItem[0].status == 9) || data.pendingStatus == 9 || disabledOfDetailPage"
      type="primary"
      @click="clickDrop('assignPersonnel')">分配人员</el-button>
    <el-button
      v-permission="'manage.testing.project.projectDetail.projectTask.taskStart'"
      :disabled="singleDisabled || selectItem[0].status != 0 || !startTestDisabled || data.pendingStatus == 9 || disabledOfDetailPage"
      type="primary"
      @click="clickDrop('starTesting')">开始测试</el-button>
    <el-dropdown v-if="manage.testing.project.projectDetail.projectTask.taskAssign" trigger="click" placement="bottom-start" @command="clickDrop">
      <el-button :disabled="disabledOfDetailPage" type="primary">
        操作<i class="el-icon-arrow-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-permission="'manage.testing.project.projectDetail.projectTask.taskOver'"
          :disabled="singleDisabled || selectItem[0].status != 1 || selectItem[0].status == 9 || data.pendingStatus == 9"
          command="endTesting">结束测试</el-dropdown-item>
        <el-dropdown-item :disabled="singleDisabled || selectItem[0].status == 9 || data.pendingStatus == 9" command="caseTesting">测试用例</el-dropdown-item>
        <el-dropdown-item :disabled="singleDisabled || selectItem[0].status == 9 || data.pendingStatus == 9" command="testingReport">测试报告</el-dropdown-item>
        <el-dropdown-item :disabled="singleDisabled || selectItem[0].status == 0 || selectItem[0].status == 9 || data.pendingStatus == 9" command="createQuestion">提交问题</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 弹窗 -->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      destroy-on-close
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import assignPersonnel from './modal-assignPersonnel.vue'
import starTesting from './modal-starTesting.vue'
import endTesting from './modal-endTesting.vue'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import { getEnvType, testingItemsDetailAPI } from '@/api/testing/index'
export default {
  name: 'ActionMenu',
  components: {
    assignPersonnel,
    starTesting,
    endTesting
  },
  mixins: [mixinsActionMenu],
  props: {
    moduleName: {
      type: String,
      default: ''
    },
    selectItem: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      disabledOfDetailPage: false,
      data: null,
      // 弹窗title映射
      titleMapping: {
        'assignPersonnel': '分配人员',
        'starTesting': '开始测试',
        'endTesting': '结束测试'
      },
      envDeployData: null,
      roleIds: [],
      userInfo: JSON.parse(localStorage.getItem('loginUserInfo')) || {}
    }
  },
  computed: {
    ...mapGetters(['manage', 'userInfo']),
    // 分配人员、开始测试按钮限制
    startTestDisabled() {
      if (!this.envDeployData) return false
      if (this.envDeployData.envType == '0') {
        // 本平台部署，需厂商设为部署完成，且项目状态为“待测试”/“测试中”
        return (
          [1, 0].includes(this.data.projectStatus) &&
          this.envDeployData.deployed == '1'
        )
      }
      if (this.envDeployData.envType == '1') {
        // 外部部署
        return true
      }
    },
    // 检测人员测试用例、测试报告、提交问题限制条件
    testerButtonDisabled() {
      const ids = JSON.parse(this.userInfo.roleIds)
      // 只有181253检测人员角色
      const isOnlyTester = Array.isArray(ids) && ids.length === 1 && ids[0] === 181253
      // 勾选的任务是否包含当前登录人
      const isContainLoginPerson = (this.selectItem && this.selectItem.length > 0 && this.selectItem[0].testerList) ? this.selectItem[0].testerList.some(item => item.testerId == this.userInfo.userId) : false
      return isOnlyTester && isContainLoginPerson
    }
  },
  watch: {
    '$route': {
      handler(newVal) {
        if (newVal.name == 'testingTask_detail') {
          this.disabledOfDetailPage = true
        } else {
          this.disabledOfDetailPage = false
        }
      }
    }
  },
  created() {
    // 检测项目负责人：181251，检测主管：181252，检测人员：181253，检测厂商：181254
    this.roleIds = this.userInfo.roleIds ? JSON.parse(this.userInfo.roleIds) : []
    this.getProjectDetail()
    this.getProjectEnvType()
  },
  methods: {
    'getProjectDetail': function() {
      this.id = this.$route.params.id
      testingItemsDetailAPI(this.id).then(res => {
        if (res.data && res.data.code === 0) {
          this.data = res.data.data
        }
        this.loading = false
      })
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    'clickDrop': function(name) {
      if (name == 'caseTesting') {
        this.$router.push({
          name: 'testingTask_detail',
          params: {
            id: this.selectItem[0].id,
            projectId: this.$route.params.id,
            view: 'testingCase'
          }
        })
      } else if (name == 'assignPersonnel') {
        if (this.envDeployData.envType == '0' && !this.startTestDisabled) {
          this.$message.error('环境部署完毕后才可进行分配人员')
          return
        }
        this.modalName = name
      } else if (name == 'testingReport') {
        this.$router.push({
          name: 'testingTask_detail',
          params: {
            id: this.selectItem[0].id,
            projectId: this.$route.params.id,
            view: 'testingReport'
          }
        })
      } else if (name == 'createQuestion') {
        const params = {}
        params.taskId = this.selectItem[0].id
        params.projectId = this.$route.params.id
        params.type = this.$route.name
        const url = this.$router.resolve({
          name: 'createTestingQuestion',
          query: params
        }).href
        window.open(url, '_blank')
      } else {
        this.modalName = name
      }
    },
    // 获取项目测试环境
    getProjectEnvType() {
      this.id = this.$route.name === 'testingTask_detail' ? this.$route.params.projectId : this.$route.params.id
      getEnvType(this.id).then(res => {
        const data = { ...res.data.data }
        this.$set(this, 'envDeployData', data)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.action-menu {
  display: inline-block;
}
</style>
