<template>
  <div v-loading="loading" class="dialog-wrap">
    <!-- 表单内容 -->
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="快照名称" prop="name">
        <el-input v-model.trim="form.name" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input
          v-model.trim="form.description"
          :autosize="false"
          :rows="3"
          type="textarea"
          style="width: 100%"
          maxlength="255"
          placeholder="请输入"
        />
      </el-form-item>
    </el-form>
    <batch-template
      :data="data"
      :available-data="availableData"
      view-key="nodeName"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!availableData.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import validate from '@/packages/validate/index'
import { createSnapshotApi } from '@/api/testing/index.js'
import { createSnapshot } from '@/packages/topo/api/orchestration'
export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    // virtual: 虚拟机  network: 网络设备
    envType: {
      type: String,
      default: 'network'
    }
  },
  data() {
    return {
      loading: false,
      form: {
        name: '',
        description: ''
      },
      rules: {
        name: [validate.required(), validate.name_64_char]
      }
    }
  },
  computed: {
    availableData: function() {
      const tempArr = this.data.filter((item) => {
        return ['running', 'shutoff'].includes(item.status)
      })
      return tempArr
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true
          // 网络编排
          if (this.envType == 'network') {
            const result = this.availableData.map(item => {
              return createSnapshot(item.id, this.form)
            })
            Promise.all(result).then(res => {
              if (result.length === res.length) {
                this.$message.success('创建成功')
                this.$emit('call', 'refresh')
                this.close()
              }
            }).finally(() => {
              this.loading = false
            })
          }
          // 虚拟机类型
          if (this.envType == 'virtual') {
            const result = this.availableData.map(item => {
              return createSnapshotApi({ vmId: item.id, ...this.form })
            })
            Promise.all(result).then(res => {
              if (result.length === res.length) {
                this.$message.success('创建成功')
                this.$emit('call', 'refresh')
                this.close()
              }
            }).finally(() => {
              this.loading = false
            })
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-form {
  padding: 0;
}
</style>
