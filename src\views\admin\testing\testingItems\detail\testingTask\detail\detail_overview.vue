<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="12">
        <detail-card title="基本信息">
          <el-form slot="content" label-position="left" label-width="120px">
            <el-form-item label="任务名称：">{{ data.name || '-' }}</el-form-item>
            <el-form-item label="所属项目：">
              <a
                v-if="data.projectName"
                :href="`/testing/testing/detail/${data.projectId}/overview`"
                target="_blank"
              >
                {{ data.projectName }}
              </a>
              <span v-else>-</span>
            </el-form-item>
            <el-form-item label="测试轮次：">{{ data.round || '-' }}</el-form-item>
            <el-form-item label="状态：">
              <el-badge :type="module.statusObj[data.testStatus] ? module.statusObj[data.testStatus].type : 'info'" is-dot />
              {{ module.statusObj[data.testStatus] ? module.statusObj[data.testStatus].label : '-' }}
            </el-form-item>
            <el-form-item label="开始时间：">{{ data.beginTime || '-' }}</el-form-item>
            <el-form-item label="结束时间：">{{ data.endTime || '-' }}</el-form-item>
            <el-form-item label="创建人：">{{ data.createByName || '-' }}</el-form-item>
            <el-form-item label="创建时间：">{{ data.createAt || '-' }}</el-form-item>
            <el-form-item label="最后修改人：">{{ data.updateByName || '-' }}</el-form-item>
            <el-form-item label="最后修改时间：">{{ data.updateAt || '-' }}</el-form-item>
          </el-form>
        </detail-card>
      </el-col>
      <el-col :span="12">
        <detail-card title="历史记录">
          <div slot="content">
            <Remark
              :show-add-button="true"
              :can-edit="true"
              :related-id="recordId"
              :records="historyRecords"
              module-name="testingOpera"
              @sortOpera="sortOpera"
              @addRemark="addRemark"
              @editRemark="editRemark"
            />
          </div>
        </detail-card>
      </el-col>
    </el-row>
    <!-- 弹窗 -->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      destroy-on-close
      append-to-body
      width="800px"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          :remark-data="remarkData"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
  </div>
</template>

<script>
import module from '../config.js'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import detailCard from '@/packages/detail-view/detail-card.vue'
import addRemark from '@/views/admin/testing/testingItems/detail/testingTask/action/modal-addRemark.vue'
import editRemark from '@/views/admin/testing/testingItems/detail/testingTask/action/modal-editRemark.vue'
import Remark from '@/components/testing/Remark/index.vue'
import { operaRecordQuery } from '@/api/testing/index.js'

export default {
  components: {
    detailCard,
    addRemark,
    editRemark,
    Remark
  },
  mixins: [mixinsActionMenu],
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      module,
      // 弹窗title映射
      titleMapping: {
        'addRemark': '添加备注',
        'editRemark': '编辑备注'
      },
      tbOrder: '', // 默认按时间降序排列（最新的在前面）
      recordId: '',
      remarkData: null,
      historyRecords: []
    }
  },
  created() {
    this.getHistoryList()
  },
  methods: {
    sortOpera(data) {
      this.tbOrder = data.tbOrder
      this.getHistoryList()
    },
    addRemark() {
      this.clickDrop('addRemark')
    },
    editRemark(data) {
      this.remarkData = data
      this.clickDrop('editRemark')
    },
    // 获取历史记录列表
    getHistoryList() {
      const params = {}
      params.taskId = this.$route.params.id
      params.tbOrder = this.tbOrder
      operaRecordQuery(params).then(res => {
        this.historyRecords = res.data.data.map(item => {
          return {
            time: item.createAt,
            user: item.createByName,
            operationTypeCode: item.operationTypeCode,
            operationTypeName: item.operationTypeName,
            changes: item.children ? item.children.filter(i => {
              return i.fieldLabel != '备注'
            }).map(it => {
              return {
                fieldLabel: it.fieldLabel,
                oldValue: it.fieldLabel == '状态' ? this.module.statusMapping[it.oldValue] : it.oldValue,
                newValue: it.fieldLabel == '状态' ? this.module.statusMapping[it.newValue] : it.newValue
              }
            }) : [],
            detail: item.children,
            showDetail: item.children ? item.children.length > 0 : false
          }
        })
      })
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.getHistoryList()
      }
    },
    'clickDrop': function(name, data) {
      if (this.drawerAction && this.drawerAction.includes(name)) {
        this.drawerName = name
      } else {
        this.modalName = name
      }
    }
  }
}
</script>

<style scoped lang="scss">
</style>
