<template>
  <div class="history-wrap">
    <Remark
      :show-add-button="true"
      :can-edit="true"
      :related-id="recordId"
      :records="historyRecords"
      module-name="testingOpera"
      @sortOpera="sortOpera"
      @addRemark="showAddDialog"
      @editRemark="showEditDialog"
    />
    <!-- 添加备注弹窗 -->
    <el-dialog
      :visible.sync="addDialogVisible"
      :title="titleMapping.addRemark"
      width="800px"
      append-to-body
      @close="handleAddDialogClose"
    >
      <div class="dialog-wrap">
        <editor
          ref="addEditor"
          :key="addEditorKey"
          :editor-config="blurEditorFocusConfig"
          :content="noteContent"
          height="300px"
          width="100%"
          @contentChange="contentChange('note', $event)"
        />
        <div class="dialog-footer">
          <el-button type="text" @click="handleAddDialogClose">取消</el-button>
          <el-button :loading="loading" type="primary" @click="submitAddNote">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 编辑备注弹窗 -->
    <el-dialog
      :visible.sync="editDialogVisible"
      :title="titleMapping.editRemark"
      width="800px"
      append-to-body
      @close="handleEditDialogClose"
    >
      <div class="dialog-wrap">
        <editor
          ref="editEditor"
          :key="editEditorKey"
          :editor-config="blurEditorFocusConfig"
          :content="noteContent"
          height="300px"
          width="100%"
          @contentChange="contentChange('note', $event)"
        />
        <div class="dialog-footer">
          <el-button type="text" @click="handleEditDialogClose">取消</el-button>
          <el-button :loading="loading" type="primary" @click="submitEditNote">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getOperationHistoryList, operaRecordAddRemark, operaRecordUpdateRemark } from '@/api/testing/testCase'
import Remark from '@/components/testing/Remark/index.vue'
import Editor from '@/packages/editor/index.vue'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'

export default {
  components: {
    Editor,
    Remark
  },
  mixins: [mixinsActionMenu],
  props: {
    id: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      blurEditorFocusConfig: {
        placeholder: '请输入',
        autoFocus: false
      },
      addDialogVisible: false,
      editDialogVisible: false,
      noteContent: '',
      sortDesc: true, // 默认倒序
      historyRecords: [],
      // 弹窗title映射
      titleMapping: {
        'addRemark': '添加备注',
        'editRemark': '编辑备注'
      },
      tbOrder: '', // 默认按时间降序排列（最新的在前面）
      recordId: '',
      remarkData: null,
      loading: false,
      editEditorKey: 'editEditorKey' + new Date().getTime(),
      addEditorKey: 'addEditorKey' + new Date().getTime()
    }
  },
  created() {
    this.getHistoryList()
  },
  methods: {
    getHistoryList() {
      getOperationHistoryList({
        id: this.id,
        tbOrder: this.tbOrder
      }).then(res => {
        this.historyRecords = res.data.map(item => {
          return {
            time: item.createAt,
            user: item.createByName,
            operationTypeCode: item.operationTypeCode,
            operationTypeName: item.operationTypeName,
            changes: item.children ? item.children.filter(i => {
              return i.fieldLabel != '备注'
            }).map(it => {
              return { fieldLabel: it.fieldLabel, oldValue: it.oldValue, newValue: it.newValue }
            }) : [],
            detail: item.children,
            showDetail: item.children ? item.children.length > 0 : false
          }
        })
      })
    },
    sortOpera(data) {
      this.tbOrder = data.tbOrder
      this.getHistoryList()
    },
    showAddDialog() {
      this.addEditorKey = 'addEditorKey' + new Date().getTime()
      this.noteContent = ''
      this.addDialogVisible = true
    },
    showEditDialog(data) {
      this.editEditorKey = 'editEditorKey' + new Date().getTime()
      this.remarkData = data
      if (data && data.content) {
        this.noteContent = data.content[0].remark
      }
      this.editDialogVisible = true
    },
    // 富文本编辑器内容改变
    contentChange(key, value) {
      if (this.delHtml(value)) {
        this.noteContent = value
      } else {
        const imgStrs = value.match(/<img.*?>/g)
        if (imgStrs && imgStrs.length) { // 内容只有图片时
          this.noteContent = value
        } else {
          this.noteContent = ''
        }
      }
    },

    // 过滤html代码、空格、回车 空白字符
    delHtml(str) {
      str = str.replace(/<("[^"]*"|'[^']*'|[^'">])*>/gi, '')
      str = str.replace(/[\r\n]/g, '')
      str = str.replace(/\s/g, '')
      str = str.replace(/&nbsp;/ig, '')
      return str
    },
    // 提交添加备注
    submitAddNote() {
      if (!this.noteContent || this.noteContent === '<p><br></p>') {
        this.$message.warning('请输入备注内容')
        return
      }
      this.loading = true
      const params = {}
      params.id = this.id
      params.remark = this.noteContent
      operaRecordAddRemark(params).then((res) => {
        if (res.code == 0) {
          this.$message.success('添加成功')
          this.handleAddDialogClose()
          this.getHistoryList()
        }
      }).finally(() => {
        this.loading = false
      })
    },

    // 提交编辑备注
    submitEditNote() {
      if (!this.noteContent || this.noteContent === '<p><br></p>') {
        this.$message.warning('请输入备注内容')
        return
      }
      this.loading = true
      const params = {}
      params.id = this.remarkData.content[0].id
      params.remark = this.noteContent
      operaRecordUpdateRemark(params).then((res) => {
        if (res.code == 0) {
          this.$message.success('编辑成功')
          this.handleEditDialogClose()
          this.getHistoryList()
        }
      }).finally(() => {
        this.loading = false
      })
    },

    // 关闭添加弹窗处理
    handleAddDialogClose() {
      this.noteContent = ''
      this.$forceUpdate()
      this.addDialogVisible = false
    },

    // 关闭编辑弹窗处理
    handleEditDialogClose() {
      this.noteContent = ''
      this.$forceUpdate()
      this.remarkData = null
      this.editDialogVisible = false
    }
  }
}
</script>

<style scoped>
.history-wrap {
  padding: 16px;
  background-color: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-right {
  display: flex;
  align-items: center;
}

.sort-text {
  margin-right: 8px;
  color: #606266;
}

.history-content {
  flex: 1;
  overflow-y: auto;
}

.history-item {
  margin-bottom: 16px;
}

.history-item-header {
  font-size: 14px;
  margin-bottom: 8px;
}

.history-time {
  font-weight: bold;
}

.history-operator {
  margin-left: 8px;
}

.history-item-content {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  margin-top: 8px;
}

.history-item-changes {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  margin-top: 8px;
}

.change-item {
  line-height: 24px;
}

.editor-container {
  margin-bottom: 16px;
}

.rich-text-content {
  line-height: 1.5;
}
</style>
