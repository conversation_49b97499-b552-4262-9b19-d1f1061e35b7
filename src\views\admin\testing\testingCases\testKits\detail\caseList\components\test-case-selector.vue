<template>
  <div class="drawer-wrap">
    <split-pane ref="split-pane" :min-percent="minPercent" :default-percent="percent" split="vertical" @resize="resize">
      <div
        slot="paneL"
        class="collapse-transition"
        style="padding-right: 0;height: 100%;"
      >
        <div class="tree-container">
          <tree
            ref="treeRef"
            :tree-width="treeWidth"
            :i-search="false"
            :tree-data="treeData"
            :default-props="defaultProps"
            :is-show-data-count="true"
            :is-show-children-num="false"
            :is-show-dropdown="false"
            :default-expanded-keys="treeData.length > 0 ? [treeData[0].id] : []"
            :default-checked-keys="treeData.length > 0 ? [treeData[0].id] : []"
            @currentTreeNode="currentTreeNode"
            @clearCurrentNode="clearCurrentNode"
          />
        </div>
      </div>
      <div
        slot="paneR"
        ref="tableColRef"
        class="resource-table collapse-transition"
      >
        <!-- 操作区 -->
        <div class="operation-wrap">
          <div class="operation-left">
            <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
          </div>
          <div class="operation-right">
            <el-badge :value="searchBtnShowNum">
              <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
            </el-badge>
            <!-- 自定义表格列 -->
            <t-table-config
              :data="columnsObj"
              :active-key-arr="columnsViewArr"
              style="padding-left: 5px;"
              @on-change-col="onChangeCol"
            />
          </div>
        </div>
        <div
          class="collapse-btn"
          @click="toggleCollapse"
        >
          <i
            :class="fold ? 'el-icon-caret-right' : 'el-icon-caret-left'"
          />
        </div>
        <!-- 搜索区 -->
        <t-search-box
          v-show="searchView"
          :search-key-list="searchKeyListView"
          :default-placeholder="'默认搜索测试用例标题'"
          @search="searchMultiple"
        />
        <!-- 列表 -->
        <t-table-view
          ref="tableView"
          :height="height"
          :single="isSingle"
          :loading="tableLoading"
          :data="tableData"
          :total="tableTotal"
          :page-size="pageSize"
          :current="pageCurrent"
          :select-item="selectItem"
          :default-sort="{ prop: 'sortNumber', order: 'descending' }"
          current-key="id"
          @on-select="onSelect"
          @on-current="onCurrent"
          @on-change="changePage"
          @on-sort-change="onSortChange"
          @on-page-size-change="onPageSizeChange"
        >
          <el-table-column
            v-for="item in columnsViewArr"
            :key="item"
            :min-width="colMinWidth"
            :width="columnsObj[item].colWidth"
            :label="columnsObj[item].title"
            :fixed="columnsObj[item].master ? 'left' : false"
            :sortable="columnsObj[item].sortable"
            :prop="item"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span v-if="item === 'sortNumber'">
                <a :href="`/testing/testingCaseDetail/${scope.row.id}/overview?routeSearch=true&searchVal=${scope.row.sortNumber}&searchKey=sortNumber&moduleName=testCases`" target="_blank">
                  {{ scope.row[item] || '-' }}
                </a>
              </span>
              <span v-else-if="item === 'title'">
                <a :href="`/testing/testingCaseDetail/${scope.row.id}/overview?routeSearch=true&searchVal=${scope.row.title}&searchKey=title&moduleName=testCases`" target="_blank">
                  {{ scope.row[item] || '-' }}
                </a>
              </span>
              <span v-else-if="item === 'priority'">
                <el-badge
                  :type="
                    (module.priorityObj[scope.row.priority] &&
                      module.priorityObj[scope.row.priority].type) ||
                      'info'
                  "
                  is-dot
                />
                {{
                  (module.priorityObj[scope.row.priority] &&
                    module.priorityObj[scope.row.priority].label) ||
                    "-"
                }}
              </span>
              <span v-else-if="item === 'type'">
                {{
                  (module.typeObj[scope.row.type] &&
                    module.typeObj[scope.row.type].label) ||
                    "-"
                }}
              </span>
              <span v-else-if="item === 'projectName'">
                <a v-if="scope.row.projectId" :href="`/testing/testing/detail/${scope.row.projectId}/overview`" target="_blank">
                  {{ scope.row[item] || '-' }}
                </a>
                <span v-else>
                  -
                </span>
              </span>
              <span v-else-if="item === 'referenceCount'">
                {{ scope.row[item].toString() || "-" }}
              </span>
              <span v-else>
                {{ scope.row[item] || "-" }}
              </span>
            </template>
          </el-table-column>
        </t-table-view>
      </div>
    </split-pane>
    <div class="drawer-footer">
      <el-button :disabled="selectItem.length === 0" type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script>
import { caseCategoryTreeUrlName, caseProjectPage, getSuiteSelectCaseList } from '@/api/testing/testCase.js'
import mixinsPageTable from '@/packages/mixins/page_table'
import splitPane from '@/packages/mixins/split-pane'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import tTableView from '@/packages/table-view/index.vue'
import tree from '@/packages/tree/index.vue'
import module from '../config.js'

export default {
  name: 'TestCaseSelector',
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol,
    tree
  },
  mixins: [mixinsPageTable, splitPane],
  props: {
    // 默认单选
    isSingle: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'label',
        count: 'count'
      },
      module,
      moduleName: 'testCaseSelector',
      selectItem: [],
      searchView: false,
      treeData: [],
      // 当前选中的分类ID
      currentNodeId: '0',
      // 记录搜索条件
      searchParams: {},
      // 排序相关
      sortField: '',
      sortOrder: '',
      searchKeyList: [
        { key: 'title', label: '用例标题', master: true, placeholder: '请输入测试用例标题' },
        { key: 'sortNumber', label: '用例编号' },
        { key: 'categoryName', label: '分类' },
        { key: 'type', label: '类型', type: 'select', valueList: module.typeArr },
        { key: 'priority', label: '优先级', type: 'select', valueList: module.priorityArr },
        { key: 'projectName', label: '关联项目', type: 'select', valueList: [] },
        { key: 'createByName', label: '创建人' }
      ],
      columnsObj: {
        'sortNumber': {
          title: '用例编号', master: true, colWidth: 100, sortable: true
        },
        'title': {
          title: '用例标题', master: true, sortable: true
        },
        'type': {
          title: '类型', colWidth: 100, sortable: true
        },
        'priority': {
          title: '优先级', colWidth: 90, sortable: true
        },
        'projectName': {
          title: '关联项目', sortable: true
        },
        'categoryName': {
          title: '分类', sortable: true
        },
        'createByName': {
          title: '创建人', sortable: true
        }
      },
      columnsViewArr: [
        'sortNumber',
        'title',
        'type',
        'priority',
        'projectName',
        'categoryName',
        'createByName'
      ],
      treeWidth: 210
    }
  },
  computed: {
    'searchBtnShowNum': function() { // 搜索项的数量
      if (this.searchView) return null
      return Object.keys(this.searchParams).length || null
    }
  },
  async created() {
    await this.getTreeData()
    await this.getProjectList()
    // 初始加载数据，使用树的根分类ID
    this.initDefaultTree()
  },
  methods: {
    async getProjectList() {
      const res = await caseProjectPage({ pageType: 0 })
      if (res && res.code === 0) {
        const projectItem = this.searchKeyList.find(item => item.key === 'projectName')
        if (projectItem) {
          projectItem.valueList = res.data.records.map(item => ({
            label: item.projectName,
            value: String(item.id)
          }))
        }
      }
    },
    // 刷新表格
    'refresh': function() {
      this.$emit('refresh')
      this.selectItem = []
      if (this.single) {
        this.$emit('on-select', [])
        this.setHighlightRow(null)
      }
      this.getList(this.searchParams)
    },
    // 获取数据列表
    getList(params = {}, showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      this.searchParams = { ...params }
      let postParams = this.getPostData('page', 'limit')
      postParams.suiteId = this.$route.params.id
      postParams.pageType = 1
      postParams = { ...postParams, ...params }

      // 添加分类ID
      if (this.currentNodeId !== undefined) {
        postParams.categoryId = this.currentNodeId
      }

      // 处理搜索参数
      if (params.title) {
        postParams.title = params.title
      }
      if (params.sortNumber) {
        postParams.sortNumber = params.sortNumber
      }
      if (params.categoryName) {
        postParams.categoryName = params.categoryName
      }
      if (params.type) {
        postParams.type = params.type.split(',')
      }
      if (params.priority) {
        postParams.priority = params.priority.split(',')
      }
      if (params.projectName) {
        postParams.projectIds = params.projectName.split(',')
      }
      if (params.createByName) {
        postParams.createByName = params.createByName
      }

      // 处理排序
      if (this.sortField) {
        postParams.sortField = this.sortField
        postParams.sortOrder = this.sortOrder || ''
      } else if (!this.sortField && !params.sortField) {
        // 默认排序
        postParams.sortField = 'sortNumber'
        postParams.sortOrder = 'desc'
      }

      getSuiteSelectCaseList(postParams).then(res => {
        this.tableData = res.data.records
        this.tableTotal = res.data.total
        this.handleSelection()
      }).finally(() => {
        this.tableLoading = false
      })
    },

    // 初始化默认树选择
    initDefaultTree() {
      // 使用树的根分类ID
      if (this.treeData && this.treeData.length > 0) {
        this.currentNodeId = this.treeData[0].id
      } else {
        this.currentNodeId = '0' // 如果没有树数据，使用默认值
      }
      // 加载数据
      this.getList()
      // 确保根节点被选中
      this.$nextTick(() => {
        if (this.$refs.treeRef) {
          this.$refs.treeRef.setCurrentKey(this.currentNodeId)
        }
      })
    },

    // 获取分类树结构
    async getTreeData() {
      try {
        this.tableLoading = true
        const params = {
          type: 1, // 根据实际接口参数调整
          busId: this.$route.params.id,
          urlName: 'suite-case-add-case'
        }
        const res = await caseCategoryTreeUrlName(params)

        if (res && res.data) {
          // 转换树结构数据
          this.treeData = this.transformTreeData(res.data)
        } else {
          this.treeData = []
        }
      } catch (error) {
        console.error('获取分类树结构失败:', error)
        this.treeData = []
      } finally {
        this.tableLoading = false
      }
    },

    // 转换树结构数据
    transformTreeData(data) {
      if (!data || !Array.isArray(data)) return []

      return data.map(item => {
        const node = {
          nodeKey: item.id,
          label: item.name || '未命名分类',
          id: item.id,
          parentId: item.parentId,
          type: item.type,
          count: item.count || ''
        }

        if (
          item.children &&
          Array.isArray(item.children) &&
          item.children.length > 0
        ) {
          node.children = this.transformTreeData(item.children)
        }

        return node
      })
    },

    // 打开搜索
    openSearch() {
      this.searchView = !this.searchView
    },

    // 当前树节点变化
    currentTreeNode(data, node) {
      if (data && data.id !== undefined) {
        this.currentNodeId = data.id
        // 保留当前搜索条件，结合树节点进行搜索
        this.getList(this.searchParams)
      }
    },

    // 清除当前树节点选择
    clearCurrentNode() {
      // 重置为根分类
      if (this.treeData && this.treeData.length > 0) {
        this.currentNodeId = this.treeData[0].id
      } else {
        this.currentNodeId = '0'
      }
      this.getList()
    },

    // 处理搜索
    searchMultiple(params) {
      this.pageSize = 10
      this.pageCurrent = 1
      this.searchParams = { ...params }
      // 结合当前树节点和搜索条件进行搜索
      this.getList(params)
    },

    // 切换左侧树的折叠状态
    toggleCollapse() {
      this.fold = !this.fold
      if (this.fold) {
        this.percent = this.minPercent
      } else {
        this.percent = 20
      }
    },

    // 处理排序变化
    onSortChange({ column, prop, order }) {
      // 映射排序字段
      const fieldMapping = {
        'ID': 'sortNumber',
        '标题': 'title',
        '分类': 'categoryName',
        '优先级': 'priority',
        '类型': 'type',
        '创建人': 'createByName'
      }

      this.sortField = fieldMapping[column.label] || prop || ''
      if (order === 'descending') {
        this.sortOrder = 'desc'
      } else if (order === 'ascending') {
        this.sortOrder = 'asc'
      } else {
        this.sortOrder = ''
      }
      this.getList(this.searchParams)
    },

    // 取消选择
    cancel() {
      this.$emit('close')
    },

    // 确认选择
    confirm() {
      // 将选中的测试用例返回给父组件
      this.$emit('call', 'testCase', this.selectItem)
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.resource-table {
  height: 100%;
  padding-left: 0;
  position: relative;

  .collapse-btn {
    z-index: 200;
    position: absolute;
    top: calc(50% - 80px);
    left: -10px;
    width: 10px;
    height: 60px;
    background-color: var(--color-600);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .collapse-transition {
    transition: all 0.3s ease;
  }
}

.tree-container {
  height: 100%;
  overflow-y: auto;
  border-right: 1px solid var(--neutral-300);
}

.operation-wrap {
  flex-shrink: 0;
}

.t-table-view {
  flex: 1;
  overflow: hidden;
}

</style>
