<template>
  <div class="buttons-wrap">
    <el-button
      v-permission="'manage.testing.project.projectDetail.testEnvironment.vmInstanceSnapshotsCreate'"
      v-if="showHandleButton"
      :disabled="createDisableData || data.pendingStatus == 9"
      icon="el-icon-plus" type="primary"
      @click="clickDrop('createSnapshots')">创建快照</el-button>
    <!-- 待部署环境状态才高亮显示 -->
    <el-button
      v-permission="'manage.testing.project.projectDetail.testEnvironment.envDeployed'"
      v-if="showHandleButton && showDeployedBtn"
      :disabled="(deployed && redeploy == null) || (redeploy != null && redeploy) || data.projectStatus != '6' || data.pendingStatus == 9"
      type="primary"
      @click="clickDrop('setAsDeployed')">设为部署完成</el-button>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :modal="false"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          :env-type="envType"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import createSnapshots from './modal-create.vue'
import setAsDeployed from './modal-deploy.vue'
export default {
  components: {
    createSnapshots,
    setAsDeployed
  },
  mixins: [mixinsActionMenu],
  props: {
    selectItem: {
      type: Array,
      default: () => []
    },
    envType: {
      type: String,
      default: 'network'
    },
    // 0: 未完成 1: 完成
    deployed: {
      type: Number,
      default: 0
    },
    // 重新部署 0: 未完成 1: 完成
    redeploy: {
      type: Number,
      default: null
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'createSnapshots': '创建快照',
        'setAsDeployed': '设为部署完成'
      },
      roleIds: [],
      showDeployBtnRoles: [],
      routeName: '',
      showDeployedBtn: false,
      userInfo: JSON.parse(localStorage.getItem('loginUserInfo')) || {}
    }
  },
  computed: {
    // 所选数据状态均不符合按钮置灰
    createDisableData: function() {
      const flag = this.selectItem.every((item) => {
        return !['running', 'shutoff'].includes(item.status)
      })
      return flag
    },
    // 检测项目测试环境才显示按钮
    showHandleButton() {
      return this.routeName == 'testing_detail'
    }
  },
  mounted() {
    // 1.testing_detail: 检测项目测试环境
    // 2.testingTask_detail：测试任务详情测试环境
    this.routeName = this.$route.name
    // 检测厂商：181254 超管：180162
    this.roleIds = this.userInfo.roleIds ? JSON.parse(this.userInfo.roleIds) : []
    this.showDeployBtnRoles = [181254, 180162]
    this.showDeployedBtn = this.roleIds.some(item => this.showDeployBtnRoles.includes(item))
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      } else if (type === 'refreshDeploy') {
        this.$emit('call', type)
      }
    },
    'clickDrop': function(name) {
      this.modalName = name
    }
  }
}
</script>
