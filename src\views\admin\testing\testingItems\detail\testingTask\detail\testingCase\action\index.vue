<template>
  <div class="buttons-wrap">
    <el-dropdown trigger="click" placement="bottom-start" @command="clickDrop">
      <el-button :disabled="detailData.pendingStatus == 9" type="primary">
        添加测试用例 <i class="el-icon-arrow-down el-icon--right"/>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-permission="'manage.testing.project.projectDetail.projectTask.taskDetailView.taskCase.taskCaseSelectPage'" command="addCase">直接添加用例</el-dropdown-item>
        <el-dropdown-item v-permission="'manage.testing.project.projectDetail.projectTask.taskDetailView.taskCase.taskCaseSuitePage'" command="addKit">从测试套件添加</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <el-dropdown style="margin-right: 4px;" trigger="click" placement="bottom-start" @command="clickDrop">
      <el-button type="primary">
        操作<i class="el-icon-arrow-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-permission="'manage.testing.project.projectDetail.projectTask.taskDetailView.taskCase.taskCaseAssignTo'" :disabled="multipleDisabled || detailData.pendingStatus == 9" command="assignItem">指派给</el-dropdown-item>
        <el-dropdown-item v-permission="'manage.testing.project.projectDetail.projectTask.taskDetailView.taskCase.taskCaseDelete'" :disabled="multipleDisabled || detailData.pendingStatus == 9" command="removeItem">移除</el-dropdown-item>
        <el-dropdown-item v-permission="'manage.testing.project.projectDetail.projectTask.taskDetailView.taskCase.taskCaseExport'" command="exportItem">导出</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 弹窗 -->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 侧拉弹窗 start -->
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="computeDrawerWidth"
      append-to-body
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          :data="selectItem"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>
    <!-- 侧拉弹窗 end -->
  </div>
</template>

<script>
import addCase from './select-addCase.vue'
import addKit from './select-testKit.vue'
import assignItem from './modal-assignItem.vue'
import removeItem from './modal-removeItem.vue'
import exportItem from './modal-exportItem.vue'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import { taskCaseAssociation, taskCaseAssociationSuite } from '@/api/testing/testCase.js'
import { testingItemsDetailAPI } from '@/api/testing/index'

export default {
  name: 'ActionMenu',
  components: {
    addCase,
    addKit,
    assignItem,
    removeItem,
    exportItem
  },
  mixins: [mixinsActionMenu],
  inject: ['tableVm'],
  props: {
    moduleName: {
      type: String,
      default: ''
    },
    selectItem: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      detailData: {},
      // 弹窗title映射
      titleMapping: {
        'addCase': '选择测试用例',
        'addKit': '选择测试套件',
        'assignItem': '指派给',
        'removeItem': '移除',
        'exportItem': '导出'
      }
    }
  },
  computed: {
    computeDrawerWidth() {
      if (this.drawerName === 'addCase') {
        return '56.25%'
      } else if (this.drawerName === 'addKit') {
        return '56.25%'
      } else {
        return this.drawerWidth
      }
    }
  },
  created() {
    this.getData()
  },
  methods: {
    // 根据id获取详情数据
    'getData': function() {
      this.loading = true
      this.id = this.$route.name === 'testingTask_detail' ? this.$route.params.projectId : this.$route.params.id
      testingItemsDetailAPI(this.id).then(res => {
        if (res.data && res.data.code === 0) {
          this.detailData = res.data.data
        } else {
          this.$message.error(res.data.msg || '获取项目详情失败')
        }
        this.loading = false
      })
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.tableVm.refresh()
      }
    },
    'clickDrop': function(name) {
      // 需要侧拉打开的操作
      if (['addCase', 'addKit'].includes(name)) {
        this.drawerName = name
      } else {
        this.modalName = name
      }
    },
    drawerConfirmCall: function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      } else if (type === 'caseSelect') {
        this.drawerName = 'addCase'
        this.caseSelect(data)
      } else if (type === 'kitSelect') {
        this.kitSelect(data)
      }
    },
    kitSelect(data) {
      const params = {
        taskId: this.$route.params.id,
        suiteIds: data.map(item => item.id),
        categoryId: this.tableVm.currentNodeId
      }
      taskCaseAssociationSuite(params).then(res => {
        if ([0, 200].includes(res.code)) {
          this.$message.success('添加成功')
          this.drawerClose()
          this.tableVm.refresh()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    caseSelect(data) {
      const caseCategoryIdBoList = data.map(item => {
        return {
          caseId: item.id,
          categoryId: item.categoryId
        }
      })
      const params = {
        taskId: this.$route.params.id,
        caseCategoryIdBoList
      }
      taskCaseAssociation(params).then(res => {
        if ([0, 200].includes(res.code)) {
          this.$message.success('添加成功')
          this.drawerClose()
          this.tableVm.refresh()
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.action-menu {
  display: inline-block;
}
</style>
