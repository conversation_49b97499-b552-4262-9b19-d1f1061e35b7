<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-width="100px" @submit.native.prevent>
      <el-form-item label="账号" prop="username">
        <el-input v-model.trim="formData.username" :disabled="editMode"/>
      </el-form-item>
      <el-form-item v-if="!editMode" label="登录密码" prop="password">
        <el-input v-model.trim="formData.password" type="password" show-password/>
      </el-form-item>
      <el-form-item label="姓名" prop="realname">
        <el-input v-model.trim="formData.realname"/>
      </el-form-item>
      <el-form-item label="性别" prop="sex">
        <el-select v-model="formData.sex" size="medium" clearable placeholder="请选择" style="width: 100%">
          <el-option
            v-for="item in sexList"
            :key="item.value"
            :label="item.label"
            :value="Number(item.value)"/>
        </el-select>
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input v-model.trim="formData.mobile"/>
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model.trim="formData.email"/>
      </el-form-item>
      <el-form-item label="企业" prop="deptId">
        <el-select v-model="formData.deptId" filterable placeholder="请选择" style="width: 100%">
          <el-option
            v-for="item in deptList"
            :key="item.id"
            :label="item.label"
            :value="item.id"/>
        </el-select>
      </el-form-item>
      <el-form-item label="岗位" prop="post">
        <el-input v-model.trim="formData.post"/>
      </el-form-item>
      <el-form-item label="直属上级" prop="parentId">
        <el-tag
          v-if="selectedParent"
          :disable-transitions="true"
          closable
          @click="drawerName = 'selectParent'"
          @close="selectedParent = null, formData.parentId = ''">
          {{ selectedParent.realname }}
        </el-tag>
        <el-button v-else type="ghost" @click="drawerName = 'selectParent'">选择直属上级</el-button>
      </el-form-item>
      <el-form-item label="角色" prop="roleId">
        <el-select
          v-model="formData.roleId"
          :popper-append-to-body="false"
          popper-class="select-popper-class"
          filterable
          multiple
          style="width: 100%">
          <el-option-group
            v-for="group in roleGroups"
            :key="group.pid"
            :label="group.name">
            <el-option
              v-for="item in group.list"
              :key="item.roleId"
              :label="item.roleName"
              :value="item.roleId" />
          </el-option-group>
        </el-select>
      </el-form-item>
      <el-form-item prop="certificationIdCard">
        <span slot="label">
          <span>身份证号</span>
          <el-tooltip v-if="editMode" transfer>
            <i class="cr-icon-info" />
            <div slot="content">{{ activeItem.haveCertificationIdCard ? '已绑定身份证号，设置新的身份证号进行覆盖' : '未绑定身份证号' }}</div>
          </el-tooltip>
        </span>
        <el-input v-model.trim="formData.certificationIdCard"/>
      </el-form-item>
    </el-form>
    <!-- 侧拉弹窗 start -->
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      append-to-body
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          :single="true"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>
    <!-- 侧拉弹窗 end -->
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import module from '../config.js'
import validate from '@/packages/validate'
import { addUserAPI, editUserAPI } from '@/api/usercenter/user'
import { getDeptListAPI } from '@/api/usercenter/dept'
import { getRoleListAPI } from '@/api/usercenter/role'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import selectParent from '@/views/admin/usercenter/user/action/selectUsers.vue'
import encryption from '@/utils/encryption'

export default {
  components: {
    selectParent
  },
  mixins: [mixinsActionMenu],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    },
    deptId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      sexList: module.sexArr,
      titleMapping: {
        'selectParent': '选择直属上级'
      },
      formData: {
        'username': '',
        'password': '',
        'realname': '',
        'sex': null,
        'mobile': '',
        'email': '',
        'deptId': '',
        'post': '',
        'parentId': '',
        'roleId': [],
        'certificationIdCard': ''
      },
      selectedParent: null,
      deptList: [],
      roleGroups: [],
      loading: false,
      validate: validate,
      rules: {
        'username': [
          validate.required('blur'),
          { pattern: /^(?!\s)[^\s]{2,64}(?<!\s)$/, message: '2-64个字符，不支持空格' }
        ],
        'password': [
          validate.required('blur'),
          { pattern: /^[\da-zA-Z!@#$%^&*]{1,20}$/, message: '密码由1-20位组成' }
        ],
        'realname': [
          validate.required('blur'),
          validate.name_64_char
        ],
        'mobile': [
          validate.mobilePhone
        ],
        'email': [
          validate.email
        ],
        'deptId': [
          validate.required('change')
        ],
        'roleId': [
          validate.required('change')
        ],
        'certificationIdCard': [
          validate.idcard
        ]
      },
      apiType: addUserAPI
    }
  },
  computed: {
    editMode: function() {
      return this.name === 'modalModify'
    },
    activeItem: function() {
      return this.data[0]
    }
  },
  created() {
    this.formData.deptId = this.deptId
    this.getDeptList()
    this.getRoleList()
    if (this.editMode && this.activeItem) {
      for (const key in this.formData) {
        if (key === 'roleId') {
          this.formData[key] = this.activeItem[key] ? this.activeItem[key].split(',').map(item => parseInt(item)) : []
        } else {
          this.formData[key] = this.activeItem[key]
        }
      }
      if (this.activeItem.parentId && this.activeItem.parentName) {
        this.selectedParent = {
          'userId': this.activeItem.parentId,
          'realname': this.activeItem.parentName
        }
      }
    }
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    // 侧拉选择资源回调
    drawerConfirmCall: function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'select_users') {
        this.selectedParent = data[0]
        this.formData.parentId = data[0].userId
        this.drawerClose()
      }
    },
    getDeptList() {
      getDeptListAPI({ type: 'save' }).then(res => {
        this.deptList = res.data || []
      })
    },
    getRoleList() {
      getRoleListAPI({}).then(res => {
        this.roleGroups = res.data || []
      })
    },
    confirm: function() {
      this.loading = true
      // 批量调用接口
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const postData = JSON.parse(JSON.stringify(this.formData))
          if (postData.certificationIdCard) {
            postData.certificationIdCard = encryption.encrypt(postData.certificationIdCard)
          }
          if (this.name === 'modalModify') {
            this.apiType = editUserAPI
            postData.userId = this.activeItem['userId']
          } else {
            // 加密密码和身份证号
            if (postData.password) {
              postData.password = encryption.encrypt(postData.password)
            }
          }

          // 处理角色ID
          if (Array.isArray(postData.roleId)) {
            postData.roleId = postData.roleId.join(',')
          }

          this.apiType(postData).then(res => {
            this.$message.success(`${this.name === 'modalAdd' ? '用户创建' : '用户编辑'}成功`)
            this.$emit('call', 'refresh')
            this.close()
          }).catch(() => {
            this.loading = false
          })
        } else {
          this.loading = false
        }
      })
    }
  }
}
</script>
