<template>
  <el-row :gutter="20">
    <el-col :span="12">
      <detail-card title="基本信息">
        <el-form slot="content" label-position="left" label-width="130px">
          <el-form-item label="流程名称：">
            {{ data.processName || '-' }}
          </el-form-item>
          <el-form-item label="流程描述：">
            <div v-if="data.processDesc" style="white-space: pre-line" v-html="data.processDesc" />
            <div v-else>-</div>
          </el-form-item>
          <el-form-item label="任务数量：">
            {{ data.taskCount || 0 }}
          </el-form-item>
          <el-form-item label="状态：">
            <span v-if="data.status == '1'"><el-badge type="success" is-dot />启用</span>
            <span v-else-if="data.status == '0'"><el-badge type="error" is-dot />禁用</span>
            <span v-else><el-badge type="info" is-dot /></span>
          </el-form-item>
          <el-form-item label="创建人：">
            {{ data.createByName || '-' }}
          </el-form-item>
          <el-form-item label="创建时间：">
            {{ data.createAt || '-' }}
          </el-form-item>
        </el-form>
      </detail-card>
    </el-col>
    <el-col :span="12">
      <detail-card title="任务信息">
        <template slot="content">
          <el-timeline class="task-timeline">
            <el-timeline-item
              v-for="(task, idx) in testProcessTaskBOList || []"
              :key="'task-' + idx"
              class="task-timeline-item"
            >
              <div class="task-row">
                <div class="task-content">
                  <div>
                    <span class="task-label">任务：</span>
                    <span :title="task.taskName">{{ task.taskName }}</span>
                  </div>
                  <i :class="taskCollapse[idx] ? 'el-icon-arrow-down' : 'el-icon-arrow-up'" class="collapse-btn" @click="toggleTask(idx)" />
                </div>
              </div>
              <transition name="fade">
                <div v-show="!taskCollapse[idx]" class="subtask-wrap">
                  <div
                    v-for="(sub, subIdx) in task.sonTestProcessTaskBOList || []"
                    :key="'sub-' + idx + '-' + subIdx"
                    class="subtask-row"
                  >
                    <div class="solid-line" />
                    <span><span class="task-label">子任务：</span>{{ sub.taskName }}</span>
                  </div>
                </div>
              </transition>
            </el-timeline-item>
          </el-timeline>
        </template>
      </detail-card>
    </el-col>
  </el-row>
</template>

<script>
import detailCard from '@/packages/detail-view/detail-card.vue'
import { getTaskProcessesByIdApi } from '@/api/testing/testProcesses.js'
export default {
  name: 'DetailOverview',
  components: {
    detailCard
  },
  props: {
    id: {
      type: [Number, String]
    },
    data: {
      type: Object
    }
  },
  data() {
    return {
      taskCollapse: [],
      testProcessTaskBOList: [] // 任务列表
    }
  },
  mounted() {
    if (this.id) {
      // 查询检测流程任务信息
      getTaskProcessesByIdApi(this.id).then((res) => {
        if (res.data && [0, 200].includes(res.code)) {
          // 转换接口数据为表单格式
          this.testProcessTaskBOList = (res.data || []).map(item => ({
            taskName: item.processName,
            sonTestProcessTaskBOList: (item.children || []).map(sub => ({
              taskName: sub.processName
            }))
          }))
        }
      })
    }
  },
  methods: {
    toggleTask(idx) {
      this.$set(this.taskCollapse, idx, !this.taskCollapse[idx])
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .detail-card-body {
  max-height: 85vh;
  overflow-y: auto;
}
.task-timeline {
  padding-left: 24px;
  width: 100%;
  ::v-deep .el-timeline-item {
    .el-timeline-item__tail {
      top: 20px;
    }
    .el-timeline-item__node {
      top: 10px;
    }
    .el-timeline-item__wrapper {
      padding-left: 20px;
    }
  }
  .task-timeline-item {
    padding-bottom: 0;
  }
  .task-row {
    font-size: 14px;
    color: #000;
    display: flex;
    width: calc(100% - 20px);
    align-items: flex-start;
    .task-content {
      width: 100%;
      background: #f6f8fa;
      border-radius: 4px;
      padding: 10px 12px;
      margin-bottom: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      >div {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: calc(100% - 20px);
      }
      .collapse-btn {
        width: 20px;
        font-size: 14px;
        cursor: pointer;
      }
    }
  }
  .subtask-wrap {
    padding-left: 54px;
    .subtask-row {
      display: flex;
      align-items: center;
      min-height: 32px;
      line-height: 20px;
      position: relative;
      .solid-line {
        width: 20px;
        height: 12px;
        border-left: 1px solid #E4E7ED;
        border-bottom: 1px solid #E4E7ED;
        position: absolute;
        left: -28px;
        top: 5px;
      }
      .fade-enter-active, .fade-leave-active {
        transition: all 0.2s;
      }
      .fade-enter, .fade-leave-to {
        opacity: 0;
        height: 0;
      }
    }
  }
  .task-label {
    color: #666;
  }
}
</style>
