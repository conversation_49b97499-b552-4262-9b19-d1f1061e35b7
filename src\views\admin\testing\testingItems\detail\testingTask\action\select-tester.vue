<template>
  <div class="drawer-wrap">
    <selected-tester
      ref="table"
      :filter-data="{}"
      :link="false"
      :data="data"
      :is-single="isSingle"
      :selected-data="transformSelectedItem"
      height="auto"
      @on-select="onSelect"
      @link-event="linkEvent"
    />
    <div class="drawer-footer">
      <el-button :disabled="!selectItem.length" type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
  </div>
</template>

<script>
import selectedTester from '@/components/testing/selectTester/index.vue'
export default {
  components: {
    selectedTester
  },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    name: {
      type: String,
      default: ''
    },
    isSingle: {
      type: Boolean,
      default: true
    },
    selectedItem: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selectItem: []
    }
  },
  computed: {
    transformSelectedItem() {
      // 将从父组件传入的 selectedItem 格式转换为 selectTester 组件需要的格式
      if (!this.selectedItem || !Array.isArray(this.selectedItem) || this.selectedItem.length === 0) {
        return []
      }
      return this.selectedItem.map(item => {
        return {
          userId: item.id,
          realname: item.name
        }
      })
    }
  },
  watch: {
    selectedItem: {
      handler(newVal) {
        if (this.$refs.table && typeof this.$refs.table.setSelectedData === 'function') {
          this.$refs.table.setSelectedData(this.transformSelectedItem)
        }
      },
      immediate: true
    }
  },
  methods: {
    'linkEvent': function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    'onSelect': function(data) {
      this.selectItem = data
    },
    'close': function() {
      this.$emit('close')
    },
    'confirm': function() {
      if (this.selectItem && this.selectItem.length > 0) {
        this.$emit('call', this.name, this.selectItem)
      } else {
        this.$emit('call', this.name, [])
      }
    }
  }
}
</script>
