<template>
  <el-dialog
    :visible.sync="visible"
    :title="title"
    width="520px"
    @closed="$emit('close')"
  >
    <div v-loading="loading" class="dialog-wrap">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="80px"
        @submit.native.prevent
      >
        <el-form-item label="名称" prop="suiteName">
          <el-input v-model.trim="formData.suiteName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="分类" prop="categoryId">
          <div class="category-select">
            <el-select
              ref="categorySelect"
              v-model="formData.categoryName"
              placeholder="请选择"
              clearable
              style="width: 100%;"
              @clear="handleCategoryClose"
            >
              <el-option :value="formData.categoryName" class="tree-select-option" disabled>
                <div class="tree-select-container">
                  <div class="tree-filter-fixed">
                    <el-input
                      v-model="categoryFilterText"
                      class="tree-filter-input"
                      size="mini"
                      clearable
                      placeholder="按名称搜索"
                      @input="handleFilterChange"
                    />
                  </div>
                  <div class="tree-container-scroll">
                    <el-tree
                      ref="categoryTree"
                      :data="filteredCategoryList"
                      :props="{ children: 'children', label: 'name' }"
                      :filter-node-method="filterNode"
                      node-key="id"
                      highlight-current
                      default-expand-all
                      @node-click="handleNodeClick"
                    >
                      <div
                        slot-scope="{ node, data }"
                        :id="data['id']"
                        :node-data="data"
                        style="width:100%"
                      >
                        <el-tooltip
                          :content="data['name']"
                          placement="top"
                          width="200"
                        >
                          <div class="tree-info">
                            <span>{{ data['name'] }}</span>
                          </div>
                        </el-tooltip>
                      </div>
                    </el-tree>
                  </div>
                </div>
              </el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model.trim="formData.description"
            :rows="4"
            type="textarea"
            placeholder="请输入"
          />
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button type="text" @click="$emit('close')">取消</el-button>
        <el-button type="primary" @click="confirm">确定</el-button>
      </div>
    </div>


  </el-dialog>
</template>

<script>
import { caseCategoryTree, testSuiteCopy, testSuiteCreate, testSuiteUpdate } from '@/api/testing/testCase.js'
import mixinsActionMenu from '@/packages/mixins/action_menu'
import Tree from '@/packages/tree'
import validate from '@/packages/validate'

export default {
  name: 'ModalAddTestKit',
  components: {
    Tree
  },
  mixins: [mixinsActionMenu],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '创建测试套件'
    },
    actionType: {
      type: String,
      default: 'add' // add, edit, copy
    },
    editData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      loading: false,
      formData: {
        suiteName: '',
        categoryId: null,
        categoryName: '',
        description: ''
      },
      rules: {
        suiteName: [
          validate.required(),
          validate.name_64_char
        ],
        categoryId: [
          {
            required: true,
            message: '必填项',
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (!this.formData.categoryId) {
                callback(new Error('必填项'))
              } else {
                callback()
              }
            }
          }
        ]
      },
      // 分类选择相关
      categoryFilterText: '',
      categoryList: []
    }
  },
  computed: {
    // 过滤后的分类列表
    filteredCategoryList() {
      return this.categoryList
    }
  },
  watch: {
    // 监听编辑数据变化，回显数据
    editData: {
      handler(val) {
        if (val) {
          // 清空表单
          this.resetForm()
          // 回显数据
          this.formData = {
            ...val
          }
        }
      },
      immediate: true
    },
    // 监听弹窗显示状态
    visible(val) {
      if (val) {
        if (!this.editData) {
          // 如果是新建状态，重置表单
          this.resetForm()
        }
        // 获取分类树数据
        this.getCategoryTree()
      }
    },
    // 监听分类筛选文本变化
    categoryFilterText(val) {
      this.$refs.categoryTree && this.$refs.categoryTree.filter(val)
    },
    // 监听分类列表变化，在编辑模式下自动展开到当前节点
    categoryList: {
      handler(val) {
        if (val.length > 0 && this.formData.categoryId && (this.actionType === 'edit' || this.actionType === 'copy')) {
          this.$nextTick(() => {
            // 递归查找并展开到当前节点
            this.expandToCurrentNode(this.formData.categoryId)
          })
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取分类树数据
    async getCategoryTree() {
      try {
        this.loading = true
        const res = await caseCategoryTree({ type: 2 }) // 传入type=2
        if (res && res.code === 0 && res.data) {
          this.categoryList = res.data
        }
      } catch (error) {
        console.error('获取分类树数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 重置表单
    resetForm() {
      this.formData = {
        suiteName: '',
        categoryId: null,
        categoryName: '',
        description: ''
      }
      this.$nextTick(() => {
        this.$refs.formRef && this.$refs.formRef.clearValidate()
      })
    },

    // 过滤节点方法
    filterNode(value, data) {
      if (!value) return true
      return data.name.toLowerCase().indexOf(value.toLowerCase()) !== -1
    },

    // 处理过滤输入变化
    handleFilterChange(val) {
      if (val) {
        // 当有搜索内容时，展开所有节点以便查看结果
        this.$nextTick(() => {
          this.$refs.categoryTree && this.$refs.categoryTree.filter(val)
        })
      }
    },

    // 处理节点点击事件
    handleNodeClick(data) {
      this.formData.categoryId = data.id
      this.formData.categoryName = data.name
      // 关闭下拉框
      this.$nextTick(() => {
        this.$refs.categorySelect && this.$refs.categorySelect.blur()
      })
      this.$refs.formRef.validateField('categoryId')
    },

    // 删除已选分类
    handleCategoryClose() {
      this.formData.categoryId = null
      this.formData.categoryName = ''
      this.$refs.formRef.validateField('categoryId')
    },

    // 递归查找并展开到当前节点
    expandToCurrentNode(categoryId) {
      if (!this.$refs.categoryTree) return

      // 查找节点路径
      const findNodePath = (tree, id, path = [], level = 0) => {
        for (let i = 0; i < tree.length; i++) {
          const node = tree[i]
          const currentPath = [...path, node.id]

          if (node.id === id) {
            return currentPath
          }

          if (node.children && node.children.length > 0) {
            const foundPath = findNodePath(node.children, id, currentPath, level + 1)
            if (foundPath) return foundPath
          }
        }
        return null
      }

      const nodePath = findNodePath(this.categoryList, categoryId)
      if (nodePath) {
        // 展开路径上的所有节点
        nodePath.forEach(nodeId => {
          this.$refs.categoryTree.store.nodesMap[nodeId] &&
          this.$refs.categoryTree.store.nodesMap[nodeId].expanded === false &&
          this.$refs.categoryTree.store.nodesMap[nodeId].expand()
        })

        // 设置当前选中节点
        this.$refs.categoryTree.setCurrentKey(categoryId)
      }
    },

    // 提交表单
    confirm() {
      this.$refs.formRef.validate(async valid => {
        if (valid) {
          this.loading = true

          try {
            // 准备提交数据
            const submitData = {
              suiteName: this.formData.suiteName,
              categoryId: this.formData.categoryId,
              description: this.formData.description || ''
            }

            // 根据操作类型选择不同的API
            let res
            if (this.actionType === 'edit' && this.editData && this.editData.id) {
              submitData.id = this.editData.id
              res = await testSuiteUpdate(submitData)
            } else if (this.actionType === 'copy' && this.editData && this.editData.id) {
              // 复制功能，需要传递copyId
              submitData.copyId = this.editData.id
              res = await testSuiteCopy(submitData)
            } else {
              res = await testSuiteCreate(submitData)
            }

            if (res && (res.code === 0 || res.code === 200)) {
              this.$emit('success', res.data || submitData)
            } else {
              if (this.actionType === 'edit') {
                this.$message.error('编辑失败')
              }
              if (this.actionType === 'copy') {
                this.$message.error('复制失败')
              }
              if (this.actionType === 'add') {
                this.$message.error('创建失败')
              }
            }
          } catch (error) {
            console.error('操作失败:', error)
          } finally {
            this.loading = false
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-wrap {
  padding: 0 20px;
}

.category-select {
  display: flex;
  align-items: center;
  width: 100%;
}

.tree-select-option {
  padding: 0;
  height: auto;
}

.tree-select-container {
  display: flex;
  flex-direction: column;
  max-height: 255px;
}

.tree-filter-fixed {
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: #fff;
  padding: 5px;
  border-bottom: 1px solid #ebeef5;
}

.tree-filter-input {
  width: 100%;
  margin-bottom: 5px;
}

.tree-container-scroll {
  width: 350px;
  flex: 1;
  overflow-y: auto;
  max-height: 220px;

  .tree-info {
    width: 280px;
    font-size: 14px;
    font-weight: 500;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  ::v-deep .el-tree-node__content {
    height: 30px;
    line-height: 30px;
  }

  ::v-deep .el-tree-node__label {
    width: 280px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    font-size: 14px;
    font-weight: 500;
  }

  ::v-deep .el-tree-node.is-current > .el-tree-node__content {
    background-color: #F5F7FA;
    color: var(--color-600);
  }
  ::v-deep .el-tree-node__content{
    &:hover{
      background-color: #F5F7FA;
      color: var(--color-600);
    }
  }
}

::v-deep .el-input__validateIcon {
  display: none;
}
</style>
