<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="12">
        <detail-card title="检测报告" class="mb-15">
          <el-form slot="content" label-position="left" label-width="130px">
            <el-form-item label="检测报告模板">
              <div class="tpl-upload">
                <div class="upload-select">
                  <input ref="reportFile" type="file" class="upload-select-input" @change="handleFileChange($event, 'report')">
                  <el-button
                    v-permission="'manage.testing.testProcesses.testProcessesDetail.testReportTemplate.testProcessesUploadFile'"
                    v-if="!formData.report.fileName"
                    class="upload-click-wrap"
                    @click="handleUploadClick('report')">上传文件</el-button>
                  <el-progress
                    v-if="formData.report.progress > 0 && formData.report.progress < 100"
                    :percentage="formData.report.progress"
                    :show-text="true"
                  />
                  <div v-show="formData.report.fileName" :style="{ 'border-color': formData.report.fileError ? '#F56C6C' : 'var(--color-601-border)', 'background-color': formData.report.fileError ? '#fff6f4' : 'var(--color-601-background)' }" class="file-container">
                    <i :style="{ 'color': formData.report.fileError ? '#F56C6C' : '#909399' }" class="el-icon-document" size="16" />
                    <div :title="formData.report.fileName" class="file-name">{{ formData.report.fileName }}</div>
                    <div class="action-buttons">
                      <el-button title="预览" type="text" icon="el-icon-view" @click="handleFileView('report', formData.report)" />
                      <el-button
                        v-permission="'manage.testing.testProcesses.testProcessesDetail.testReportTemplate.testProcessesUpdateFile'"
                        title="更新" type="text" icon="el-icon-upload2"
                        @click="handleUploadClick('report')"
                      />
                      <el-button title="下载" type="text" icon="el-icon-download" @click="handleFileDownload('report')" />
                      <el-button
                        v-permission="'manage.testing.testProcesses.testProcessesDetail.testReportTemplate.testProcessesDeleteFile'"
                        title="删除" type="text" icon="el-icon-delete"
                        @click="handleFileDelete('report', formData.report.fileName)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </detail-card>
        <detail-card title="测试任务报告">
          <el-form slot="content" label-position="left" style="padding-left: 15px;">
            <el-form-item label="测试任务" class="task-title">
              <span>测试任务报告模板</span>
            </el-form-item>
            <template v-for="(task, taskIndex) in testProcessTaskBOList">
              <!-- 没有子任务 -->
              <el-form-item v-if="!task.children || task.children.length === 0" :key="'task_' + taskIndex" :title="task.processName" :label="`任务：${task.processName}`">
                <div class="tpl-upload">
                  <div class="upload-select">
                    <input :ref="'taskFile_' + task.id" type="file" class="upload-select-input" @change="handleFileChange($event, 'task_' + task.id)">
                    <el-button
                      v-permission="'manage.testing.testProcesses.testProcessesDetail.testReportTemplate.testProcessesUploadFile'"
                      v-if="!formData['task_' + task.id] || !formData['task_' + task.id].fileName"
                      class="upload-click-wrap"
                      @click="handleUploadClick('task_' + task.id)">上传文件</el-button>
                    <el-progress
                      v-if="formData['task_' + task.id] && formData['task_' + task.id].progress > 0 && formData['task_' + task.id].progress < 100"
                      :percentage="formData['task_' + task.id].progress"
                      :show-text="true"
                    />
                    <div v-show="formData['task_' + task.id] && formData['task_' + task.id].fileName" :style="{ 'border-color': formData['task_' + task.id] && formData['task_' + task.id].fileError ? '#F56C6C' : 'var(--color-601-border)', 'background-color': formData['task_' + task.id] && formData['task_' + task.id].fileError ? '#fff6f4' : 'var(--color-601-background)' }" class="file-container">
                      <i :style="{ 'color': formData['task_' + task.id] && formData['task_' + task.id].fileError ? '#F56C6C' : '#909399' }" class="el-icon-document" size="16" />
                      <div :title="formData['task_' + task.id] && formData['task_' + task.id].fileName" class="file-name">{{ formData['task_' + task.id] && formData['task_' + task.id].fileName || '' }}</div>
                      <div class="action-buttons">
                        <el-button title="预览" type="text" icon="el-icon-view" @click="handleFileView(`task_${task.id}`, formData['task_' + task.id])" />
                        <el-button
                          v-permission="'manage.testing.testProcesses.testProcessesDetail.testReportTemplate.testProcessesUpdateFile'"
                          title="更新" type="text" icon="el-icon-upload2"
                          @click="handleUploadClick('task_' + task.id)"
                        />
                        <el-button title="下载" type="text" icon="el-icon-download" @click="handleFileDownload('task_' + task.id)" />
                        <el-button
                          v-permission="'manage.testing.testProcesses.testProcessesDetail.testReportTemplate.testProcessesDeleteFile'"
                          title="删除" type="text" icon="el-icon-delete"
                          @click="handleFileDelete(`task_${task.id}`, formData['task_' + task.id].fileName)"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
              <!-- 含有子任务 -->
              <div v-else :key="'subtask_' + taskIndex" class="xinChuang-section">
                <div class="section-header">
                  <i :class="['el-icon-caret-' + (taskCollapse[taskIndex] ? 'top' : 'bottom')]" style="color: #CBD0DB;" @click="toggleTaskCollapse(taskIndex)" />
                  <div :title="task.processName" class="section-title">{{ `任务：${task.processName}` }}</div>
                  <div v-show="!taskCollapse[taskIndex]" :style="{ 'height': (45 * task.children.length + 10) + 'px' }" class="sub-line" />
                </div>
                <transition name="fade">
                  <div v-show="!taskCollapse[taskIndex]" class="sub-task-wrap">
                    <el-form-item v-for="(subTask, subIndex) in task.children" :key="subIndex" :title="subTask.processName" :label="`子任务：${subTask.processName}`">
                      <div class="tpl-upload">
                        <div class="upload-select">
                          <input :ref="'subTaskFile_' + subTask.id" type="file" class="upload-select-input" @change="handleFileChange($event, 'subTask_' + subTask.id)">
                          <el-button
                            v-permission="'manage.testing.testProcesses.testProcessesDetail.testReportTemplate.testProcessesUploadFile'"
                            v-if="!formData['subTask_' + subTask.id] || !formData['subTask_' + subTask.id].fileName"
                            class="upload-click-wrap"
                            @click="handleUploadClick('subTask_' + subTask.id)">上传文件</el-button>
                          <el-progress
                            v-if="formData['subTask_' + subTask.id] && formData['subTask_' + subTask.id].progress > 0 && formData['subTask_' + subTask.id].progress < 100"
                            :percentage="formData['subTask_' + subTask.id].progress"
                            :show-text="true"
                          />
                          <div
                            v-show="formData['subTask_' + subTask.id] && formData['subTask_' + subTask.id].fileName"
                            :style="{ 'border-color': formData['subTask_' + subTask.id] && formData['subTask_' + subTask.id].fileError ? '#F56C6C' : 'var(--color-601-border)', 'background-color': formData['subTask_' + subTask.id] && formData['subTask_' + subTask.id].fileError ? '#fff6f4' : 'var(--color-601-background)' }"
                            class="file-container"
                          >
                            <i :style="{ 'color': formData['subTask_' + subTask.id] && formData['subTask_' + subTask.id].fileError ? '#F56C6C' : '#909399' }" class="el-icon-document" size="16" />
                            <div :title="formData['subTask_' + subTask.id] && formData['subTask_' + subTask.id].fileName" class="file-name">{{ formData['subTask_' + subTask.id] && formData['subTask_' + subTask.id].fileName || '' }}</div>
                            <div class="action-buttons">
                              <el-button title="预览" type="text" icon="el-icon-view" @click="handleFileView(`subTask_${subTask.id}`, formData['subTask_' + subTask.id])" />
                              <el-button
                                v-permission="'manage.testing.testProcesses.testProcessesDetail.testReportTemplate.testProcessesUpdateFile'"
                                title="更新" type="text" icon="el-icon-upload2"
                                @click="handleUploadClick('subTask_' + subTask.id)"
                              />
                              <el-button title="下载" type="text" icon="el-icon-download" @click="handleFileDownload('subTask_' + subTask.id)" />
                              <el-button
                                v-permission="'manage.testing.testProcesses.testProcessesDetail.testReportTemplate.testProcessesDeleteFile'"
                                title="删除" type="text" icon="el-icon-delete"
                                @click="handleFileDelete(`subTask_${subTask.id}`, formData['subTask_' + subTask.id].fileName)"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-form-item>
                  </div>
                </transition>
              </div>
            </template>
          </el-form>
        </detail-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import detailCard from '@/packages/detail-view/detail-card.vue'
import { getTaskProcessesByIdApi, uploadTestProcessesFile, getTestProcessesFileById, deleteTestProcessesFile } from '@/api/testing/testProcesses.js'
import filePreview from '@/components/testing/utils/filePreview'
export default {
  name: 'DetailTestReport',
  components: {
    detailCard
  },
  mixins: [filePreview],
  props: {
    id: {
      type: [Number, String]
    },
    data: {
      type: Object
    }
  },
  data() {
    return {
      taskCollapse: [],
      formData: {
        report: {
          fileUrl: '',
          fileName: '',
          fileError: false,
          progress: 0 // 进度条
        }
      },
      previewUrl: '',
      testProcessTaskBOList: [] // 任务列表
    }
  },
  mounted() {
    if (this.id) {
      // 查询检测流程任务信息
      getTaskProcessesByIdApi(this.id).then((res) => {
        if (res.data && [0, 200].includes(res.code)) {
          this.testProcessTaskBOList = res.data || []
          // 初始化任务折叠状态
          this.taskCollapse = this.testProcessTaskBOList.map(() => false)
          // 初始化表单数据
          this.initFormData()
          // 处理文件回显
          this.handleFileDisplay()
        }
      })
    }
    this.getTestProcessesFileFn()
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      this.testProcessTaskBOList.forEach(task => {
        if (!task.children || task.children.length === 0) {
          this.$set(this.formData, `task_${task.id}`, {
            fileUrl: '',
            fileName: '',
            fileType: '',
            fileError: false,
            progress: 0
          })
        } else {
          task.children.forEach(subTask => {
            this.$set(this.formData, `subTask_${subTask.id}`, {
              fileUrl: '',
              fileName: '',
              fileType: '',
              fileError: false,
              progress: 0
            })
          })
        }
      })
    },
    // 处理文件回显
    handleFileDisplay() {
      this.testProcessTaskBOList.forEach(task => {
        // 处理父任务文件
        if (task.sysCoreFileVO) {
          const fileKey = `task_${task.id}`
          this.$set(this.formData, fileKey, {
            fileUrl: task.sysCoreFileVO.path,
            fileName: task.sysCoreFileVO.name,
            fileType: task.sysCoreFileVO.fileType,
            fileError: false
          })
        }

        // 处理子任务文件
        if (task.children && task.children.length > 0) {
          task.children.forEach(subTask => {
            if (subTask.sysCoreFileVO) {
              const fileKey = `subTask_${subTask.id}`
              this.$set(this.formData, fileKey, {
                fileUrl: subTask.sysCoreFileVO.path,
                fileName: subTask.sysCoreFileVO.name,
                fileType: subTask.sysCoreFileVO.fileType,
                fileError: false
              })
            }
          })
        }
      })
    },
    // 获取检测检测报告模板文件
    getTestProcessesFileFn() {
      const formData = new FormData()
      formData.append('sourceId', this.id)
      getTestProcessesFileById(formData).then((res) => {
        if ([0, 200].includes(res.code)) {
          if (res.data) {
            const { name, path, fileType } = res.data[0] || {}
            this.formData.report.fileName = name
            this.formData.report.fileUrl = path
            this.formData.report.fileType = fileType
          }
        }
      })
    },
    // 文件变更处理
    handleFileChange(e, type) {
      const files = e.target.files
      if (!files || !files.length) return
      const file = files[0]
      if (file.size > 1024 * 1024 * 600) {
        this.formData[type].fileError = true
        this.$message.error('文件大小超出限制（600MB）')
        return
      }
      this.formData[type].fileError = false
      this.$set(this.formData[type], 'progress', 0) // 重置进度
      const formData = new FormData()
      formData.append('file', file)
      formData.append('name', file.name)
      if (type == 'report') {
        formData.append('type', 1)
        formData.append('sourceId', this.id)
      } else {
        formData.append('type', 2)
        formData.append('sourceId', type.split('_')[1])
      }
      // 处理上传进度事件
      const config = {
        onUploadProgress: (e) => {
          if (e.total) {
            const percent = Math.round((e.loaded / e.total) * 100)
            this.$set(this.formData[type], 'progress', percent)
          }
        }
      }
      uploadTestProcessesFile(formData, config).then((res) => {
        if (res.data && [0, 200].includes(res.code)) {
          this.formData[type].fileName = file.name
          this.formData[type].fileUrl = res.data.path
          this.formData[type].fileType = res.data.fileType
          this.formData[type].progress = 100
        } else {
          this.formData[type].fileError = true
          this.$message.error(res.data.msg || '上传失败')
        }
      }).catch(() => {
        this.formData[type].fileError = true
        this.$message.error('上传失败')
      })
    },
    // 点击上传按钮
    handleUploadClick(type) {
      // 检测报告模板的特殊处理
      if (type === 'report') {
        this.$refs.reportFile.click()
        this.$refs.reportFile.value = ''
        return
      }
      // 任务和子任务的处理
      const taskId = type.split('_')[1]
      const refName = type.startsWith('subTask_') ? `subTaskFile_${taskId}` : `taskFile_${taskId}`
      const inputEl = this.$refs[refName]
      if (Array.isArray(inputEl)) {
        // 如果 ref 返回的是数组，取第一个元素
        inputEl[0].click()
        inputEl[0].value = ''
      } else if (inputEl) {
        // 如果 ref 返回的是单个元素
        inputEl.click()
        inputEl.value = ''
      }
    },
    // 查看文件
    handleFileView(type, file) {
      let fileType = file.fileType
      if (fileType) {
        if (
          file &&
          !this.fileCouldPreview.includes(fileType.toLowerCase())
        ) {
          this.$message.warning('该文件不支持预览，请下载查看')
          return
        }
      }
      if (!fileType) {
        if (file.fileName) {
          fileType = file.fileName.split('.')[file.fileName.split('.').length - 1]
        }
        if (file.name) {
          fileType = file.name.split('.')[file.name.split('.').length - 1]
        }
        if (
          file &&
          !this.fileCouldPreview.includes(fileType.toLowerCase())
        ) {
          this.$message.warning('该文件不支持预览，请下载查看')
          return
        }
      }
      // 判断是否为xlsx类型
      const isXlsx = fileType && fileType.toLowerCase() === 'xlsx'
      if (this.formData[type] && this.formData[type].fileUrl) {
        const fileUrl = window.ADMIN_CONFIG.VIP_URL + this.formData[type].fileUrl
        if (isXlsx) {
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(fileUrl)) + '&officePreviewType=html'
        } else {
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(fileUrl))
        }
        window.open(this.previewUrl)
      }
    },
    // 下载文件
    handleFileDownload(type) {
      if (this.formData[type] && this.formData[type].fileUrl) {
        fetch(this.formData[type].fileUrl, {
          method: 'get',
          responseType: 'blob'
        })
          .then((response) => response.blob())
          .then((blob) => {
            const a = document.createElement('a')
            const URL = window.URL || window.webkitURL
            const herf = URL.createObjectURL(blob)
            a.href = herf
            a.download = this.formData[type] && this.formData[type].fileName
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(herf)
          })
      }
    },
    // 删除文件
    handleFileDelete(type, name) {
      this.$confirm(`请确认是否删除“${name}”文件模板？`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning'
      }).then(() => {
        const formData = new FormData()
        if (type == 'report') {
          formData.append('sourceId', this.id)
        } else {
          formData.append('sourceId', type.split('_')[1])
        }
        deleteTestProcessesFile(formData).then((res) => {
          if (res.code === 0 || res.code === 200) {
            this.$message.success('删除成功')
            this.formData[type] = {
              fileUrl: '',
              fileName: '',
              fileType: '',
              fileError: false
            }
          }
        })
      }).catch(() => {
        console.info('已取消删除')
      })
    },
    // 切换任务折叠状态
    toggleTaskCollapse(index) {
      this.$set(this.taskCollapse, index, !this.taskCollapse[index])
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .detail-card-body {
  max-height: 70vh;
  overflow-y: auto;
}
::v-deep .test-report .el-form-item:first-child {
  .el-form-item__label {
    font-weight: bold;
  }
}
::v-deep.sub-task-wrap .el-form-item__label {
  padding-left: 14px !important;
}
::v-deep.el-form .el-form-item__label {
  color: #333;
  font-size: 14px;
  font-weight: normal;
}
.tpl-upload {
  .upload-select-input {
    display: none;
  }
  .upload-click-wrap {
    display: inline-block;
  }
  .file-container {
    overflow-wrap: break-word;
    word-break: normal;
    line-height: 1.5;
    border: 1px solid;
    padding: 6px 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;
    width: 320px;
    display: flex;
    justify-content: flex-start;
    .file-name {
      margin-left: 5px;
      width: 200px;
      overflow: hidden;
      color: #000000D9;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .el-icon-document {
      margin-top: 3px;
    }
    .action-buttons {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      gap: 5px;
      .el-button {
        padding: 0;
        margin: 0;
        border: none;
        color: var(--color-600);
        &.is-disabled {
          cursor: not-allowed;
          color: #909399;
          border: none;
        }
      }
    }
  }
}

.xinChuang-section {
  margin-bottom: 5px;
  .section-header {
    display: flex;
    align-items: center;
    margin: 0 0 10px -20px;
    color: #333;
    height: 35px;
    position: relative;
    .section-title {
      margin: 0 0 0 2px;
      width: calc(50% - 10px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    i {
      margin-left: 2px;
      cursor: pointer;
      font-size: 15px;
      transition: transform 0.3s;
    }
    .sub-line {
      position: absolute;
      left: 9px;
      top: 28px;
      width: 1px;
      background-color: #CBD0DB;
    }
  }
}

.fade-enter-active, .fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

::v-deep .task-title {
  .el-form-item__label,
  span {
    color: #333;
    font-size: 14px;
    font-weight: bold;
  }
}
::v-deep .el-form .el-form-item {
  margin-bottom: 5px !important;
  align-items: center;
  .el-form-item__label {
    height: 36px;
    line-height: 36px !important;
    padding: 0;
    width: 50% !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .el-form-item__content {
    min-height: 36px;
    display: flex;
    align-items: center;
    .upload-click-wrap {
      margin: 4px 0;
    }
  }
}
::v-deep .el-progress {
  width: 336px;
  margin: 5px 0;
  .el-progress-bar .el-progress-bar__outer {
    height: 10px !important;
  }
}
</style>
