<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert
      :closable="false"
      type="warning"
      title="此操作用于在测试完成之后将项目相关信息、关联资源、测试报告、附件等归入档案库保存，不可撤销，请谨慎操作。"
    />

    <div class="info-box">
      <div class="info-item">
        <span class="label">检测项目</span>
        <span class="value">{{ data[0].name }}</span>
      </div>
      <div class="info-item">
        <span class="label">当前状态</span>
        <span class="value">
          <el-badge :type="getStatusClass(data[0].status)" is-dot/>
          {{ getStatusLabel(data[0].status) }}
        </span>
      </div>

      <div class="info-item">
        <span class="label">下载文件</span>
        <div class="value">
          <el-checkbox v-model="formData.reportDownload">测试报告</el-checkbox>
          <el-checkbox v-model="formData.attachmentDownload">附件</el-checkbox>
        </div>
      </div>
    </div>

    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import { archiveProjectAPI, archiveDownZipAPI } from '@/api/testing/index'
import { downloadExcelWithResData } from '@/utils'
export default {
  name: 'ActionArchive',
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      formData: {
        reportDownload: true,
        attachmentDownload: true
      },
      statusMap: {
        2: { label: '测试通过', type: 'success' },
        3: { label: '测试不通过', type: 'danger' }
      }
    }
  },
  computed: {
    // 筛选返回可操作数据
    availableArr() {
      // 只有完成状态的项目才能归档
      return this.data.filter(item => item.status === '3')
    }
  },
  methods: {
    getStatusLabel(status) {
      return this.statusMap[status] && this.statusMap[status].label || '-'
    },
    getStatusClass(status) {
      return this.statusMap[status] && this.statusMap[status].type || 'info'
    },
    close() {
      this.$emit('close')
    },
    confirm() {
      if (!this.data || !this.data.length) {
        this.$message.warning('没有可归档的项目')
        return
      }
      this.loading = true
      // 归档参数
      const data = {
        reportDownload: this.formData.reportDownload,
        attachmentDownload: this.formData.attachmentDownload
      }
      const id = this.data[0].id
      // 下载zip参数
      const { reportDownload, attachmentDownload } = this.formData
      let type = null
      if (reportDownload && attachmentDownload) {
        type = 3
      } else if (reportDownload) {
        type = 1
      } else if (attachmentDownload) {
        type = 2
      }
      const params = { type }

      // 调用API
      archiveProjectAPI(id, data).then(res => {
        if (res.data.code === 0) {
          this.$message.success('归档成功')
          this.$emit('call', 'refresh')
          this.close()
          if (params.type) {
            archiveDownZipAPI(id, params).then(res => {
              downloadExcelWithResData(res)
            })
          }
        } else {
          this.$message.error(res.data.msg || '归档失败')
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.info-box {
  margin: 20px 0;
}

.info-item {
  display: flex;
  margin-bottom: 15px;

  .label {
    width: 100px;
    text-align: right;
    color: #606266;
    line-height: 32px;
    padding-right: 12px;
  }

  .value {
    flex: 1;
    line-height: 32px;
  }

  .info {
    color: #909399;
  }

  .warning {
    color: #e6a23c;
  }

  .success {
    color: #67c23a;
  }

  .danger {
    color: #f56c6c;
  }
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}
</style>
