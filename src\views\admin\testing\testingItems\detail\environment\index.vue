<template>
  <div class="content-wrap-layout">
    <div class="switch-group">
      <el-radio-group v-model="activeName" size="small" class="common-radio-group" @input="changeType">
        <el-radio-button label="1">设备列表</el-radio-button>
        <el-radio-button v-if="envType === 0 && resourceType === 1" label="2">拓扑</el-radio-button>
      </el-radio-group>
    </div>
    <div class="vertical-wrap">
      <template v-if="activeName == '1'">
        <!-- 网络编排列表 -->
        <page-table
          v-if="envType == 0 && resourceType == 1 && resourceApplyFlag !== undefined"
          ref="table"
          :default-selected-arr="defaultSelectedArr"
          :cache-pattern="true"
          :env-type="'network'"
          :data="data"
          :resource-apply-flag="resourceApplyFlag"
          default-selected-key="id"
          @refresh="refresh"
          @link-event="linkEvent"
          @on-select="tabelSelect"
          @on-current="tabelCurrent"
          @jumpToOption="handleJumpToOption"
        >
          <action-menu
            slot="action"
            :module-name="moduleName"
            :select-item="selectItem"
            :env-type="'network'"
            :deployed="deployed"
            :redeploy="redeploy"
            :data="data"
            @call="actionHandler"
          />
        </page-table>
        <!-- 虚拟机列表 -->
        <qemuPageTable
          v-else-if="envType == 0 && resourceType == 0"
          ref="table"
          :default-selected-arr="defaultSelectedArr"
          :cache-pattern="true"
          :env-type="'virtual'"
          :deployed="deployed"
          :redeploy="redeploy"
          :data="data"
          default-selected-key="id"
          @refresh="refresh"
          @link-event="linkEvent"
          @on-select="tabelSelect"
          @on-current="tabelCurrent"
          @jumpToOption="handleJumpToOption"
        >
          <action-menu
            slot="action"
            :module-name="moduleName"
            :select-item="selectItem"
            :env-type="'virtual'"
            :deployed="deployed"
            :redeploy="redeploy"
            :data="data"
            @call="actionHandler"
          />
        </qemuPageTable>
        <!-- 外部部署列表 -->
        <externalPageTable
          v-else-if="envType == 1 && resourceApplyFlag !== undefined"
          ref="table"
          :default-selected-arr="defaultSelectedArr"
          :cache-pattern="true"
          :data="data"
          :resource-apply-flag="resourceApplyFlag"
          default-selected-key="nodeId"
          @refresh="refresh"
          @link-event="linkEvent"
          @on-select="tabelSelect"
          @on-current="tabelCurrent"
          @jumpToOption="handleJumpToOption"
        />
      </template>
      <!-- 拓扑 -->
      <template v-if="activeName === '2'">
        <topology ref="topology" :data="topologyData" @topologyIdChanged="handleTopologyIdChanged"/>
      </template>
    </div>
  </div>
</template>

<script>
import moduleConf from './config'
import pageTable from './table/index.vue'
import qemuPageTable from './table/qemu.vue'
import externalPageTable from './table/external.vue'
import actionMenu from './action/index.vue'
import topology from './topology/index.vue'
import { getSceneInfoTopo, getEnvType, instanceConsole, taskInstanceConsole, queryResourceApplyAPI } from '@/api/testing/index'
export default {
  // 测试环境
  name: 'EnvironmentList',
  components: {
    pageTable,
    qemuPageTable,
    externalPageTable,
    actionMenu,
    topology
  },
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      noDataImg: require('@/packages/table-view/nodata.png'),
      moduleConf,
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      activeName: '1', // 1.设备列表 2.拓扑
      topologyData: null,
      topologyId: '',
      envType: '',
      deployed: 0, // 0: 未完成 1: 完成
      redeploy: null, // 重新部署
      resourceType: '',
      apiType: instanceConsole, // 默认调用项目控制台
      resourceApplyFlag: undefined
    }
  },
  created() {
    // 清除cookie，防止影响jms
    document.cookie.split(';').forEach(cookie => {
      const [rawKey] = cookie.split('=')
      const key = rawKey.trim()
      const elseKeys = ['SESSION_COOKIE_NAME_PREFIX', 'X-JMS-LUNA-ORG']
      if (key.includes('jms') || elseKeys.includes(key)) {
        // 设置过期时间为过去，删除该 cookie
        document.cookie = `${key}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`
      }
    })
    this.getResourceApplyList()
      .then(() => this.getProjectEnvType())
      .catch(error => {
        console.error('获取资源申请列表失败:', error)
      })
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
          break
        case 'refreshDeploy':
          // 刷新状态
          this.getProjectEnvType()
          this.$refs['table'].getList()
          break
      }
    },
    refresh: function() {
    },
    changeType(val) {
      this.activeName = val
    },
    // 处理拓扑ID变更
    handleTopologyIdChanged(data) {
      if (data) {
        this.topologyId = data.topologyTemplateId
        // 如果当前在设备列表页，刷新列表
        if (this.activeName === '1' && this.$refs['table']) {
          this.$refs['table'].getList(true)
        }
      }
    },
    // 跳转到设备操作页面
    handleJumpToOption(device) {
      const params = {
        vmId: device.id,
        projectId: this.data.id,
        ip: device.target || device.deviceIp
      }
      if (this.$route.name == 'testingTask_detail') {
        params.projectId = this.$route.params.projectId
        params.taskId = this.$route.params.id
        params.ip = device.target || device.deviceIp
        this.apiType = taskInstanceConsole
      }
      this.apiType(params).then((res) => {
        window.open(res.data.data, '_blank')
      })
    },
    // 获取申请资源列表
    getResourceApplyList() {
      return new Promise((resolve, reject) => {
        const params = {
          page: 1,
          limit: 9999,
          pageType: 1,
          projectId: this.$route.params.projectId || this.data.id
        }
        queryResourceApplyAPI(params).then((res) => {
          if (res.data && res.data.code === 0) {
            this.resourceApplyFlag = res.data.data.records.some(item => item.auditStatus == '0')
            resolve()
          } else {
            reject(new Error('获取资源申请列表失败'))
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    getProjectEnvType() {
      getEnvType(this.$route.params.projectId || this.data.id).then(res => {
        const data = { ...res.data.data }
        this.envType = data.envType
        this.deployed = data.deployed
        this.redeploy = data.redeploy
        this.resourceType = data.resourceType
        if (data && data.envType == 0 && data.resourceType == 1 && this.resourceApplyFlag == false) {
          this.getTopoInfo()
        }
      })
    },
    getTopoInfo() {
      getSceneInfoTopo(this.$route.params.projectId || this.data.id).then(res => {
        const data = { ...res.data.data }
        this.topologyData = data
        this.topologyId = data.topologyTemplateId
      }).catch(error => {
        console.error('获取拓扑信息出错:', error)
        this.$message.error('获取拓扑信息出错')
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-radio-group.common-radio-group {
  border-radius: 10px;
  border: none;
  padding: 2px;
  background-color: #F5F6F9;
  .el-radio-button {
    width: 90px;
    .el-radio-button__inner {
      width: 100%;
      background-color: #F5F6F9;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: bold;
      color: var(--neutral-700);
    }
  }
  .is-active {
    .el-radio-button__inner {
      background-color: #fff;
      color: var(--color-600);
      border-radius: 8px;
      font-size: 14px;
      border: none;
      font-weight: bold;
    }
  }
  .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    box-shadow: none;
    -webkit-box-shadow: none;
  }
}
.wrapper {
  padding: 5px 0 0 0 !important;
}
</style>
