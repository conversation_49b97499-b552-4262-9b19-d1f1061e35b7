<template>
  <div class="buttons-wrap">
    <!-- 测试中、测试通过及测试不通过才可点击 -->
    <el-button
      v-permission="'manage.testing.project.projectDetail.projectTask.taskDetailView.taskIssue.problemSave'"
      v-if="projectStatus"
      :disabled="![1, 2, 3].includes(projectStatus) || data.pendingStatus == 9 || detailData.pendingStatus == 9"
      type="primary"
      @click="clickDrop('createQuestion')"
    >提交问题</el-button>
    <el-button
      v-permission="'manage.testing.project.projectDetail.projectTask.taskDetailView.taskIssue.problemSave'"
      v-else
      :disabled="![1, 2, 3].includes(taskStatus) || data.pendingStatus == 9 || detailData.pendingStatus == 9"
      type="primary"
      @click="clickDrop('createQuestion')"
    >提交问题</el-button>
    <el-button
      v-permission="'manage.testing.project.projectDetail.projectTask.taskDetailView.taskIssue.problemExport'"
      type="primary"
      @click="clickDrop('exportItem')"
    >导出</el-button>
    <!-- 弹窗 -->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      destroy-on-close
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          :project-data="data"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 侧拉弹窗 start -->
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          :data="selectItem"
          :project-data="data"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>
    <!-- 侧拉弹窗 end -->
  </div>
</template>

<script>
import { getProjectStatusApi, getTaskStatusApi } from '@/api/testing/index'
import exportItem from './modal-exportItem.vue'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
export default {
  name: 'ActionMenu',
  components: {
    exportItem
  },
  mixins: [mixinsActionMenu],
  props: {
    detailData: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: Object,
      default: () => ({})
    },
    moduleName: {
      type: String,
      default: ''
    },
    selectItem: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      drawerAction: [], // 需要侧拉打开的操作
      // 弹窗title映射
      titleMapping: {
        'exportItem': '导出'
      },
      projectStatus: null,
      taskStatus: null
    }
  },
  created() {
    if (this.$route.name == 'testing_detail') {
      this.getProjectStatus()
    }
    if (this.$route.name == 'testingTask_detail') {
      this.getTaskStatus()
    }
  },
  methods: {
    // 获取项目状态
    getProjectStatus() {
      const data = {
        id: this.$route.params.id
      }
      getProjectStatusApi(data).then(res => {
        this.projectStatus = res.data.data
      })
    },
    // 获取任务状态
    getTaskStatus() {
      const data = {
        id: this.$route.params.id
      }
      getTaskStatusApi(data).then(res => {
        this.taskStatus = res.data.data
      })
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    drawerConfirmCall: function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    'clickDrop': function(name) {
      if (name == 'createQuestion') {
        const params = {}
        if (this.$route.name == 'testing_detail') {
          params.projectId = this.$route.params.id
        }
        if (this.$route.name == 'testingTask_detail') {
          params.taskId = this.$route.params.id
          params.projectId = this.$route.params.projectId
        }
        params.type = this.$route.name
        const url = this.$router.resolve({
          name: 'createTestingQuestion',
          query: params
        }).href
        window.open(url, '_blank')
      } else {
        this.modalName = name
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.action-menu {
  display: inline-block;
}
</style>
