<template>
  <div v-loading="loading" class="course-detail-wrap">
    <!-- 随堂练习的考试模式下-开始考试 -->
    <div v-if="contentInfo.isExamMode == 1 && isAnswerStatus == 1" class="mask">
      <el-button v-if="!loading" type="primary">正在考试中</el-button>
    </div>
    <!-- 随堂练习的考试模式下-开始考试 -->
    <div v-if="contentInfo.isExamMode == 1 && isAnswerStatus == 1" class="mask">
      <el-button type="primary" @click="changeView('examination')">跳转至考试界面</el-button>
    </div>
    <div class="detail-left">
      <div class="course-table">课程目录</div>
      <div class="course-name">{{ courseInfo.name }}</div>
      <div class="chapter-unit">
        <div v-for="(item, index) in courseInfo.chapterUnitList" :key="index" style="margin: 0 10px;">
          <div :class="{'active': chapterIdNew == item.id}" class="course-title" >
            第{{ index + 1 }}章 | {{ item.name }}
          </div>
          <div v-for="(unit, unitIndex) in item.unitList" :key="unit.id" class="course-unit">
            <div :class="{'active': unitIdNew == unit.id}" class="unit-name" >
              第{{ unitIndex + 1 }}单元 | {{ unit.name }}
              <svg v-if="unit.isFoled" t="1748611917389" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14725" width="20" height="20" @click="handleFoled(unit)"><path d="M877.714286 768c0 21.942857-14.628571 36.571429-36.571429 36.571429h-585.142857c-21.942857 0-36.571429-14.628571-36.571429-36.571429v-585.142857c0-21.942857 14.628571-36.571429 36.571429-36.571429h585.142857c21.942857 0 36.571429 14.628571 36.571429 36.571429v585.142857zM841.142857 73.142857h-585.142857C197.485714 73.142857 146.285714 124.342857 146.285714 182.857143v585.142857c0 58.514286 51.2 109.714286 109.714286 109.714286h585.142857c58.514286 0 109.714286-51.2 109.714286-109.714286v-585.142857c0-58.514286-51.2-109.714286-109.714286-109.714286z m-146.285714 365.714286H585.142857V329.142857c0-21.942857-14.628571-36.571429-36.571428-36.571428s-36.571429 14.628571-36.571429 36.571428V438.857143H402.285714c-21.942857 0-36.571429 14.628571-36.571428 36.571428s14.628571 36.571429 36.571428 36.571429H512v109.714286c0 21.942857 14.628571 36.571429 36.571429 36.571428s36.571429-14.628571 36.571428-36.571428V512h109.714286c21.942857 0 36.571429-14.628571 36.571428-36.571429S716.8 438.857143 694.857143 438.857143z" fill="#6e8eca" p-id="14726" /></svg>
              <svg v-else t="1748611879935" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13682" width="20" height="20" @click="handleFoled(unit)"><path d="M832 128a64 64 0 0 1 64 64v640a64 64 0 0 1-64 64H192a64 64 0 0 1-64-64V192a64 64 0 0 1 64-64h640z m0 64H192v640h640V192z m-144 288a16 16 0 0 1 16 16v32a16 16 0 0 1-16 16h-352A16 16 0 0 1 320 528v-32a16 16 0 0 1 16-16z" fill="#6e8eca" p-id="13683" /></svg>
            </div>
            <div v-for="(content, contentIndex) in unit.contentList" :key="content.id" :class="{'is-active': content.id == id, 'foled': unit.isFoled }" class="course-content" @click="courseChange(item.id, unit.id, content.id, content)">
              <div class="content-info">
                <div class="content-name">
                  <div class="mr-5">{{ index + 1 }}.{{ unitIndex + 1 }}.{{ contentIndex + 1 }}</div>
                  {{ content.name }}
                </div>
              </div>
              <div>
                {{ content.contentPeriod }}课时
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="detail-right">
      <div class="detail-func">
        <div v-if="showLiveTelecast" :class="{'is-active': viewName == 'liveTelecast'}" class="switch-tags" @click="changeView('liveTelecast')">
          <i class="cr-icon-zhibozhibo" />直播
        </div>
        <div v-if="showObj.isHaveVideo" :class="{'is-active': viewName == 'video', 'is-disabled' : liveStatus == 1 && roleType == 2}" class="switch-tags" @click="changeView('video')">
          <i class="cr-icon-shipin" />视频
        </div>
        <div v-if="showObj.isHaveCourseware" :class="{'is-active': viewName == 'document', 'is-disabled' : liveStatus == 1 && roleType == 2}" class="switch-tags" @click="changeView('document')">
          <i class="cr-icon-wendang" />文档
        </div>
        <div v-if="showExperiment" :class="{'is-active': viewName == 'experiment', 'is-disabled' : liveStatus == 1 && roleType == 2}" class="switch-tags" @click="changeView('experiment')">
          <i class="cr-icon-diannao" />实验
        </div>
        <div v-if="(showObj.isHavePractice && roleType == 2) || (showObj.isHavePractice && editMode == 1)" :class="{'is-active': viewName == 'examination', 'is-disabled' : (liveStatus == 1 && roleType == 2)}" class="switch-tags" @click="changeView('examination')">
          <i class="cr-icon-ceshi" />考试
        </div>
        <div v-if="showObj.isHavePackage" :class="{'is-active': viewName == 'annex', 'is-disabled' : liveStatus == 1 && roleType == 2}" class="switch-tags" @click="changeView('annex')">
          <i class="cr-icon-fujian" />附件
        </div>
        <div v-if="contentInfo.beginTime && contentInfo.endTime" class="time">
          <span>上课时间：</span>
          <i class="el-icon-time" />
          {{ `${contentInfo.beginTime} - ${contentInfo.endTime.split(' ')[1]}` }}
        </div>
        <el-button v-if="showIntroduce" class="go-bottom" @click="goCourse">课程介绍</el-button>
      </div>
      <div class="detail-content">
        <Video v-if="viewName == 'video' && id" :id="id"/>
        <report v-if="viewName == 'document' && id" :id="id" :is-download="false" show-package/>
        <liveTelecastUrl
          v-if="viewName == 'liveTelecast'"
          :content-name="showObj.name"
          :live-url="liveUrl"
          :role="roleType"
          :live-status="liveStatus"
          :scheduling-code="schedulingCode"
          :begin-time="contentInfo.beginTime"
          :end-time="contentInfo.endTime"
          type="查看"
        />
        <div v-if="viewName != 'liveTelecast' && viewName != 'video' && viewName != 'document'" style="height:100%;width:100%;">
          <div v-if="viewName == 'experiment'" class="experiment-tip">
            <i class="cr-icon-diannao" />
            <div>请点击上侧“实验”，可进行课程实验</div>
          </div>
          <div v-else-if="(showObj.isHavePractice && roleType == 2) || (showObj.isHavePractice && editMode == 1)" class="experiment-tip">
            <i class="cr-icon-ceshi" />
            <div>请点击上侧“考试”，可进行课程考试</div>
          </div>
          <div v-else-if="showObj.isHavePackage" class="experiment-tip">
            <i class="cr-icon-fujian" />
            <div>请点击上侧“附件”，可下载课程附件</div>
          </div>
          <el-empty
            v-else
            :image="img"
            :image-size="110"
            style="margin: 100px auto"
            description="暂无数据"
          />
        </div>
      </div>
    </div>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :id="id"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>

<script>
import Video from '@/components/courseDetail/Video'
import report from './report'
import annex from '@/components/courseDetail/annex'
import liveTelecastUrl from '@/components/liveTelecastUrl'
import { queryLive, queryPracticeUser, getCourseInfo, getContentInfo } from '@/api/teacher/index'

import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import { mapState } from 'vuex'
import { tabManager } from '@/packages/utils/tabManager.js'

export default {
  components: {
    Video,
    report,
    liveTelecastUrl,
    annex
  },
  mixins: [mixinsActionMenu],
  props: {
    oneLevelName: String,
    // 自学：1
    type: [String, Number],
    roleType: [String, Number],
    chapterId: [String, Number],
    unitId: [String, Number],
    contentId: [String, Number],
    content: Object,
    editMode: {
      type: [String, Number],
      default: 0
    },
    refresh: Boolean,
    showIntroduce: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      userInfo: {},
      img: require('@/assets/empty_state.png'),
      viewName: 0,
      practiceIndex: '1',
      requiredCourse: this.oneLevelName == 'requiredCourse', // 是必修课
      loading: true,
      practiceList: [],
      studentAnswerList: [],
      id: '',
      showObj: {},
      courseId: this.$route.query.courseId, // 课程id
      schedulingCode: this.$route.query.schedulingCode, // 排课id
      courseInfo: {}, // 课程信息
      contentInfo: {}, // 选择的课程内容信息
      liveUrl: '', // 直播地址
      liveStatus: 0, // 直播是否开启
      isAnswerStatus: 0, // 随堂练习-考试模式-是否开始考试
      chapterIdNew: '', // 选中的章节
      unitIdNew: '', // 选中的单元
      titleMapping: {
        'annex': '下载附件'
      }
    }
  },
  provide() {
    return {
      'parentVm': this
    }
  },
  computed: {
    ...mapState('websocketListener', [
      'training_liveStatus_Socket', // 教师开始关闭直播
      'training_isAnswerStatus_Socket' // 实训-随堂练习-考试模式下-是否开始考试
    ]),
    showQuestionBank() {
      let flag = false
      if (this.oneLevelName == 'selfStudyCourses') {
        flag = this.viewName == 2 && this.studentAnswerList.length > 0
      } else {
        flag = this.viewName == 2 && this.oneLevelName == 'requiredCourse'
      }
      return flag
    },
    // 显示直播
    showLiveTelecast() {
      return this.courseInfo.isRequiredCourse && !(this.roleType == 3) // 必修课且不是助教
    },
    // 显示实验
    showExperiment() {
      return this.showObj.contentType == 2 || (this.editMode == 1 && this.showObj.contentType == 2)
    },
    // 动态试卷暂不支持教师/助教查看题目详情
    cannotDynamicPaper() {
      return this.contentInfo.examType == 1 && this.roleType != '2'
    },
    showExercisesPaper() {
      return this.viewName == 2 && (this.oneLevelName != 'requiredCourse' && this.studentAnswerList.length == 0)
    }
  },
  watch: {
    contentId: {
      handler() {
        this.courseChange(this.chapterId, this.unitId, this.contentId, this.content)
      }
    },
    // 教师开始关闭直播
    'training_liveStatus_Socket': function(newVal, oldVal) {
      if (newVal && newVal.contentId == this.id) {
        this.liveStatus = newVal.liveStatus
      }
    },
    // 实训-随堂练习-考试模式下-是否开始考试
    'training_isAnswerStatus_Socket': function(newVal, oldVal) {
      if (newVal) {
        this.isAnswerStatus = newVal.isAnswerStatus
      }
    },
    refresh: {
      handler() {
        this.getCourseInfo()
      }
    }
  },
  mounted() {
    this.userInfo = JSON.parse(localStorage.getItem('loginUserInfo'))
    this.getCourseInfo()
    if (this.$route.query.practiceIndex) {
      this.practiceIndex = this.$route.query.practiceIndex
    }
  },
  methods: {
    goCourse() {
      this.el = document.querySelector('.el-scrollbar__wrap') // 获取default页面，监听滚动
      const targetbox = document.getElementById('course')
      const target = targetbox.offsetTop
      this.el.scrollTop = target
    },
    handleFoled(unit) {
      this.$set(unit, 'isFoled', !unit.isFoled)
    },
    // 设置viewName
    setViewName() {
      if (this.showLiveTelecast) {
        this.viewName = 'liveTelecast'
      } else if (this.showObj.isHaveVideo) {
        this.viewName = 'video'
      } else if (this.showObj.isHaveCourseware) {
        this.viewName = 'document'
      } else if (this.showExperiment) {
        this.viewName = 'experiment'
      } else if (this.showObj.isHavePractice) {
        this.viewName = 'examination'
      } else if (this.showObj.isHavePackage) {
        this.viewName = 'annex'
      }
    },
    // 获取课程内容信息
    getContentInfo() {
      let num = 0
      if (this.courseInfo.isRequiredCourse) {
        num = 1
      } else if (this.courseInfo.isElectiveCourse) {
        num = 2
      } else {
        num = 3
      }
      if (!this.id) {
        this.loading = false
        return
      }
      const postData = {
        courseId: this.$route.query.courseId,
        contentId: this.id,
        roleType: this.roleType,
        requiredOrElective: num,
        schedulingCode: this.schedulingCode,
        sectionTime: this.$route.query.sectionTime,
        sectionSeason: this.$route.query.sectionSeason
      }
      getContentInfo(postData).then(res => {
        // 查看拓扑所需要用到的参数
        this.contentInfo = res.data
        this.schedulingCode = res.data.schedulingCode
        this.liveStatus = res.data.liveStatus
        if (this.courseInfo.isRequiredCourse) {
          this.queryLive()
        } else {
          this.loading = false
        }
      }).catch(() => {
        this.loading = false
      })

      // 设置viewName
      this.setViewName()
    },
    // 随堂练习-考试模式-获取是否开始考试
    getCourseInfo() {
      const postData = {
        courseId: this.$route.query.courseId,
        roleType: this.roleType,
        schedulingCode: this.schedulingCode,
        sectionTime: this.$route.query.sectionTime,
        sectionSeason: this.$route.query.sectionSeason,
        editMode: this.editMode
      }
      getCourseInfo(postData).then(res => {
        this.courseInfo = res.data
        this.id = ''
        if (this.$route.query.contentId) {
          this.courseInfo.chapterUnitList.forEach(item => {
            item.unitList.forEach(unit => {
              // 课程为空时
              if (unit.contentList.length === 0) {
                this.showObj = {}
              } else {
                unit.contentList.forEach(content => {
                  if (this.$route.query.contentId == content.id) {
                    this.chapterIdNew = item.id
                    this.unitIdNew = unit.id
                    this.id = content.id
                    this.showObj = content
                    this.schedulingCode = content.schedulingCode
                  }
                })
              }
            })
          })
        } else {
          this.courseInfo.chapterUnitList.forEach(item => {
            item.unitList.forEach(unit => {
              // 课程为空时
              if (unit.contentList.length === 0) {
                this.showObj = {}
              } else {
                unit.contentList.forEach(content => {
                  if (content && !this.id) {
                    this.chapterIdNew = item.id
                    this.unitIdNew = unit.id
                    this.id = content.id
                    this.schedulingCode = content.schedulingCode
                    this.showObj = content
                  }
                })
              }
            })
          })
        }
        this.getContentInfo()
      }).catch(() => {
        this.loading = false
      })
    },
    courseChange(chapterId, unitId, contentId, content) {
      if (contentId == this.id) {
        return
      }
      this.chapterIdNew = chapterId
      this.unitIdNew = unitId
      this.id = contentId
      this.showObj = content
      this.schedulingCode = content.schedulingCode
      this.$emit('switch-courses', chapterId, unitId, contentId, content)
      this.getPracticeList()
      this.getContentInfo()
    },
    // getStudentAnswer() {
    //   const { courseId, contentId } = this.$route.query
    //   if (!courseId) {
    //     return
    //   }
    //   if (this.schedulingCode) {
    //     studentAnswer({ schedulingCode: this.schedulingCode }).then((res) => {
    //       if (res.code == 0) {
    //         this.studentAnswerList = res.data || []
    //         this.practiceList.forEach((k) => {
    //           const data = this.studentAnswerList.find(j => j.questionCode == k.questionCode)
    //           if (data) {
    //             this.$set(k, 'questionStudentScore', data.questionScore)
    //             this.$set(k, 'questionUserAnswer', data.questionUserAnswer)
    //           }
    //         })
    //       }
    //     })
    //   } else {
    //     studentSelfStudyAnswer({ courseId, contentId: contentId || this.id }).then((res) => {
    //       if (res.code == 0) {
    //         this.studentAnswerList = res.data || []
    //         this.practiceList.forEach((k) => {
    //           const data = this.studentAnswerList.find(j => j.questionCode == k.questionCode)
    //           if (data) {
    //             this.$set(k, 'questionStudentScore', data.questionScore)
    //             this.$set(k, 'questionUserAnswer', data.questionUserAnswer)
    //           }
    //         })
    //       }
    //     })
    //   }
    // },
    getPracticeList() {
      queryPracticeUser({ contentId: this.id || this.$route.query.id, courseCode: this.$route.query.courseId }).then(res => {
        if (res.code == 0) {
          this.practiceList = res.data || []
          if (!this.requiredCourse) {
            // this.getStudentAnswer()
          }
        }
      })
    },
    // 获取直播地址
    queryLive() {
      if (!this.schedulingCode) {
        this.loading = false
        return
      }
      queryLive({ roleType: this.roleType, schedulingCode: this.schedulingCode }).then(res => {
        if (res.code === 0 && res.data.length > 0) {
          this.liveUrl = res.data
          this.loading = false
        }
      }).catch(() => {
        this.loading = false
      })
    },
    // 视图切换
    changeView(name) {
      // 直播开始后不允许切换
      if (this.liveStatus == 1 && this.roleType == 2) {
        return
      }
      // 随堂练习-考试模式-开始后 不让看课件和视频
      if (name == 'annex') {
        this.modalShow = true
        this.modalName = 'annex'
        return
      }
      if (name == 'examination') {
        if (this.cannotDynamicPaper) {
          window.open(`/cannotDynamicPaper`, '_blank')
          return
        }
        if (this.studentAnswerList.length) {
          this.$message({
            message: `该考试已完成`,
            type: 'error'
          })
          return
        }
        let num = 0
        if (this.courseInfo.isRequiredCourse) {
          num = 1
        } else if (this.courseInfo.isElectiveCourse) {
          num = 2
        } else {
          num = 3
        }
        window.open(`/exercisesPaper?courseId=${this.$route.query.courseId}&curriculumCode=${this.id}&oneLevelName=${this.$route.query.oneLevelName}&schedulingId=${this.schedulingCode || ''}&sign=${num}&answerTime=${this.showObj.answerTime}&isExamMode=${this.contentInfo.isExamMode}&examType=${this.contentInfo.examType != '1' ? '' : this.contentInfo.examType}`, '_blank')
        return
      }
      if (name == 'experiment') {
        // 浏览器tab页签唯一值用课程id和课程内容id拼接
        const id = `experiment-${this.courseId}-${this.showObj.id}`
        const url =
          '/experiment_topo' +
          '?id=' + this.showObj.id +
          '&courseId=' + (this.courseId) +
          '&courseName=' + (this.courseInfo.name) +
          '&name=' + (this.showObj.name) +
          '&enterType=' + (this.$route.query.enterType || 'student') +
          '&topologyAllocation=' + (this.contentInfo.topologyAllocation) +
          '&roleId=' + (this.contentInfo.roleId || '') +
          '&schedulingId=' + (this.contentInfo.schedulingCode || '') +
          '&sceneInstanceId=' + (this.editMode == 1 ? this.showObj.sourceSceneId : this.contentInfo.sceneInstanceId || '')
        const existingRef = tabManager.getTabRef(id)
        if (existingRef && !existingRef.closed) {
          // 非当前打开者链的话，就新打开一个界面，不能打开空界面
          if (existingRef.location.href === 'about:blank') {
            existingRef.location.replace(url)
            setTimeout(() => existingRef.focus(), 300)
          } else {
            existingRef.focus()
          }
        } else {
          const newTab = window.open(url, `tab-${id}`)
          tabManager.setTabRef(id, newTab)
        }
        return
      }
      this.viewName = String(name)
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.course-detail-wrap{
  width: 95%;
  height: 700px;
  box-shadow: 0px 0px 20px #496694;
  border-radius: 4px;
  margin-top: 10px;
  overflow: hidden;
  position: relative;
  display: flex;
  ::v-deep .el-loading-mask {
    background-color: rgba(0, 0, 0, 0.9);
  }
  .detail-left {
    width: 27%;
    height: 100%;
    font-weight: 500;
    border-right: 1px solid rgba(38,203,211,.3);
    color: #fff;
    display: flex;
    flex-direction: column;
    >div {
      margin: 0 20px;
    }
    .course-table {
      margin-top: 13px;
      font-size: 18px;
      text-align: center;
      height: 24px;
    }
    .course-name {
      margin: 0;
      font-size: 16px;
      border-bottom: 1px solid rgba(38, 203, 211, 0.3);
      height: 48px;
      text-align: center;
      width: 100%;
      display: block;
      line-height: 48px;
    }
    .course-title {
      display: flex;
      min-height: 50px;
      align-items: center;
      font-size: 16px;
    }
    .chapter-unit {
      flex: 1;
      min-height: 0;
      margin: 0;
      overflow-y: auto;
      overflow-x: hidden;
      &::-webkit-scrollbar-track-piece {
        background: transparent;
      }
      &::-webkit-scrollbar-thumb {
        background-color: rgba(144, 147, 153, 0.3) !important;
      }
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
    }
    .course-unit {
      .unit-name {
        padding-left: 32px;
        font-size: 14px;
        padding-bottom: 10px;
        padding-top: 10px;
        padding-right: 25px;
        position: relative;
        svg {
          font-size: 16px;
          cursor: pointer;
          margin: 3px 5px;
          position: absolute;
          left: 5px;
          top: 8px;
        }
      }
    }
    .course-content {
      padding: 10px 20px 10px 40px;
      margin-bottom: 2px;
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      font-size: 12px;
      position: relative;
      border-radius: 5px;
      cursor: pointer;
      &.foled {
        display: none;
      }
      .content-info {
        flex: 1;
        display: flex;
        align-items: flex-start;
      }
      .content-name {
        flex: 1;
        margin-right: 5px;
        display: flex;
      }
      .in-progress {
        position: absolute;
        left: 18px;
        top: 10px;
        color: #67C23A;
        font-size: 20px;
      }
    }
    .course-content:hover {
      color: var(--color-700);
      background-color: var(--color-100);
    }
    .is-active {
      color: var(--color-700) !important;
      background-color: var(--color-100);
    }
    .is-disabled {
      cursor: not-allowed;
      color: #535360;
    }
    .is-disabled:hover {
      color: #535360;
    }
  }
  .detail-right {
    width: 73%;
    display: flex;
    flex-direction: column;
    .detail-func {
      position: relative;
      border-bottom: 1px solid rgba(38,203,211,.3);
      min-height: 80px;
      width: 100%;
      display: flex;
      align-items: center;
      padding: 10px 20px;
      .time {
        flex: 1;
        min-width: 0;
        color: #fff;
        font-size: 17px;
        font-weight: 500;
      }
      .switch-tags {
        color: #fff;
        font-size: 14px;
        cursor: pointer;
        display: flex;
        align-items: center;
        flex-direction: column;
        padding: 12px 18px 10px 18px;
        border-radius: 8px;
        margin-right: 15px;
        position: relative;
        &.is-active {
          color: var(--color-700);
          font-weight: 800;
          background-color: var(--color-100);
          i {
            font-weight: 800;
          }
        }
        &.is-disabled {
          cursor: not-allowed;
          color: #535360;
        }
        &.switch-tags:hover {
          color: var(--color-700);
        }
        &.is-disabled:hover {
          color: #535360;
        }
        &.is-active:hover {
          color: var(--color-700);
        }
        .examination-lock {
          position: absolute;
          font-size: 14px;
          right: -18px;
          bottom: 5px;
          cursor: text;
          &.active {
            cursor: pointer;
            color: var(--color-700);
          }
        }
        i {
          font-size: 20px;
        }
      }
      .go-bottom {
        width: 100px;
        height: 40px;
        background: url("../assets/goMatchBg.png") center no-repeat;
        background-size: 100% 100%;
        border-radius: 10px;
        font-size: 16px;
        color: #fff;
        font-weight: 500;
        border: none;
      }
    }
    .detail-content {
      flex: 1;
      position: relative;
      background: transparent;
      min-height: 0;
      ::v-deep {
        .live_context .el-carousel img {
          border-radius: 4px;
        }
        .live_context .is-wait {
          color: #fff;
        }
        .no-live {
          color: #fff;
        }
        .live_context {
          color: #fff;
        }
        #showVideo {
          background: #000;
        }
      }
    }
  }
}
.experiment-tip {
  color: #fff;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  i {
    font-size: 80px;
    margin-bottom: 10px;
  }
}
.practice-tabs {
  margin: 0px 0 0 20px;
}
.practice-wrap {
  flex: 1;
  height: 100%;
  /deep/.paper_container {
    padding: 0;
    .paper_left .question_details .question_list {
      margin-top: 0;
    }
  }
}
.mask {
  height: 100%;
  width: 100%;
  position: absolute;
  z-index: 100;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  >div {
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
::v-deep .dialog-wrap .attachment-wrap {
  overflow-y: auto;
  max-height: calc(60vh - 28px);
  padding-right: 5px !important;
}
</style>
