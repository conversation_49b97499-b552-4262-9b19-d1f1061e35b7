<template>
  <div class="login-wrapper">
    <div class="left-info">
      <div class="logo-link-url">
        <img v-if="!configLoading" :src="config.linkUrl || logoNull" alt="">
      </div>
      <!-- 产品名称 -->
      <div v-if="!configLoading" class="title">
        <div
          v-if="config.productName"
          style="white-space: pre-wrap"
          v-html="config.productName.replace(/ /g, '&nbsp;')"
        />
      </div>
      <!-- 产品描述 -->
      <div v-if="!configLoading" class="description">
        <div
          v-if="config.productDesc"
          style="white-space: pre-wrap"
          v-html="config.productDesc.replace(/ /g, '&nbsp;')"
        />
      </div>
    </div>
    <div ref="rightLogin" :style="{ paddingTop: paddingTop }" class="right-login">
      <div class="logo-box">
        {{ titleMap[activeCom] }}
      </div>
      <div ref="loginMainContent" class="login-main-content">
        <component ref="loginMainComponent" :config-data="config" :is="activeCom"/>
      </div>
    </div>
  </div>
</template>

<script>
import LoginByPwd from './component/LoginByPwd'
import { queryBaseInfoConfig } from '@/api/admin/config'
export default {
  name: 'Login',
  components: {
    LoginByPwd
  },
  data() {
    return {
      activeCom: 'LoginByPwd',
      titleMap: {
        LoginByPwd: '欢迎登录'
      },
      logoNull: require('@/packages/layout/adminV1/logo-null.png'),
      config: {},
      configLoading: true,
      paddingTop: '0px'
    }
  },
  mounted() {
    this.getDetail()
    this.$nextTick(() => {
      this.calcPaddingTop()
      window.addEventListener('resize', this.calcPaddingTop)
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.calcPaddingTop)
  },
  methods: {
    /**
       * 获取网站信息详情
       */
    getDetail() {
      queryBaseInfoConfig().then((res) => {
        if (res.data.length && res.code == 0) {
          this.config = res.data[0]
          localStorage.setItem('baseWebsiteInfo', JSON.stringify(res.data[0]))
          this.$store.commit('SET_WEBSITEINFO', res.data[0])
          this.configLoading = false
        }
      })
    },
    // 动态计算登录页距离顶部高度
    calcPaddingTop() {
      this.$nextTick(() => {
        const rightLogin = this.$refs.rightLogin
        const loginMainContent = this.$refs.loginMainContent
        if (rightLogin && loginMainContent) {
          const parentH = rightLogin.clientHeight
          let contentH = 0
          if (this.$refs.captchaEnabled) {
            contentH = loginMainContent.clientHeight
          } else {
            contentH = loginMainContent.clientHeight - 80 // 减去验证码高度
          }
          let pt = ((parentH - contentH - 94) * 5) / 14
          if (pt < 0) pt = 0
          this.paddingTop = pt + 'px'
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import 'index';
.login-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  background: url('~@/assets/login/info-bg.png') center center / 1920px 100% no-repeat;
  background-size: cover;
  display: flex;
  overflow: auto;
  .left-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: auto;
    .logo-link-url {
      position: absolute;
      top: 5%;
      left: 30px;
      img {
        height: 70px;
      }
    }
    .title {
      font-size: 48px;
      font-weight: bold;
      color: #fff;
      line-height: 1.5;
      margin-top: 25vh;
    }
    .description {
      color: #fff;
      font-size: 24px;
      line-height: 34px;
      max-width: 50vw;
    }
  }
  .right-login {
    width: 37.5%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-left: 3.3%;
    padding-right: 3.3%;
    justify-content: flex-start;
    background-color: rgba(255, 255, 255, 0.8);
    .logo-box {
      font-size: 32px;
      color: #000;
      letter-spacing: 1px;
      margin-bottom: 40px;
      padding: 0 10px;
      border-bottom: 4px solid var(--color-600);
    }
    .login-main-content {
      width: 100%;
      max-width: 600px;
      height: auto;
      background: #fff;
      border-radius: 30px;
      box-sizing: border-box;
      border: 1px solid #B9D2CC;
      overflow: hidden;
      padding: 45px 0 40px 0;
    }
  }
}
</style>
