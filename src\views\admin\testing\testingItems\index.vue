<template>
  <div class="content-wrap-layout">
    <div class="vertical-wrap">
      <page-table
        ref="table"
        :default-selected-arr="defaultSelectedArr"
        :cache-pattern="true"
        @refresh="refresh"
        @link-event="linkEvent"
        @on-select="tabelSelect"
        @on-current="tabelCurrent"
      >
        <action-menu
          slot="action"
          :module-name="moduleName"
          :select-item="selectItem"
          @call="actionHandler"
        />
      </page-table>
    </div>
  </div>
</template>

<script>
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
import moduleConf from './config.js'
export default {
  name: 'TestingItems',
  components: {
    pageTable,
    actionMenu
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: []
    }
  },
  methods: {
    // 列表点击
    linkEvent({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler(type, data) {
      switch (type) {
        case 'create':
          this.$router.push({ name: 'testing_create' })
          break
        case 'edit':
          this.$router.push({ name: 'testing_edit', params: { id: this.selectItem[0].id, status: this.selectItem[0].status }})
          break
        case 'delete':
          // 调用删除接口
          break
        case 'refresh':
          this.selectItem = []
          this.$refs['table'].getList(false)
          break
      }
    }
  }
}
</script>
