<template>
  <div class="buttons-wrap">
    <el-button v-permission="'manage.testing.testProcesses.testProcessesList.testProcessesCreate'" icon="el-icon-plus" type="primary" @click="clickDrop('addTestingProcess')">创建流程</el-button>
    <el-dropdown trigger="click" placement="bottom-start" @command="clickDrop">
      <el-button type="primary">
        操作 <i class="el-icon-arrow-down el-icon--right"/>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-permission="'manage.testing.testProcesses.testProcessesList.testProcessesUpdate'" :disabled="singleDisabled" command="editTestingProcess">编辑</el-dropdown-item>
        <el-dropdown-item v-permission="'manage.testing.testProcesses.testProcessesList.testProcessesStatus'" :disabled="singleDisabled || (selectItem[0] && selectItem[0].status === '1')" command="start">启用</el-dropdown-item>
        <el-dropdown-item v-permission="'manage.testing.testProcesses.testProcessesList.testProcessesStatus'" :disabled="singleDisabled || (selectItem[0] && selectItem[0].status === '0')" command="forbidden">禁用</el-dropdown-item>
        <el-dropdown-item :disabled="singleDisabled" command="testReportTemplate">检测报告模版</el-dropdown-item>
        <el-dropdown-item v-permission="'manage.testing.testProcesses.testProcessesList.testProcessesUpdate'" :disabled="multipleDisabled" command="deleteTestingProcess">删除</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import addTestingProcess from './modal_add.vue'
import editTestingProcess from './modal_add.vue'
import deleteTestingProcess from './modal-delete'
import { handleTestProcessesStatusApi } from '@/api/testing/testProcesses.js'
export default {
  components: {
    addTestingProcess,
    editTestingProcess,
    deleteTestingProcess
  },
  mixins: [mixinsActionMenu],
  props: {
    selectItem: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'addTestingProcess': '创建流程',
        'editTestingProcess': '编辑流程',
        'deleteTestingProcess': '删除流程'
      },
      statusMapping: {
        'forbidden': 0,
        'start': 1
      }
    }
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    'clickDrop': function(name) {
      if (['addTestingProcess', 'editTestingProcess'].includes(name)) {
        this.modalWidth = '600px'
      }
      // 启用和禁用确认框
      if (['forbidden', 'start'].includes(name)) {
        this.$confirm(`请确认是否${name == 'start' ? '启用' : '禁用'}${this.selectItem[0].processName}流程?`, `${name == 'start' ? '启用' : '禁用'}`, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 启用和禁用接口
          handleTestProcessesStatusApi(this.selectItem[0].id, this.statusMapping[name]).then((res) => {
            if ([0, 200].includes(res.code)) {
              this.$message.success(`${name == 'start' ? '启用' : '禁用'}成功`)
              this.$emit('call', 'refresh')
            }
          })
        })
      } else if (name == 'testReportTemplate') {
        // 详情检测报告模板
        this.$router.push({
          path: `/testing/testingProcess/detail/${this.selectItem[0].id}/testReportTemplate`
        })
      } else {
        this.modalName = name
      }
    }
  }
}
</script>
