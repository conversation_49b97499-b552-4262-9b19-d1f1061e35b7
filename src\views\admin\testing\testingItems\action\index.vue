<template>
  <div class="buttons-wrap">
    <!-- 厂商、检测人员显示 -->
    <el-button v-if="(roleIds.includes(181251) || roleIds.includes(181254)) && !manage.testing.project.projectList.projectCreate" :disabled="singleDisabled" type="primary" @click="examineProject()">审核</el-button>
    <el-button v-permission="'manage.testing.project.projectList.projectCreate'" type="primary" icon="el-icon-plus" @click="handleCommand('create')">创建检测项目</el-button>
    <el-dropdown v-if="isShowHandleButton" trigger="click" placement="bottom-start" @command="handleCommand">
      <el-button type="primary">
        操作<i class="el-icon-arrow-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-permission="'manage.testing.project.projectList.projectTestApply'" :disabled="isSubmitDisabled" command="submit">项目资料送审</el-dropdown-item>
        <el-dropdown-item v-permission="'manage.testing.project.projectList.projectRetestApply'" :disabled="isRetestDisabled" command="modalRetest">申请复测</el-dropdown-item>
        <el-dropdown-item v-permission="'manage.testing.project.projectList.projectDelayApply'" :disabled="isDelayDisabled" command="modalExtend">申请延期</el-dropdown-item>
        <el-dropdown-item v-permission="'manage.testing.project.projectList.projectCreate'" :disabled="singleDisabled" command="examine">审核</el-dropdown-item>
        <el-dropdown-item v-permission="'manage.testing.project.projectList.projectUpdate'" :disabled="isEditDisabled" command="edit">编辑</el-dropdown-item>
        <el-dropdown-item v-permission="'manage.testing.project.projectList.projectArchive'" :disabled="isArchiveDisabled" command="modalArchive">归档</el-dropdown-item>
        <el-dropdown-item v-permission="'manage.testing.project.projectList.projectRemove'" :disabled="isDeleteDisabled" command="modalDelete">删除</el-dropdown-item>
        <el-dropdown-item v-permission="'manage.testing.project.projectList.projectPause'" :disabled="isSuspendDisabled" command="modalSuspend">挂起</el-dropdown-item>
        <el-dropdown-item v-permission="'manage.testing.project.projectList.projectUnpause'" :disabled="isUnsuspendDisabled" command="modalUnsuspend">取消挂起</el-dropdown-item>
        <el-dropdown-item v-permission="'manage.testing.project.projectList.projectUnpause'" :disabled="isReleaseTopologyDisabled" command="modalReleaseTopology">释放资源</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      destroy-on-close
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          :num="num"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import modalRetest from './modal-retest.vue'
import modalExtend from './modal-extend.vue'
import modalArchive from './modal-archive.vue'
import modalDelete from './modal-delete.vue'
import modalSuspend from './modal-suspend.vue'
import modalUnsuspend from './modal-unsuspend.vue'
import modalReleaseTopology from './modal-releaseTopology.vue'
import { getResourcesCountAPI } from '@/api/testing/index.js'
export default {
  components: {
    modalRetest,
    modalExtend,
    modalArchive,
    modalDelete,
    modalSuspend,
    modalUnsuspend,
    modalReleaseTopology
  },
  mixins: [mixinsActionMenu],
  props: {
    moduleName: {
      type: String,
      default: ''
    },
    selectItem: {
      type: Array,
      default: () => []
    },
    page: {
      type: String,
      default: 'list'
    }
  },
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'modalRetest': '申请复测',
        'modalExtend': '申请延期',
        'modalArchive': '归档',
        'modalDelete': '删除',
        'modalSuspend': '挂起',
        'modalUnsuspend': '取消挂起',
        'modalReleaseTopology': '释放资源'
      },
      roleIds: [],
      userInfo: JSON.parse(localStorage.getItem('loginUserInfo')) || {},
      num: 0
    }
  },
  computed: {
    ...mapGetters(['manage']),
    // 根据角色判断是否展示操作按钮
    isShowHandleButton() {
      const permissionData = this.manage.testing.project.projectList
      if (Object.prototype.toString.call(permissionData) === '[object Object]' && Object.keys(permissionData).length > 1) {
        return true
      }
    },
    // 基础单选禁用条件
    isBasicSingleDisabled() {
      return this.singleDisabled
    },

    // 项目资料送审按钮禁用条件
    isSubmitDisabled() {
      // 仅“资料待送审”状态可点击按钮
      if (this.selectItem[0] && !this.singleDisabled) {
        const status = this.selectItem[0].status
        if (status == '4') {
          return false
        } else {
          return true
        }
      } else {
        return true
      }
    },

    // 申请复测按钮禁用条件
    isRetestDisabled() {
      // 测试不通过”状态的条目可申请复测，单选
      if (this.selectItem[0] && !this.singleDisabled) {
        const status = this.selectItem[0].status
        if (status == '3') {
          return false
        } else {
          return true
        }
      } else {
        return true
      }
    },

    // 申请延期按钮禁用条件
    isDelayDisabled() {
      // 非测试中状态的条目可申请复测，单选
      return this.selectItem[0] && (this.selectItem[0].status == '1' || this.selectItem[0].status == '9') || this.singleDisabled
    },

    // 编辑按钮禁用条件
    isEditDisabled() {
      return this.selectItem[0] && this.selectItem[0].status == '9' || this.singleDisabled
    },

    // 归档按钮禁用条件
    isArchiveDisabled() {
      // 测试通过/测试不通过”两个状态的条目才可进行归档，单选
      if (this.selectItem[0] && !this.singleDisabled) {
        const status = this.selectItem[0].status
        if (status == '2' || status == '3') {
          return false
        } else {
          return true
        }
      } else {
        return true
      }
    },

    // 删除按钮禁用条件
    isDeleteDisabled() {
      // 测试中状态的条目才可进行删除，单选
      if (this.selectItem[0] && !this.singleDisabled) {
        const status = this.selectItem[0].status
        if (status != '1') {
          return false
        } else {
          return true
        }
      } else {
        return true
      }
    },

    // 挂起按钮禁用条件
    isSuspendDisabled() {
      if (this.selectItem[0] && !this.singleDisabled) {
        const status = this.selectItem[0].status
        if (status != '9') {
          return false
        } else {
          return true
        }
      } else {
        return true
      }
    },

    // 取消挂起按钮禁用条件
    isUnsuspendDisabled() {
      // 已挂起”状态的条目才可以进行取消挂起
      if (this.selectItem[0] && !this.singleDisabled) {
        const status = this.selectItem[0].status
        if (status == '9') {
          return false
        } else {
          return true
        }
      } else {
        return true
      }
    },

    // 释放资源按钮禁用条件

    isReleaseTopologyDisabled() {
      // “已挂起”状态的项目，“释放资源”按钮置灰
      if (this.selectItem[0] && !this.singleDisabled) {
        const status = this.selectItem[0].status
        if (status == '9') {
          return true
        } else {
          return false
        }
      } else {
        return true
      }
    }
  },
  mounted() {
    // 检测项目负责人：181251，检测主管：181252，检测人员：181253，检测厂商：181254
    this.roleIds = this.userInfo.roleIds ? JSON.parse(this.userInfo.roleIds) : []
  },
  methods: {
    getResourcesCount(name) {
      const params = {}
      params.id = this.selectItem[0].id
      getResourcesCountAPI(params).then((res) => {
        if (res.data.code === 0 || res.data.code === 200) {
          this.num = res.data.data
          this.modalName = name
        }
      })
    },
    confirmCall(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      } else {
        this.$emit('call', type, data)
      }
    },
    handleCommand(command) {
      if (command === 'create' || command === 'edit') {
        this.$emit('call', command, this.selectItem[0])
      } else if (command === 'submit') {
        this.$router.push({
          name: 'testing_submit',
          params: { id: this.selectItem[0].id },
          query: {
            name: this.selectItem[0].name,
            productName: this.selectItem[0].productName,
            vendorName: this.selectItem[0].vendorName,
            productVersion: this.selectItem[0].productVersion,
            auditStatus: this.selectItem[0].auditStatus,
            applyId: this.selectItem[0].applyId
          }
        })
      } else if (command === 'examine') {
        this.$router.push({
          name: 'testing_detail',
          params: {
            id: this.selectItem[0].id,
            view: 'applyRecord'
          }
        })
      } else {
        this.clickDrop(command)
      }
    },
    examineProject() {
      this.$router.push({
        name: 'testing_detail',
        params: {
          id: this.selectItem[0].id,
          view: 'applyRecord'
        }
      })
    },
    async clickDrop(name) {
      if (name === 'modalReleaseTopology') {
        this.getResourcesCount('modalReleaseTopology')
      } else {
        this.modalWidth = '520px'
        this.modalName = name
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.buttons-wrap {
  display: inline-block;
}
</style>
