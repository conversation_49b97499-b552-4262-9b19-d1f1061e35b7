<template>
  <div class="plugin-view">
    <h3 class="plugin-title">{{ pluginTitle }}</h3>
    <div v-loading="true" v-if="loading" class="plugin-loading" />
    <el-empty v-else-if="!apiData" :image="noDataImg" :image-size="120" style="padding: 0;height: 100%;" description="暂无数据" />
    <div v-else class="issues-ranking-wrap">
      <div v-if="apiData.rankingData.length > 0" class="ranking-list">
        <div
          v-for="(item, index) in apiData.rankingData"
          :key="index"
          class="ranking-item"
        >
          <div :class="getRankClass(index + 1)" class="rank-number">{{ index + 1 }}</div>
          <div class="project-info">
            <div class="project-name">{{ item.projectName }}</div>
          </div>
          <div class="issue-count">{{ item.issueCount }}</div>
          <div class="view-btn" @click="handleView(item)">查看</div>
        </div>
      </div>
      <el-empty v-else-if="apiData.rankingData && apiData.rankingData.length === 0" :image="noDataImg" :image-size="120" style="padding: 0;height: 100%;" description="暂无数据" />
    </div>
  </div>
</template>

<style lang="scss">
.issues-ranking-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .ranking-list {
    flex: 1;
    overflow-y: auto;
    padding-right: 15px;

    .ranking-item {
      display: flex;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid #EBEEF5;

      &:last-child {
        border-bottom: none;
      }

      .rank-number {
        width: 30px;
        height: 22px;
        line-height: 21px;
        text-align: center;
        border-radius: 8px;
        margin-right: 10px;
        font-size: 14px;
        font-weight: 500;
        border: 2px solid #E6A23C;
        border-color: #909399;
        color: #909399;

        &.rank-1 {
          border-color: #ee6666;
          color: #ee6666;
        }

        &.rank-2 {
          border-color: #fac858;
          color: #fac858;
        }

        &.rank-3 {
          border-color: #73c0de;
          color: #73c0de;
        }

        &.rank-4, &.rank-5 {
          border-color: #909399;
          color: #909399;
        }
      }

      .project-info {
        flex: 1;
        margin-right: 15px;

        .project-name {
          font-size: 14px;
          color: #303133;
        }
      }

      .issue-count {
        width: 50px;
        text-align: right;
        font-size: 14px;
        color: #303133;
        margin-right: 15px;
      }

      .view-btn {
        color: var(--color-600);
        cursor: pointer;
        font-size: 14px;
        padding: 0 5px;
      }
    }
  }

  .no-data {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #909399;
    font-size: 14px;
  }
}
</style>

<script>
import { mapGetters } from 'vuex'
import { projectProblemRanking } from '@/api/testing/testingOverview'
import pluginMixin from './mixin_plugin.js'

export default {
  mixins: [pluginMixin],
  props: {
    projectId: {
      type: [Number, String],
      default: ''
    },
    processId: {
      type: String,
      default: ''
    },
    currentRole: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      noDataImg: require('@/packages/table-view/nodata.png'),
      apiData: {
        rankingData: [
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['manage'])
  },
  watch: {
    'processId': {
      handler(val) {
        this.getData()
      },
      immediate: true
    }
  },
  methods: {
    handleView(row) {
      if (row.archiveStatus == 0 && this.manage.testing.project) {
        window.open(`/testing/testing/detail/${row.projectId}/issuesList`)
      } else if (row.archiveStatus == 1 && this.manage.testing.sample) {
        window.open(`/testing/sampleLibrary/detail/${row.projectId}/issuesList`)
      } else {
        this.$message.warning('暂无权限')
      }
    },
    /**
     * 获取排名的样式类
     * @param {number} rank - 排名数字
     * @return {string} 对应的CSS类名
     */
    getRankClass(rank) {
      return `rank-${rank}`
    },

    /**
     * 获取项目问题排行数据
     *
     * @param {boolean} hideLoading - 是否隐藏加载状态
     * @API GET /api/testing/issues-ranking
     */
    getData(hideLoading) {
      if (!hideLoading) {
        this.loading = true
      }
      const params = {
        processId: this.processId,
        roleId: this.currentRole
      }
      projectProblemRanking(params).then(res => {
        this.apiData.rankingData = res.data
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
