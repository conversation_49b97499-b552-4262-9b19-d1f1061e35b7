<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert
      :closable="false"
      type="warning"
      title="此操作将删除所有项目相关信息，请谨慎操作。"
    />
    <div class="info-box">
      <div class="info-item">
        <span class="label">检测项目：</span>
        <span class="value">{{ data[0].name }}</span>
      </div>
      <div class="info-item" style="margin-bottom: -5px;">
        <span class="label">当前状态：</span>
        <el-badge :type="getStatusClass(data[0].status)" is-dot/><span>{{ getStatusLabel(data[0].status) }}</span>
      </div>
    </div>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import { testingItemsDeleteAPI } from '@/api/testing/index'

export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      statusMap: {
        '0': { label: '待测试', value: '0', type: 'info' },
        '1': { label: '测试中', value: '1', type: 'warning' },
        '2': { label: '测试通过', value: '2', type: 'success' },
        '3': { label: '测试不通过', value: '3', type: 'danger' },
        // 项目状态 projectStatus
        '4': { label: '待送审资料', value: '4', type: 'info' },
        '5': { label: '待审核资料', value: '5', type: 'info' },
        '6': { label: '待部署环境', value: '6', type: 'info' },
        // 挂起状态 pendingStatus
        '8': { label: '取消', value: '8', type: 'info' },
        '9': { label: '已挂起', value: '9', type: 'info' }
      }
    }
  },
  computed: {
    // 筛选返回可操作数据
    availableArr() {
      return this.data
    }
  },
  methods: {
    getStatusLabel(status) {
      return this.statusMap[status] && this.statusMap[status].label || '-'
    },
    getStatusClass(status) {
      return this.statusMap[status] && this.statusMap[status].type || 'info'
    },
    close() {
      this.$emit('close')
    },
    confirm() {
      if (!this.data || !this.data.length) {
        this.$message.warning('没有可删除的项目')
        return
      }

      this.loading = true
      const id = this.data[0].id

      // 调用删除接口
      testingItemsDeleteAPI(id).then(res => {
        if (res.data.code === 0) {
          this.$message.success('删除成功')
          this.$emit('call', 'refresh')
          this.close()
        } else {
          this.$message.error(res.data.msg || '删除失败')
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.info-box {
  margin: 20px 0;
  .info-item {
    display: flex;
    margin-bottom: 15px;
    align-items: center;
    .label {
      width: 100px;
      text-align: right;
      line-height: 32px;
      padding-right: 12px;
    }
    .value {
      flex: 1;
      line-height: 32px;
    }
  }
}
.success {
  color: #67C23A;
}
.warning {
  color: #E6A23C;
}
.primary {
  color: var(--color-600);
}
.danger {
  color: #F56C6C;
}
</style>
