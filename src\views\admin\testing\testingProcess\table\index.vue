<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索流程名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="columnsObj[item].colMinWidth || colMinWidth"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'processName'">
            <a
              v-if="scope.row.processName"
              :href="`/testingProcess/detail/${scope.row.id}/overview`"
              style="color: var(--color-600); cursor: pointer"
              @click.prevent="handleDetail(scope.row)"
            >
              {{ scope.row.processName }}
            </a>
            <span v-else>-</span>
          </span>
          <div v-else-if="item == 'vendorStatus'">
            <span v-if="scope.row.vendorStatus == '1'">开放</span>
            <span v-else-if="scope.row.vendorStatus == '0'">关闭</span>
            <span v-else>-</span>
          </div>
          <div v-else-if="item == 'status'">
            <span v-if="scope.row.status == '1'"><el-badge type="success" is-dot />启用</span>
            <span v-else-if="scope.row.status == '0'"><el-badge type="error" is-dot />禁用</span>
            <span v-else>-</span>
          </div>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import module from '../config.js'
import { getTestProcessesListApi } from '@/api/testing/testProcesses.js'
export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      searchKeyList: [
        { key: 'processName', label: '流程名称', placeholder: '请输入流程名称', master: true },
        { key: 'vendorStatus', label: '厂商可选', type: 'radio', placeholder: '请选择厂商可选', valueList: [
          { label: '开放', value: '1' },
          { label: '关闭', value: '0' }
        ] },
        { key: 'status', label: '状态', type: 'radio', placeholder: '请选择状态', valueList: [
          { label: '启用', value: '1' },
          { label: '禁用', value: '0' }
        ] },
        { key: 'createByName', label: '创建人', placeholder: '请输入创建人' },
        { key: 'timeRange', label: '创建时间', type: 'time_range', placeholder: '请选择创建时间' }
      ],
      columnsObj: {
        'processName': {
          title: '流程名称', master: true
        },
        'vendorStatus': {
          title: '厂商可选',
          colMinWidth: 50
        },
        'taskCount': {
          title: '任务数量',
          colMinWidth: 50
        },
        'status': {
          title: '状态',
          colMinWidth: 50
        },
        'createByName': {
          title: '创建人',
          colMinWidth: 70
        },
        'createAt': {
          title: '创建时间',
          colMinWidth: 90
        }
      },
      columnsViewArr: [
        'processName',
        'vendorStatus',
        'taskCount',
        'status',
        'createByName',
        'createAt'
      ]
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      if (params.timeRange) {
        params.createTimeStart = params.timeRange.split(',')[0]
        params.createTimeEnd = params.timeRange.split(',')[1]
        delete params.timeRange
      }
      getTestProcessesListApi(params).then((res) => {
        this.tableData = res.data ? res.data.records : []
        this.tableTotal = res.data.total || 0
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
      })
    },
    // 查看详情
    handleDetail(row) {
      this.$router.push({
        path: `/testing/testingProcess/detail/${row.id}/overview`
      })
      this.$refs['tableView'].clearSelection()
      this.setHighlightRow(row)
      this.$refs['tableView'].toggleRowSelection(row)
    }
  }
}
</script>
