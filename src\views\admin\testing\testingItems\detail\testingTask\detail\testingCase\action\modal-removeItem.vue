<template>
  <div v-loading="loading" class="dialog-wrap">
    <batch-template
      :data="data"
      :available-data="availableData"
      :show-delete-warning="false"
      view-key="title"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="checked" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { taskCaseDelete } from '@/api/testing/testCase.js'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      checked: false,
      availableData: []
    }
  },
  mounted() {
    this.filterData()
  },
  methods: {
    filterData() {
      this.availableData = this.data.filter(item => {
        return item
      })
      this.availableData.length == 0 ? this.checked = true : this.checked = false
    },
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const ids = this.availableData.map(item => {
        return item.id
      })
      const params = {
        taskId: this.$route.params.id,
        ids: ids
      }
      taskCaseDelete(params).then((res) => {
        if ([0, 200].includes(res.code)) {
          this.$message.success('删除成功')
          this.$emit('call', 'refresh', params)
          this.close()
        }
      }).catch(() => {
        this.close()
        this.loading = false
      })
    }
  }
}
</script>
