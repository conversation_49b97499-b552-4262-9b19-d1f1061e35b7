export default {
  name: 'QuestionList',
  // 影响程度映射
  impactArr: [
    { label: '致命', value: '1', type: 'danger' },
    { label: '严重', value: '2', type: 'warning' },
    { label: '一般', value: '3', type: 'primary' },
    { label: '轻微', value: '4', type: 'info' }
  ],
  // 优先级映射
  priorityArr: [
    { label: '低', value: '1', type: 'info' },
    { label: '中', value: '2', type: 'warning' },
    { label: '高', value: '3', type: 'danger' }
  ],
  // 状态映射
  statusArr: [
    { label: '待审核', value: '1', type: 'info' },
    { label: '激活', value: '2', type: 'warning' },
    { label: '已修复', value: '3', type: 'success' },
    { label: '已关闭', value: '4', type: 'info' },
    { label: '已拒绝', value: '5', type: 'danger' }
  ],
  // 类型映射
  typeArr: [
    { label: '功能测试', value: '1' },
    { label: '性能测试', value: '2' },
    { label: '安全测试', value: '3' }
  ],
  // 将数组转换为对象形式，方便查找
  get impactObj() {
    return this.impactArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  },
  get priorityObj() {
    return this.priorityArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  },
  get statusObj() {
    return this.statusArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  },
  get typeObj() {
    return this.typeArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  }
}
