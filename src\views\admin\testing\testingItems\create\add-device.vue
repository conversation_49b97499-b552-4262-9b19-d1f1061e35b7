<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="设备名称" prop="name" class="required-field">
        <el-input v-model.trim="formData.name" placeholder="请输入"/>
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <span>主机</span>
        <!-- 本期先隐藏，后面会用到 -->
        <!-- <el-radio-group v-model="formData.type" size="small" @change="changePortInput">
          <el-radio-button :label="1">主机</el-radio-button>
          <el-radio-button :label="2">WEB</el-radio-button>
        </el-radio-group> -->
      </el-form-item>
      <el-form-item :label="formData.type == '1' ? 'IP' : '地址'" :rules="ipAddressRules" prop="target" class="required-field">
        <el-input v-model.trim="formData.target" placeholder="请输入"/>
      </el-form-item>
      <el-form-item label="端口号" prop="targetPort">
        <el-input v-model.trim="formData.targetPort" placeholder="请输入" @input="validatePortInput"/>
      </el-form-item>

      <el-form-item label="访问账号">
        <div class="account-wrapper">
          <div v-for="(account, index) in formData.accounts" :key="index" class="account-item">
            <div class="account-content">
              <div class="username-row">
                <el-form-item
                  :prop="`accounts.${index}.username`"
                  :rules="accountRules.username"
                  :label="`用户名${index + 1}`"
                  label-width="80px"
                  class="required-field"
                  style="margin-bottom: 5px;"
                >
                  <el-input v-model.trim="account.username" placeholder="请输入"/>
                </el-form-item>
                <i
                  v-if="formData.accounts.length > 1"
                  class="el-icon-delete delete-icon"
                  @click="removeAccount(index)"
                />
              </div>
              <el-form-item
                :prop="`accounts.${index}.password`"
                :rules="accountRules.password"
                label="密码"
                label-width="80px"
                class="required-field"
                style="margin-bottom: 0;"
              >
                <el-input v-model.trim="account.password" :type="account.eyeOpen ? 'password' : 'text'" placeholder="请输入">
                  <!-- 打开 -->
                  <svg v-if="!account.eyeOpen" slot="suffix" t="*************" class="form-control-feedback" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" @click="account.eyeOpen=true">
                    <path d="M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 0 0 0 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766z m-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176z m0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z" p-id="4042" />
                  </svg>
                  <!-- 关闭 -->
                  <svg v-if="account.eyeOpen" slot="suffix" t="*************" class="form-control-feedback" viewBox="0 0 1267 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" @click="account.eyeOpen=false">
                    <path d="M954.******** 116.62496767L1001.******** 163.******** 257.******** 907.4856826l-46.********-46.93778417z" p-id="2672" />
                    <path d="M445.******** 483.40800716a162.******** 162.******** 0 0 1 240.********-113.74833038l47.********-47.********a228.******** 228.******** 0 0 0-316.4593616 316.45936238l47.********-47.********a162.******** 162.******** 0 0 1-18.********-107.99452366zM1009.******** 451.09817057a110.******** 110.******** 0 0 1 0 121.27253821c-102.******** 148.********-249.******** 232.********-404.******** 232.8078664a436.******** 436.******** 0 0 1-186.********-42.93224866l-49.******** 49.79255647a517.******** 517.******** 0 0 0 237.******** 58.42326694c180.******** 0 341.********-94.******** 452.********-243.43027852a197.******** 197.******** 0 0 0 0-229.******** 645.******** 645.******** 0 0 0-137.********-137.6487578l-49.******** 48.68605618a589.******** 589.******** 0 0 1 137.******** 142.51736316z" p-id="2673" />
                    <path d="M291.10649744 764.45932868l48.46475565-48.46475564a588.87997344 588.87997344 0 0 1-138.75525887-143.62386426 108.21582418 108.21582418 0 0 1 0-121.27253821C303.27801169 303.04830121 450.66398024 218.29030417 605.13155713 218.29030417a436.84670003 436.84670003 0 0 1 188.54781536 44.26005028l50.23515752-50.89905833a518.94909385 518.94909385 0 0 0-238.11907208-58.42326693c-180.******** 0-341.******** 94.********-452.55901803 243.43027851A192.******** 192.******** 0 0 0 116.50059822 511.73443968a192.******** 192.******** 0 0 0 36.******** 114.******** 651.******** 651.******** 0 0 0 137.******** 137.********zM830.******** 555.55188969a228.******** 228.******** 0 0 0-34.********-170.8437957l-47.******** 47.********a162.******** 162.******** 0 0 1-221.******** 221.30025297l-47.******** 47.35825457a228.******** 228.******** 0 0 0 351.********-145.61556668z" p-id="2674" />
                  </svg>
                </el-input>
              </el-form-item>
            </div>
          </div>
          <div class="add-account-btn">
            <el-button type="text" icon="el-icon-plus" style="color: var(--color-600)" @click="addAccount">添加账号</el-button>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="备注">
        <el-input v-model.trim="formData.remark" type="textarea" rows="3" placeholder="请输入"/>
      </el-form-item>
    </el-form>

    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" style="margin-left: 10px;" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import validate from '@/packages/validate/index'

export default {
  name: 'AddDevice',
  props: {
    projectId: {
      type: [String, Number],
      default: ''
    },
    deviceBtnType: {
      type: String,
      default: ''
    },
    // 编辑设备数据
    editDeviceData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    // 自定义验证器：端口号
    const validatePort = (rule, value, callback) => {
      if (!value) {
        callback()
      } else {
        const portNum = parseInt(value, 10)
        if (!/^\d+$/.test(value) || portNum < 1 || portNum > 65535) {
          callback(new Error('请输入1-65535之间的整数'))
        } else {
          callback()
        }
      }
    }

    return {
      loading: false,
      formData: {
        name: '',
        target: '',
        type: 1,
        targetPort: 22, // 主机走ssh 默认22，WEB走http，默认80
        accounts: [
          { username: '', password: '', eyeOpen: true }
        ],
        remark: ''
      },
      initialFormData: {
        name: '',
        target: '',
        type: 1,
        targetPort: 22,
        accounts: [
          { username: '', password: '', eyeOpen: true }
        ],
        remark: ''
      },
      rules: {
        name: [
          validate.required(),
          validate.name_64_char
        ],
        type: [validate.required()],
        targetPort: [
          { validator: validatePort, trigger: 'blur' }
        ],
        remark: [
          { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
        ]
      },
      accountRules: {
        username: [
          validate.required(),
          validate.name_64_char
        ],
        password: [
          validate.required(),
          validate.name_64_char
        ]
      },
      ipAddressRules: [
        { required: true, message: '请输入IP地址', trigger: 'blur' },
        { validator: this.validateIp, trigger: 'blur' }
      ]
    }
  },
  watch: {
    'formData.type'(val) {
      if (val == 1) {
        this.ipAddressRules = [
          { required: true, message: '请输入IP地址', trigger: 'blur' },
          { validator: this.validateIp, trigger: 'blur' }
        ]
      } else {
        this.ipAddressRules = [
          { required: true, message: '请输入URL', trigger: 'blur' },
          { validator: this.validateUrl, trigger: 'blur' }
        ]
      }
      // 清除并重新校验
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate('target')
        }
      })
    }
  },
  created() {
    // 组件创建时保存一份初始数据的深拷贝
    this.initialFormData = JSON.parse(JSON.stringify(this.formData))
    if (this.deviceBtnType == 'edit') {
      const deviceData = JSON.parse(JSON.stringify(this.editDeviceData))
      // 保存原始索引，用于无id数据的更新
      this.formData._index = deviceData._index

      const commonFiledArr = ['name', 'targetPort', 'remark']
      commonFiledArr.map(key => {
        this.formData[key] = deviceData[key]
      })
      if (deviceData.targetPort == '22') {
        this.$set(this.formData, 'type', 1)
      }
      if (deviceData.targetPort == '80') {
        this.$set(this.formData, 'type', 2)
      }
      this.formData.target = deviceData.target
      const accounts = deviceData.deviceAccountVOList || deviceData.deviceAccountBOList || []
      if (accounts.length > 0) {
        this.$set(this.formData, 'accounts', accounts)
      }
    }
  },
  methods: {
    validateIp(rule, value, callback) {
      const ipPattern = /^(\d{1,3}\.){3}\d{1,3}$/
      if (!value) {
        callback(new Error('请输入IP地址'))
      } else if (value.length > 64) {
        callback(new Error('长度不能超过64个字符'))
      } else if (ipPattern.test(value)) {
        const parts = value.split('.')
        const isValidIp = parts.every(part => {
          const num = parseInt(part, 10)
          return num >= 0 && num <= 255
        })
        if (isValidIp) {
          callback()
        } else {
          callback(new Error('请输入正确的IP地址'))
        }
      } else {
        callback(new Error('请输入正确的IP地址'))
      }
    },
    validateUrl(rule, value, callback) {
      const urlPattern = /^(http|https):\/\/((([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,})|((\d{1,3}\.){3}\d{1,3}))(:\d{1,5})?(\/.*)?$/
      if (!value) {
        callback(new Error('请输入URL'))
      } else if (value.length > 64) {
        callback(new Error('长度不能超过64个字符'))
      } else if (urlPattern.test(value)) {
        callback()
      } else {
        callback(new Error('请输入正确的URL'))
      }
    },
    // 验证端口输入
    validatePortInput(value) {
      if (value === '') return
      if (!/^\d+$/.test(value) || parseInt(value) < 1 || parseInt(value) > 65535) {
        this.$message.warning('请输入1-65535之间的整数')
        this.formData.targetPort = ''
      }
    },
    changePortInput(val) {
      const portMapping = {
        1: 22, // 主机默认端口
        2: 80 // WEB默认端口
      }
      this.formData.targetPort = portMapping[val] || ''
    },
    // 重置表单数据到初始状态
    resetForm() {
      this.formData = JSON.parse(JSON.stringify(this.initialFormData))
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
    },
    addAccount() {
      this.formData.accounts.push({ username: '', password: '', eyeOpen: true })
    },
    removeAccount(index) {
      this.formData.accounts.splice(index, 1)
    },
    validateAccounts() {
      let valid = true
      // 遍历所有账号，检查用户名和密码
      for (let i = 0; i < this.formData.accounts.length; i++) {
        const account = this.formData.accounts[i]

        // 检查用户名
        if (!account.username) {
          this.$message.error(`账号${i + 1}：请输入用户名`)
          valid = false
          break
        } else if (account.username.length < 1 || account.username.length > 64) {
          this.$message.error(`账号${i + 1}：用户名长度在1到64个字符之间`)
          valid = false
          break
        }

        // 检查密码
        if (!account.password) {
          this.$message.error(`账号${i + 1}：请输入密码`)
          valid = false
          break
        } else if (account.password.length < 1 || account.password.length > 64) {
          this.$message.error(`账号${i + 1}：密码长度在1到64个字符之间`)
          valid = false
          break
        }
      }

      return valid
    },
    close() {
      this.resetForm()
      this.$emit('close')
    },
    confirm() {
      this.$refs.form.validate(valid => {
        if (valid && this.validateAccounts() && this.projectId) {
          this.loading = true

          if (this.deviceBtnType == 'add') {
            // 添加模式：构造返回数据并触发回调
            const returnData = {
              name: this.formData.name,
              target: this.formData.target,
              targetPort: this.formData.targetPort,
              remark: this.formData.remark,
              deviceAccountBOList: this.formData.accounts.map(acc => ({
                username: acc.username,
                password: acc.password
              })),
              deviceAccountBOListStr: this.formData.accounts.map(acc => ({
                name: acc.username + '/' + acc.password
              }))
            }
            returnData.type = this.formData.targetPort == '80' ? 2 : 1

            this.loading = false
            this.$message.success('添加设备信息成功')
            this.$emit('call', 'confirm_device', returnData)
            this.close()
          } else {
            // 编辑模式：只更新本地数据，不调用API
            const updatedData = {
              id: this.editDeviceData.id,
              name: this.formData.name,
              target: this.formData.target,
              targetPort: this.formData.targetPort,
              remark: this.formData.remark,
              deviceAccountBOList: this.formData.accounts.map(acc => ({
                username: acc.username,
                password: acc.password
              })),
              deviceAccountBOListStr: this.formData.accounts.map(acc => ({
                name: acc.username + '/' + acc.password
              })),
              _index: this.formData._index
            }
            updatedData.type = this.formData.targetPort == '80' ? 2 : 1
            this.loading = false
            this.$message.success('编辑设备信息成功')
            this.$emit('call', 'confirm_device', updatedData)
            this.close()
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.required-field {
  ::v-deep .el-form-item__label:before {
    content: '*';
    color: #F56C6C;
    margin-right: 4px;
  }
}
.account-wrapper {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 400px;
  overflow-y: auto;
  .account-item {
    background-color: #F3F6FE;
    border-radius: 4px;
    padding: 10px;
    padding: 10px 30px 8px 10px;
    position: relative;
    .delete-icon {
      color: var(--color-600);
      right: 5px;
      top: 21px;
      position: absolute;
      cursor: pointer;
    }
  }
  .add-account-btn {
    margin-top: -10px;
  }
}

/* 添加密码显示/隐藏图标样式 */
::v-deep .form-control-feedback {
  width: 18px;
  height: 18px;
  cursor: pointer;
  color: #909399;
  transition: color 0.2s;
  vertical-align: middle;
  margin-right: 5px;

  &:hover {
    color: #606266;
  }
}
</style>
