<template>
  <el-row :gutter="20">
    <el-col :span="12">
      <detail-card title="基本信息">
        <div slot="content" class="info-panel">
          <el-form label-width="120px" class="info-form">
            <el-form-item label="名称：">
              <span>{{ dataCompute.suiteName || '-' }}</span>
            </el-form-item>
            <el-form-item label="分类：">
              <span>{{ dataCompute.categoryName || '-' }}</span>
            </el-form-item>
            <el-form-item label="用例数量：">
              <span>{{ dataCompute.caseCount.toString() || '-' }}</span>
            </el-form-item>
            <el-form-item label="描述：">
              <span>{{ dataCompute.description || '-' }}</span>
            </el-form-item>
            <el-form-item label="创建人：">
              <span>{{ dataCompute.createByName || '-' }}</span>
            </el-form-item>
            <el-form-item label="创建时间：">
              <span>{{ dataCompute.createAt || '-' }}</span>
            </el-form-item>
            <el-form-item label="最后修改时间：">
              <span>{{ dataCompute.updateAt || '-' }}</span>
            </el-form-item>
          </el-form>
        </div>
      </detail-card>
    </el-col>
  </el-row>
</template>

<script>
import detailCard from '@/packages/detail-view/detail-card.vue'
import module from '../config'
import { testSuiteGet } from '@/api/testing/testCase.js'

export default {
  name: 'DetailOverview',
  components: {
    detailCard
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    id: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      module,
      dataCompute: {
        caseCount: 0
      }
    }
  },
  created() {
    this.getData(this.id)
  },
  methods: {
    // 获取测试用例详情
    getData: function(id) {
      if (id) {
        this.loading = true
        testSuiteGet(id).then(res => {
          this.dataCompute = res.data
        }).finally(() => {
          this.loading = false
        })
      }
    },
    // 获取优先级标签
    getPriorityLabel(value) {
      if (!value) return '-'
      const priorityItem = this.module.statusArr.find(item => item.value === value)
      return priorityItem ? priorityItem.label : '-'
    },

    // 获取类型标签
    getTypeLabel(value) {
      if (!value) return '-'
      const typeItem = this.module.typeArr.find(item => item.value === value)
      return typeItem ? typeItem.label : '-'
    }
  }
}
</script>

<style lang="scss" scoped>
.case-content {
  padding: 10px;

  .case-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;

    .case-number {
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--color-600);
      color: #fff;
      border-radius: 50%;
      margin-right: 10px;
    }

    .case-title {
      flex: 1;
      font-size: 16px;
      font-weight: bold;
    }
  }

  .section-title {
    margin: 15px 0 10px;
  }

  .html-content {
    border-radius: 4px;
    padding: 10px;
    min-height: 50px;
    margin-bottom: 20px;

    ::v-deep p {
      margin: 5px 0;
      line-height: 1.6;
    }
  }
}

.info-panel, .attachment-panel {
  margin-bottom: 20px;

  .section-header {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }

  .info-form {
    ::v-deep .el-form-item {
      margin-bottom: 8px;
    }

    ::v-deep .el-form-item__label {
      color: #606266;
    }

    ::v-deep .el-form-item__content {
      color: #303133;
    }
  }
}

.attachment-list {
  .attachment-item {
    display: flex;
    align-items: center;
    padding: 8px 0;

    i {
      color: #909399;
      margin-right: 5px;
    }

    .attachment-name {
      flex: 1;
      color: var(--color-600);
      cursor: pointer;
    }

    .attachment-size {
      color: #909399;
      font-size: 12px;
      margin-right: 10px;
    }

    .attachment-actions {
      display: flex;

      .el-button {
        padding: 0 5px;
      }
    }
  }
}

.no-attachments {
  color: #909399;
  text-align: center;
  padding: 20px 0;
}
</style>
