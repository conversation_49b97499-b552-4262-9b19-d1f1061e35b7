<template>
  <div v-loading="loading" class="dialog-wrap">
    <batch-template
      :data="data"
      :available-data="availableData"
      view-key="title"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!availableData.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { suiteCaseRemove } from '@/api/testing/testCase.js'

export default {
  name: 'DeleteTestCase',
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false
    }
  },
  computed: {
    availableData: function() {
      const tempArr = this.data.filter((item) => {
        return item
      })
      return tempArr
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true

      // 获取要删除的测试用例ID列表
      const postData = this.availableData.map(item => {
        return item.id
      })
      const params = {
        caseIds: this.availableData.map(item => {
          return item.caseId
        }),
        suiteId: this.$route.params.id
      }
      suiteCaseRemove(params).then((res) => {
        if ([0, 200].includes(res.code)) {
          this.$message.success('删除成功')
          this.$emit('call', 'refresh', postData)
          this.close()
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
