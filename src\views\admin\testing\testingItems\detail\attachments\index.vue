<template>
  <div class="content-wrap-layout" style="padding: 15px 0;">
    <div v-if="processTasks.length > 0" class="switch-group">
      <radio-group
        v-model="activeName"
        :options="processTasks"
        label-key="processName"
        value-key="id"
        @change="changeType"
      />
    </div>
    <page-table
      ref="table"
      :default-selected-arr="defaultSelectedArr"
      :cache-pattern="true"
      :data="data"
      default-selected-key="id"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
      @update-file="updateFile"
    >
      <action-menu
        ref="actionMenu"
        slot="action"
        :data="data"
        :module-name="moduleName"
        :select-item="selectItem"
        :process-tasks="processTasks"
        :active-task-id="activeName"
        @call="actionHandler"
      />
    </page-table>
  </div>
</template>

<script>
import actionMenu from './action/index.vue'
import moduleConf from './config'
import pageTable from './table/index.vue'
import { getFirstTestTaskAPI } from '@/api/testing/index'
import radioGroup from '@/components/commonRadioGroup/index.vue'
export default {
  // 测试附件
  name: 'AttachmentsList',
  components: {
    pageTable,
    actionMenu,
    radioGroup
  },
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      activeName: '',
      projectId: this.$route.params.id || '',
      processTasks: null // 流程任务列表
    }
  },
  created() {
    // 初始化时获取流程任务
    this.getProcessTasks()
  },
  methods: {
    // 获取测试流程任务
    async getProcessTasks() {
      const taskRes = await getFirstTestTaskAPI(this.projectId)
      const tasks = taskRes.data.data || []

      // 处理任务数据
      const displayTasks = []
      tasks.forEach(item => {
        if (item.children && item.children.length > 0) {
          // 有子任务，只展示子任务
          item.children.forEach(child => {
            displayTasks.push({
              ...child,
              processName: child.name
            })
          })
        } else {
          // 没有子任务，展示主任务
          displayTasks.push({
            ...item,
            processName: item.name
          })
        }
      })

      // 添加"全部"选项
      this.processTasks = [
        { id: '', processName: '全部' },
        ...displayTasks
      ]
      this.activeName = '' // 默认选择"全部"
    },
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.selectItem = []
          this.$refs['table'].getList(false)
          break
        case 'changeTask':
          this.changeType(data)
          break
      }
    },
    refresh: function() {},
    changeType(val) {
      this.activeName = val
      this.$refs['table'].getList(true, val)
    },
    updateFile(row) {
      // 设置选中的文件记录
      this.selectItem = [row]
      this.activeName = row.typeId || ''
      // 通过引用获取action-menu组件实例并调用其clickDrop方法
      this.$nextTick(() => {
        if (this.$refs.actionMenu) {
          this.$refs.actionMenu.clickDrop('upload')
        } else {
          // 如果无法直接获取组件实例，则通过事件触发
          this.actionHandler('upload')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.switch-group {
  margin-bottom: 13px;
  margin-left: 13px;
}
</style>
