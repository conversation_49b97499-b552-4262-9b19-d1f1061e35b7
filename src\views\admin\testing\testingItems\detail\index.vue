<template>
  <detail-view
    ref="detailView"
    :loading="loading"
    :data="data"
    :id="id"
    :view-item="filterViewItems"
    :show-header="false"
    title-key="name"
  />
</template>

<script>
import moduleConf from '../config'
import { mapGetters } from 'vuex'
import detailView from '@/packages/detail-view/index'
import detailOverview from './detail_overview'
import detailTask from './testingTask/index'
import detailEnvironment from './environment/index.vue'
import detailIssuesList from './issuesList/index.vue'
import detailTestingReport from './testingReport/index.vue'
import detailApplyRecord from './applyRecord/index.vue'
import detailDeviceLogs from './deviceLogs/index.vue'
import detailAttachments from './attachments/index.vue'
import { testingItemsDetailAPI } from '@/api/testing/index'
export default {
  name: 'TestingItemsDetail',
  components: {
    // actionMenu,
    detailView,
    detailOverview,
    detailTask,
    detailEnvironment,
    detailIssuesList,
    detailTestingReport,
    detailApplyRecord,
    detailDeviceLogs,
    detailAttachments
  },
  data() {
    return {
      moduleName: moduleConf.name,
      id: null, // 资源ID
      data: null, // 资源数据对象
      loading: true,
      viewItem: [
        {
          transName: '概况',
          name: 'overview',
          component: detailOverview,
          permission: 'projectDetailView'
        },
        {
          transName: '测试任务',
          name: 'tasks',
          component: detailTask,
          permission: 'projectTask'
        },
        {
          transName: '测试环境',
          name: 'environment',
          component: detailEnvironment,
          permission: 'testEnvironment'
        },
        {
          transName: '测试报告',
          name: 'testingReport',
          component: detailTestingReport,
          permission: 'testingReport'
        },
        {
          transName: '问题清单',
          name: 'issuesList',
          component: detailIssuesList,
          permission: 'problem'
        },
        {
          transName: '申请记录',
          name: 'applyRecord',
          component: detailApplyRecord,
          permission: 'applyRecord'
        },
        {
          transName: '设备日志',
          name: 'deviceLogs',
          component: detailDeviceLogs,
          permission: 'deviceLog'
        },
        {
          transName: '附件',
          name: 'attachments',
          component: detailAttachments,
          permission: 'projectAttachment'
        }
      ]
    }
  },
  computed: {
    ...mapGetters(['manage']),
    // 根据不同角色显示对应tab页
    filterViewItems() {
      return this.viewItem.filter(item => {
        return this.manage.testing.project.projectDetail.hasOwnProperty(item.permission)
      })
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.params.hasOwnProperty('id') ? to.params.id : null
      const fromId = from.params.hasOwnProperty('id') ? from.params.id : null
      if (toId !== fromId && this.$route.name !== 'testingTask_detail') {
        this.loadBase()
      }
    }
  },
  created() {
    this.loadBase()
    this.$nextTick(() => {
      if (this.$route.name === 'testingTask_detail') {
        this.$refs['detailView'].tabsActive = 'tasks'
      }
    })
  },
  methods: {
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.loadBase()
      }
    },
    'loadBase': function() {
      this.getData()
    },
    // 根据id获取详情数据
    'getData': function() {
      this.loading = true
      this.id = this.$route.name === 'testingTask_detail' ? this.$route.params.projectId : this.$route.params.id
      testingItemsDetailAPI(this.id).then(res => {
        if (res.data && res.data.code === 0) {
          this.data = res.data.data
        } else {
          this.$message.error(res.data.msg || '获取项目详情失败')
        }
        this.loading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.info-section {
  margin-bottom: 30px;
  background: #fff;
  padding: 20px;
  border-radius: 4px;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
    padding-left: 10px;
    border-left: 4px solid var(--color-600);
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;

  .info-item {
    display: flex;
    align-items: center;

    .label {
      width: 120px;
      color: #606266;
    }

    .value {
      flex: 1;
      color: #303133;
    }
  }
}

.description-content {
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
  min-height: 100px;
  line-height: 1.6;
}
</style>
