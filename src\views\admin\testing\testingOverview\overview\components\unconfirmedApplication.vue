<template>
  <div class="resource-table" style="height: 100%;">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      :default-placeholder="'默认搜索申请编号'"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="true"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      type="list"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="columnsObj[item].colMinWidth || 100"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item === 'applicationNumber'">
            <a :href="`/testing/testingApplication/applicationCompleted?id=${scope.row.id}&applicationNumber=${scope.row.applicationNumber}&detail=true&routeSearch=true&searchVal=${scope.row.applicationNumber}&searchKey=applicationNumber&moduleName=testingApplicationPassed`" target="_blank">{{ scope.row.applicationNumber }}</a>
          </span>
          <span v-else>{{ scope.row[item] || '-' }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import { unconfirmedApplication } from '@/api/testing/testingOverview.js'

// 引入封装的方法
export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  props: {
    processId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      searchKeyList: [
        { key: 'applicationNumber', label: '申请编号', master: true, placeholder: '请输入' },
        { key: 'productName', label: '检测产品', placeholder: '请输入' },
        { key: 'vendorName', label: '厂商名称', placeholder: '请输入' }
      ],
      columnsObj: {
        'applicationNumber': {
          title: '申请编号', master: true
        },
        'productName': {
          title: '检测产品'
        },
        'version': {
          title: '版本号'
        },
        'vendorName': {
          title: '厂商名称'
        },
        'createByName': {
          title: '申请人'
        },
        'createAt': {
          title: '申请时间'
        }
      },
      columnsViewArr: [
        'applicationNumber',
        'productName',
        'version',
        'vendorName',
        'createByName',
        'createAt'
      ]
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params.processId = this.processId
      unconfirmedApplication(params).then((res) => {
        const data = { ...res.data }
        this.tableData = data ? data.records : []
        this.tableTotal = Number(data.total) || 0
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
