const deviceTypeArr = [
  { label: '容器', value: 'docker' },
  { label: '虚拟机', value: 'qemu' }
]

const statusArr = [
  { label: '运行中', value: 'running', type: 'success' },
  { label: '关机', value: 'shutoff', type: 'danger' },
  { label: '处理中', value: 'pending', type: 'warning' },
  { label: '启动中', value: 'starting', type: 'info' },
  { label: '错误', value: 'error', type: 'danger' }
]
// info primary warning danger
export default {
  name: 'environmentList',
  statusArr: statusArr,
  deviceTypeArr: deviceTypeArr,
  // 将状态map数组转换为对象
  deviceTypeObj: deviceTypeArr.reduce((acc, prev) => {
    acc[prev.value] = prev
    return acc
  }, {}),
  statusObj: statusArr.reduce((acc, prev) => {
    acc[prev.value] = prev
    return acc
  }, {})
}
