<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="测试任务">
        <span>{{ data[0].name || '-' }}</span>
      </el-form-item>
      <el-form-item label="当前人员">
        <span>{{ testerListStr || '-' }}</span>
      </el-form-item>
      <el-form-item label="指派给" prop="testerList">
        <el-tag
          v-for="item in form.testerList"
          :key="item.id"
          :disable-transitions="true"
          style="vertical-align: middle; margin-right: 8px;"
          closable
          @click="drawerName = 'selectedTester'"
          @close="handleCloseTester(item)">
          {{ item.name }}
        </el-tag>
        <el-button type="ghost" style="vertical-align: middle;" @click="drawerName = 'selectedTester'">选择测试人员</el-button>
      </el-form-item>
    </el-form>
    <!-- 侧拉弹窗 start -->
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      append-to-body
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          :data="data"
          :selected-item="form.testerList"
          :is-single="false"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>
    <!-- 侧拉弹窗 end -->
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="disabled" type="primary" @click="confirm('form')">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import validate from '@/packages/validate'
import selectedTester from './select-tester.vue'
import { testingTaskAssignById } from '@/api/testing/index'
export default {
  name: 'StarTesting',
  components: {
    selectedTester
  },
  mixins: [mixinsActionMenu],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    }
  },
  data() {
    return {
      module,
      loading: false,
      disabled: false,
      drawerAction: ['selectedTester'], // 需要侧拉打开的操作
      titleMapping: {
        'selectedTester': '选择测试人员'
      },
      validate: validate,
      form: {
        testerList: []
      },
      rules: {
        testerList: [validate.required(['blur', 'change'])]
      }
    }
  },
  computed: {
    testerListStr() {
      const list = (this.data[0] && Array.isArray(this.data[0].testerList)) ? this.data[0].testerList : []
      return list.map(item => item.testerName).join('、')
    }
  },
  mounted() {
    this.form.testerList = this.data[0].testerList.map(item => {
      return { name: item.testerName, id: item.testerId }
    })
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.loading = true
          this.disabled = true
          const params = {}
          params.id = this.data[0].id
          params.testerList = this.form.testerList
          testingTaskAssignById(params)
            .then((res) => {
              if (res.data.code == 0) {
                this.$message.success('分配成功')
                this.$emit('call', 'refresh')
                this.close()
              }
            }).catch(() => {
            }).finally(() => {
              this.loading = false
              this.disabled = false
            })
        }
      })
    },
    drawerConfirmCall: function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'selectedTester') {
        this.form.testerList = [...new Map([...this.form.testerList, ...data.map(item => {
          return { name: item.realname, id: item.userId }
        })].map(item => [item.id, item])).values()]
        this.drawerClose()
        this.$nextTick(() => {
          this.$refs['form'].clearValidate('testerList')
        })
      }
    },
    // 删除某个学员
    handleCloseTester(item) {
      const idx = this.form.testerList.findIndex(v => v.id == item.id)
      idx > -1 && this.form.testerList.splice(idx, 1)
    }
  }
}
</script>
