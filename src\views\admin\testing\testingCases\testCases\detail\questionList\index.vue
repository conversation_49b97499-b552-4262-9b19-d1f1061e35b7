<!--
 * @Author: <PERSON><PERSON>iin
 * @Date: 2025-04-27 15:17:15
 * @LastEditTime: 2025-04-27 16:04:40
 * @FilePath: \hrm-zsk-manage-web\src\views\admin\testing\testingCases\testCases\detail\questionList\index.vue
 * @Description:
 *
-->
<template>
  <div class="content-wrap-layout">
    <page-table
      ref="table"
      :default-selected-arr="defaultSelectedArr"
      :cache-pattern="true"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tableSelect"
      @on-current="tableCurrent"
    >
      <action-menu
        slot="action"
        :module-name="moduleName"
        :select-item="selectItem"
        @call="actionHandler"
      />
    </page-table>
  </div>
</template>

<script>
import actionMenu from './action/index.vue'
import moduleConf from './config.js'
import pageTable from './table/index.vue'

export default {
  name: 'QuestionList',
  components: {
    pageTable,
    actionMenu
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: []
    }
  },
  methods: {
    // 列表点击
    linkEvent({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tableSelect(data) {
      this.selectItem = data
    },
    // 返回单选
    tableCurrent(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
          break
        case 'create':
          // 创建问题
          break
        case 'edit':
          // 编辑问题
          break
        case 'delete':
          // 删除问题
          break
      }
    },
    refresh() {
    }
  }
}
</script>
