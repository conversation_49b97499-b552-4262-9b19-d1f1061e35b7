<template>
  <div v-loading="loading" class="drawer-wrap">
    <div class="detail-operation-right">
      <el-button
        v-if="formData.executeCount > 0"
        type="primary"
        @click.stop="transferProblem(formData)"
      >转问题</el-button>
    </div>
    <div class="drawer-wrap-content">
      <el-form ref="form" :model="formData" :rules="rules" style="padding: 0;" class="form-wrap" label-suffix="：" label-position="left" label-width="120px">
        <el-form-item label="用例标题">
          {{ formData.title }}
        </el-form-item>
        <el-form-item v-if="String(formData.type) !== '3'" label="前置条件">
          <div v-if="formData.preCondition" class="html-content" style="white-space: pre-wrap; word-break: break-all;" v-html="formData.preCondition" />
          <div v-else>-</div>
        </el-form-item>
        <el-form-item v-if="String(formData.type) !== '3'" label="操作步骤">
          <div v-if="formData.steps" class="html-content" style="white-space: pre-wrap; word-break: break-all;" v-html="formData.steps" />
          <div v-else>-</div>
        </el-form-item>
        <el-form-item v-if="String(formData.type) !== '3'" label="预期结果">
          <div v-if="formData.expectedResult" class="html-content" style="white-space: pre-wrap; word-break: break-all;" v-html="formData.expectedResult" />
          <div v-else>-</div>
        </el-form-item>
        <el-form-item v-if="String(formData.type) === '3'" label="安全风险等级">
          <span>{{ module.riskLevelObj[formData.securityRiskLevel] && module.riskLevelObj[formData.securityRiskLevel].label || '-' }}</span>
        </el-form-item>
        <el-form-item v-if="String(formData.type) === '3'" label="检测内容">
          <div v-if="formData.detectionContent" class="html-content" style="white-space: pre-wrap; word-break: break-all;" v-html="formData.detectionContent" />
          <div v-else>-</div>
        </el-form-item>
        <el-form-item v-if="String(formData.type) === '3'" label="脆弱性分析">
          <div v-if="formData.vulnerabilityAnalysis" class="html-content" style="white-space: pre-wrap; word-break: break-all;" v-html="formData.vulnerabilityAnalysis" />
          <div v-else>-</div>
        </el-form-item>
        <el-form-item label="备注">
          <div v-if="formData.remarks" class="html-content" style="white-space: pre-wrap; word-break: break-all;" v-html="formData.remarks"/>
          <div v-else>-</div>
        </el-form-item>

        <el-form-item label="附件">
          <div v-if="!attachments || attachments.length === 0">
            -
          </div>
          <div v-else class="attachment-list">
            <div
              v-for="(item, index) in attachments"
              :key="index"
              class="attachment-item"
            >
              <i class="el-icon-document"/>
              <span class="attachment-name">{{ item.name }}（{{ (item.size/1024/1024).toFixed(2) }} MB）</span>
              <div class="attachment-actions">
                <el-button type="text" icon="el-icon-view" @click="handleFileView(item)"/>
                <el-button type="text" icon="el-icon-download" @click="handleFileDownload(item)"/>
              </div>
            </div>
          </div>
        </el-form-item>

        <el-divider content-position="left">用例执行</el-divider>
        <div style="margin-bottom: 10px;">
          <span>已执行{{ formData.executeCount||0 }}次，</span>
          <a v-if="formData.executeCount > 0" @click="handleViewExecuteRecord">查看执行记录</a>
          <span v-else>暂无执行记录</span>
        </div>

        <!-- 动态渲染表单字段 -->
        <template v-if="formFields.length > 0">
          <el-form-item
            v-for="field in formFields"
            :key="field.uuid"
            :label="field.label"
            :prop="field.key"
            :rules="field.required ? [{ required: true, message: '必填项', trigger: 'blur' }] : []"
          >
            <!-- 下拉选择框 -->
            <el-select
              v-if="field.type === 'select'"
              v-model="formData[field.key]"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="option in field.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>

            <!-- 文本区域 -->
            <el-input
              v-else-if="field.type === 'textarea'"
              v-model="formData[field.key]"
              :rows="4"
              type="textarea"
              placeholder="请输入"
              maxlength="255"
            />
            <!-- 富文本 -->
            <Editor
              v-else-if="field.type === 'richtext'"
              :key="field.key"
              :content="formData[field.key]"
              :only-editor="false"
              :editor-config="blurEditorFocusConfig"
              :is-read-only="false"
              :upload-img-api="richTextTestingUploadImgApi"
              width="100%"
              height="200px"
              @contentChange="contentChange(field.key, $event)"
            />

            <!-- 默认输入框 -->
            <el-input
              v-else
              v-model="formData[field.key]"
              placeholder="请输入"
            />
          </el-form-item>
        </template>

        <!-- 如果没有动态字段，则显示默认的结果和说明字段 -->
        <template v-else>
          <template v-if="formData.type !== 3">
            <el-form-item label="执行结果" prop="executeResult">
              <el-select v-model="formData.executeResult" style="width: 100%" placeholder="请选择">
                <el-option
                  v-for="item in normalOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-else>
            <el-form-item label="执行结果" prop="executeResult">
              <el-select v-model="formData.executeResult" style="width: 100%" placeholder="请选择">
                <el-option
                  v-for="item in securityOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </template>
          <el-form-item label="结果说明" prop="executeDescription">
            <el-input v-model.trim="formData.executeDescription" :rows="4" placeholder="请输入" type="textarea"/>
          </el-form-item>
        </template>
      </el-form>
    </div>
    <div class="drawer-footer">
      <el-button :loading="submitLoading" type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
    <el-dialog
      :visible.sync="executeRecordVisible"
      title="执行结果"
      width="720px"
      append-to-body
      @close="closeRecordDia"
    >
      <div :loading="executeRecordsLoading" class="dialog-wrap">
        <div v-if="executeRecords.length > 0" class="execute-records">
          <div v-for="(record, index) in executeRecords" :key="index" class="record-item">
            <div class="record-header">
              <span>{{ index + 1 }}. {{ record.createAt }}, 由 <span style="font-weight: bold;">{{ record.createByName }}</span> 执行, 执行结果：{{ record.executeResult }}</span>
            </div>
            <template v-if="record.executeResultOther.length > 0">
              <div v-for="(item, index) in record.executeResultOther" :key="index" class="record-description">
                <template v-if="item.value && item.type !== 'richtext'">
                  <div style="display: flex;justify-content: flex-start;">
                    <div class="form-label">{{ item.label }}： </div>
                    <div class="pre-wrap">{{ item.value || '-' }}</div>
                  </div>
                </template>
                <template v-else-if="item.value && item.type === 'richtext'">
                  <div style="display: flex;justify-content: flex-start;">
                    <div class="form-label">{{ item.label }}： </div>
                    <div class="html-content" style="white-space: pre-wrap; word-break: break-all;" v-html="item.value" />
                  </div>
                </template>
              </div>
            </template>
          </div>
        </div>
        <div v-else class="no-records">
          <el-empty :image="noDataImg" description="暂无数据"/>
        </div>
        <div class="dialog-footer">
          <el-button type="primary" @click="closeRecordDia">关闭</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getTestProcessesFileById, taskCaseDetail, taskCaseGetExecuteResult, taskCaseSaveResult } from '@/api/testing/testCase'
import { richTextTestingUploadImgApi } from '@/components/testing/utils/config.js'
import Editor from '@/packages/editor/index.vue'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import filePreview from '@/components/testing/utils/filePreview'
import validate from '@/packages/validate'
import module from '../config.js'

export default {
  components: {
    Editor
  },
  mixins: [mixinsActionMenu, filePreview],
  props: {
    // 传入数据
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    name: {
      type: String
    }
  },
  data() {
    return {
      richTextTestingUploadImgApi,
      blurEditorFocusConfig: {
        placeholder: '请输入',
        autoFocus: false
      },
      noDataImg: require('@/packages/table-view/nodata.png'),
      executeRecordsLoading: false,
      module,
      loading: false,
      submitLoading: false,
      validate: validate,
      attachments: [],
      formData: {
        id: '', // 编辑时需要用到
        title: '',
        categoryId: '',
        category: null,
        priority: '3', // 默认高优先级
        type: '1', // 默认功能测试用例
        remarks: '',
        preCondition: '',
        steps: '',
        expectedResult: '',
        securityRiskLevel: '1', // 安全风险等级
        detectionContent: '', // 检测内容
        vulnerabilityAnalysis: '', // 脆弱性分析
        attachmentIds: [],
        executeResult: '',
        executeDescription: ''
      },
      formFields: [],
      normalOptions: [
        { label: '通过', value: '通过' },
        { label: '不通过', value: '不通过' },
        { label: '阻塞', value: '阻塞' }
      ],
      securityOptions: [
        { label: '合规', value: '合规' },
        { label: '违规', value: '违规' },
        { label: '待确认', value: '待确认' },
        { label: '阻塞', value: '阻塞' }
      ],
      rules: {
        executeResult: [{ required: true, message: '必填项', trigger: 'change' }]
      },
      templateData: [],
      executeRecordVisible: false,
      executeRecords: [],
      selectItemId: ''
    }
  },
  computed: {
  },
  watch: {
    formFields: {
      handler(newFields) {
        // 当表单字段变化时，动态更新验证规则
        if (newFields && newFields.length > 0) {
          const rules = {}

          newFields.forEach(field => {
            if (field.required) {
              rules[field.key] = [{ required: true, message: '必填项', trigger: field.type === 'select' ? 'change' : 'blur' }]
            }
          })

          // 更新验证规则
          this.rules = rules
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.selectItemId = this.data.id
    this.getDetail()
    this.getFileList()
  },
  methods: {
    transferProblem(data) {
      const params = {}
      params.taskId = this.$route.params.id
      params.caseId = this.formData.id
      params.caseName = this.formData.title
      params.projectId = this.$route.params.projectId
      params.type = this.$route.name
      params.typeProblem = 'transferProblem'
      params.expectedResult = data.expectedResult || '<br>'
      params.steps = data.steps || '<br>'
      const url = this.$router.resolve({
        name: 'createTestingQuestion',
        query: params
      }).href
      window.open(url, '_blank')
    },
    // 富文本编辑器内容变更处理
    contentChange(key, value) {
      if (this.delHtml(value)) {
        this.formData[key] = value
      } else {
        const imgStrs = value.match(/<img.*?>/g)
        if (imgStrs && imgStrs.length) {
          // 内容只有图片时
          this.formData[key] = value
        } else {
          this.formData[key] = ''
        }
      }
    },

    // 过滤html代码、空格、回车 空白字符
    delHtml(str) {
      str = str.replace(/<("[^"]*"|'[^']*'|[^'">])*>/gi, '')
      str = str.replace(/[\r\n]/g, '')
      str = str.replace(/\s/g, '')
      str = str.replace(/&nbsp;/gi, '')
      return str
    },
    closeRecordDia() {
      this.executeRecordVisible = false
      this.executeRecords = []
    },
    getFileList() {
      const formData = new FormData()
      formData.append('sourceId', this.data.caseId)
      getTestProcessesFileById(formData).then(res => {
        if ([0, 200].includes(res.code)) {
          this.attachments = res.data
        }
      })
    },
    handleViewExecuteRecord() {
      this.getExecuteRecords()
      this.executeRecordVisible = true
    },
    getExecuteRecords() {
      this.executeRecordsLoading = true
      taskCaseGetExecuteResult(this.selectItemId || this.data.id)
        .then(res => {
          if ([0, 200].includes(res.code)) {
            res.data.map(item => {
              if (item.formResult) {
                item.formResultJson = JSON.parse(item.formResult)
                if (item.formResultJson.length > 1) {
                  item.executeResultOther = []
                  item.executeResultOther = item.formResultJson.slice(1, item.formResultJson.length)
                }
              } else {
                item.formResultJson = []
                item.executeResultOther = []
              }
            })
            this.executeRecords = Array.from(res.data)
          }
        })
        .catch(err => {
          console.error('获取执行记录失败:', err)
        })
        .finally(() => {
          this.executeRecordsLoading = false
        })
    },
    // 下载文件
    handleFileDownload(item) {
      if (item.path) {
        fetch(item.path, {
          method: 'get',
          responseType: 'blob'
        })
          .then((response) => response.blob())
          .then((blob) => {
            const a = document.createElement('a')
            const URL = window.URL || window.webkitURL
            const href = URL.createObjectURL(blob)
            a.href = href
            a.download = item.name
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(href)
          })
      }
    },
    getDetail() {
      taskCaseDetail(this.selectItemId || this.data.id).then(res => {
        this.formData = res.data
        this.$set(this.formData, 'executeResult', '')
        // 解析表单字段
        this.parseFormFields()
      })
    },
    getDetailNotFormData() {
      taskCaseDetail(this.selectItemId || this.data.id).then(res => {
        this.formData.executeCount = res.data.executeCount
      })
    },
    // 解析表单字段
    parseFormFields() {
      if (!this.formData || !this.formData.typeTemplate) {
        this.formFields = []
        return
      }

      try {
        // 尝试解析JSON字符串
        const templateData = JSON.parse(this.formData.typeTemplate)
        this.templateData = templateData
        if (Array.isArray(templateData) && templateData.length > 0) {
          // 成功解析并且是数组
          this.formFields = templateData.map(field => {
            // 确保每个字段都有唯一标识
            if (!field.uuid) {
              field.uuid = field.key || `field_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
            }

            // 初始化表单数据
            // 使用Vue.set确保响应式
            this.$set(this.formData, field.key, this.formData[field.key] || field.defaultValue || '')

            return field
          })
          this.$nextTick(() => {
            this.$refs.form.clearValidate()
          })
        } else {
          this.formFields = []
        }
      } catch (error) {
        console.error('解析表单模板失败:', error)
        this.formFields = []
      }
    },
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      // 手动验证必填字段
      let isValid = true

      if (this.formFields.length > 0) {
        // 验证动态表单字段
        this.formFields.forEach(field => {
          if (field.required && !this.formData[field.key]) {
            this.$message.error(`${field.label}不能为空`)
            isValid = false
          }
        })
      } else {
        // 验证默认字段
        if (!this.formData.executeResult) {
          this.$message.error('执行结果不能为空')
          isValid = false
        }
      }

      if (!isValid) {
        return
      }

      this.submitLoading = true

      // 构建提交数据
      const submitData = {
        taskCaseId: this.selectItemId || this.data.id || ''
      }

      // 添加动态表单字段的值
      if (this.formFields.length > 0) {
        this.templateData.map(item => {
          this.formFields.forEach(field => {
            if (item.key === field.key) {
              item.value = this.formData[field.key] || ''
            }
          })
        })
        submitData.formResult = JSON.stringify(this.templateData)
        submitData.executeResult = this.templateData.find(item => item.uuid === 'execResult').value || ''
      } else {
        // 使用默认字段
        submitData.executeResult = this.formData.executeResult
        submitData.executeDescription = this.formData.executeDescription
      }
      // 提交数据
      taskCaseSaveResult(submitData)
        .then(res => {
          if ([0, 200].includes(res.code)) {
            this.$message.success('执行成功')
            this.$emit('call', 'refresh')
            this.getDetailNotFormData()
            this.getFileList()
          }
        })
        .catch(err => {
          console.error('执行失败:', err)
        })
        .finally(() => {
          this.submitLoading = false
        })
    },
    getFormFields() {
      // 已经在parseFormFields中实现
    },
    getOptions() {
      // 已经在data中初始化
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-operation-right {
  position: absolute;
  top: 10px;
  right: 65px;
}

.pre-wrap {
  flex: 1;
  white-space: pre-wrap;
}
.form-label {
  width: 80px;
  color: var(--neutral-700);
  font-weight: 500;
}
.html-content{
  flex: 1;
  ::v-deep img{
    max-width: 100%;
  }
}
.pointer {
  cursor: pointer;
}
.ellipsis {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
/deep/ .el-button--ghost {
    border: 1px dashed #c8cacd !important;
}
/deep/ .el-table__cell {
  padding: 0;
}
/deep/ .el-tooltip__popper {
    max-width: 300px;
}
/deep/ .el-form .tableData .el-form-item:not(.is-error) {
  margin-bottom: 0;
}

.attachment-list {
  .attachment-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    &:nth-last-child(1){
      margin-bottom: 0;
    }
    padding: 0 10px;
    border: 1px solid var(--color-601-border);
    background-color: var(--color-601-background);

    i {
      color: #909399;
      margin-right: 5px;
    }

    .attachment-name {
      flex: 1;
      color: #000000D9;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .attachment-size {
      color: #000000D9;
      font-size: 12px;
    }

    .attachment-actions {
      display: flex;
      .el-button {
        color: var(--color-600);
        padding: 0 5px;
      }
    }
  }
}

.drawer-wrap {
  .drawer-wrap-content {
    overflow: auto;
    .tableData {
      /deep/ .el-input .el-input__inner {
        padding: 0 10px;
      }
    }
    ::v-deep .el-form-item__label{
      line-height: 34px;
    }
    ::v-deep .el-form-item__content{
      line-height: 34px;
    }
  }
  .form-wrap {
    /deep/ .el-select-dropdown__item.is-disabled {
      display: none;
    }
    /deep/ .time-select-item.disabled {
      display: none;
    }
  }
}

.dialog-wrap {

  .execute-records {
    max-height: 500px;
    overflow: auto;
    padding: 10px;
    .record-item {

      .record-header {

        .result-pass {
          color: #67c23a;
          font-weight: bold;
        }

        .result-fail {
          color: #f56c6c;
          font-weight: bold;
        }
      }

      .record-description {
        font-size: 14px;
        padding-left: 1em;
      }
    }
  }

  .no-records {
    text-align: center;
    color: #909399;
    padding: 20px 0;
  }
}
</style>
