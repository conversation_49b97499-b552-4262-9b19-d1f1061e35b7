<template>
  <el-row :gutter="20">
    <el-col :span="16">
      <detail-card>
        <div slot="title">
          <el-tag
            size="small"
            effect="plain"
            type="info"
          >
            {{ data.sortNumber||'-' }}
          </el-tag>
          {{ data.title || '-' }}
        </div>
        <div v-if="['1','2'].includes(data.type)" slot="content" class="case-content">

          <div class="section-title">前置条件：</div>
          <div class="html-content" v-html="data.preCondition || '<p>-</p>'"/>

          <div class="section-title">操作步骤：</div>
          <div class="html-content" v-html="data.steps || '<p>-</p>'"/>

          <div class="section-title">预期结果：</div>
          <div class="html-content" v-html="data.expectedResult || '<p>-</p>'"/>
        </div>
        <div v-else slot="content" class="case-content">

          <div class="section-title">检测内容：</div>
          <div class="html-content" v-html="data.detectionContent || '<p>-</p>'"/>

          <div class="section-title">脆弱性分析：</div>
          <div class="html-content" v-html="data.vulnerabilityAnalysis || '<p>-</p>'"/>
        </div>
      </detail-card>
    </el-col>

    <el-col :span="8">
      <detail-card title="基本信息">
        <div slot="content" class="info-panel">
          <el-form label-width="120px" class="info-form">
            <el-form-item label="分类：">
              <span>{{ data.categoryName || '-' }}</span>
            </el-form-item>
            <el-form-item label="优先级：">
              <span>{{ getPriorityLabel(data.priority) || '-' }}</span>
            </el-form-item>
            <el-form-item label="类型：">
              <span>{{ getTypeLabel(data.type) || '-' }}</span>
            </el-form-item>
            <el-form-item v-if="data.type === '3'" label="安全风险等级：">
              <span>{{ getRiskLabel(data.securityRiskLevel) || '-' }}</span>
            </el-form-item>
            <el-form-item label="关联项目：">
              <a v-if="data.projectName" :href="data.archiveStatus ? `/testing/sampleLibrary/detail/${data.projectId}/overview` : `/testing/testing/detail/${data.projectId}/overview`" target="_blank" @click.prevent="toProjectDetail(data)">{{ data.projectName || '-' }}</a>
              <span v-else>-</span>
            </el-form-item>
            <el-form-item label="创建人：">
              <span>{{ data.createByName || '-' }}</span>
            </el-form-item>
            <el-form-item label="创建时间：">
              <span>{{ data.createAt || '-' }}</span>
            </el-form-item>
            <el-form-item label="最后修改人：">
              <span>{{ data.updateByName || '-' }}</span>
            </el-form-item>
            <el-form-item label="最后修改时间：">
              <span>{{ data.updateAt || '-' }}</span>
            </el-form-item>
          </el-form>
        </div>
      </detail-card>
      <detail-card title="附件" class="mt-10">
        <div slot="content" class="attachment-panel">
          <el-empty v-if="!attachments || attachments.length === 0" :image="img" description="暂无数据"/>
          <div v-else class="attachment-list">
            <div
              v-for="(item, index) in attachments"
              :key="index"
              class="attachment-item mb-10"
            >
              <i class="el-icon-document"/>
              <el-tooltip effect="dark" placement="top">
                <div slot="content" class="attachment-name">{{ item.name }}</div>
                <div class="attachment-name-wrap">
                  <span class="attachment-name">{{ item.name }}</span>
                  <span class="attachment-size">{{ '（'+(item.size/1024/1024).toFixed(2) +' MB）' }}</span>
                </div>

              </el-tooltip>
              <div class="attachment-actions">
                <el-button type="text" icon="el-icon-view" @click="handleFileView(item)"/>
                <el-button type="text" icon="el-icon-upload2" @click="handleFileUpdate(item)"/>
                <input
                  id="file"
                  :ref="`files_${item.id}`"
                  type="file"
                  style="display: none;"
                  @change="updateFile($event,item.id)"
                >
                <el-button type="text" icon="el-icon-download" @click="handleFileDownload(item)"/>
                <el-button type="text" icon="el-icon-delete" @click="handleFileDelete(item)"/>
              </div>
            </div>
          </div>
        </div>
      </detail-card>
    </el-col>
  </el-row>
</template>

<script>
import { getTestProcessesFileById, deleteFileById, updateFile } from '@/api/testing/testCase'
import filePreview from '@/components/testing/utils/filePreview'
import detailCard from '@/packages/detail-view/detail-card.vue'
import module from '../config'

export default {
  name: 'DetailOverview',
  components: {
    detailCard
  },
  mixins: [filePreview],
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    id: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      img: require('@/packages/table-view/nodata.png'),
      maxFileSize: localStorage.getItem('maxFileSize') || 10,
      module,
      attachments: []
    }
  },
  watch: {
    id: {
      handler(newVal) {
        this.getFileList(newVal)
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    toProjectDetail(data) {
      if (data.taskUserCount === null) {
        this.$message.warning('无权限访问该项目')
        return
      }
      if (data.archiveStatus === 1) {
        window.open(`/testing/sampleLibrary/detail/${data.projectId}/overview`, '_blank')
      } else {
        window.open(`/testing/testing/detail/${data.projectId}/overview`, '_blank')
      }
    },
    handleFileDelete(item) {
      this.$confirm('确定删除该附件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const formData = new FormData()
        formData.append('id', item.id)
        formData.append('name', item.name)
        formData.append('sourceId', this.id)
        deleteFileById(formData).then((res) => {
          if (res.code === 0 || res.code === 200) {
            this.$message.success('删除成功')
            this.getFileList(this.$route.params.id)
          }
        })
      }).catch(() => {})
    },
    handleFileUpdate(item) {
      this.$refs[`files_${item.id}`][0].click()
    },
    updateFile(e, id) {
      const files = e.target.files
      if (!files || !files.length) return
      const file = files[0]
      if (file.size > 1024 * 1024 * this.maxFileSize) {
        this.$message.error(`上传文件不能超过${this.maxFileSize}MB!`)
        return false
      }
      // this.formData[type].fileError = false
      const formData = new FormData()
      formData.append('file', file)
      formData.append('name', file.name)
      formData.append('type', 4)
      formData.append('sourceId', this.id)
      formData.append('oldFileId', id)
      updateFile(formData).then((res) => {
        if (res.code === 0 || res.code === 200) {
          this.$message.success('更新成功')
          this.getFileList(this.id)
        }
      }).catch(() => {
        this.$message.error('更新失败')
      })
    },
    // 下载文件
    handleFileDownload(item) {
      if (item.path) {
        fetch(item.path, {
          method: 'get',
          responseType: 'blob'
        })
          .then((response) => response.blob())
          .then((blob) => {
            const a = document.createElement('a')
            const URL = window.URL || window.webkitURL
            const href = URL.createObjectURL(blob)
            a.href = href
            a.download = item.name
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(href)
          })
      }
    },
    getFileList(id) {
      const formData = new FormData()
      formData.append('sourceId', id)
      getTestProcessesFileById(formData).then(res => {
        if ([0, 200].includes(res.code)) {
          this.attachments = res.data
        }
      })
    },
    // 获取安全风险等级标签
    getRiskLabel(value) {
      if (!value) return '-'
      const riskItem = this.module.riskLevelArr.find(item => item.value === value)
      return riskItem ? riskItem.label : '-'
    },
    // 获取优先级标签
    getPriorityLabel(value) {
      if (!value) return '-'
      const priorityItem = this.module.priorityArr.find(item => item.value === value)
      return priorityItem ? priorityItem.label : '-'
    },

    // 获取类型标签
    getTypeLabel(value) {
      if (!value) return '-'
      const typeItem = this.module.typeArr.find(item => item.value === value)
      return typeItem ? typeItem.label : '-'
    },

    // 下载附件
    handleDownload(item) {
      this.$message.success(`开始下载: ${item.name}`)
      // 实际项目中应调用下载API
    },

    // 删除附件
    handleDelete(item) {
      this.$confirm(`确认删除附件 ${item.name}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success(`已删除附件: ${item.name}`)
        // 实际项目中应调用删除API
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.case-content {

  .case-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;

    .case-number {
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--color-600);
      color: #fff;
      border-radius: 50%;
      margin-right: 10px;
    }

    .case-title {
      flex: 1;
      font-size: 16px;
      font-weight: bold;
    }
  }

  .section-title {
    margin-bottom: 10px;
    color: #707275;
  }

  .html-content {
    border-radius: 4px;
    min-height: 50px;
    padding-bottom: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
    &:nth-last-child(1){
      border: none;
    }
    word-wrap: break-word;
    word-break: break-all;

    ::v-deep p {
      margin: 5px 0;
      line-height: 1.6;
    }
    ::v-deep img{
      max-width: 100%;
      object-fit: contain;
    }
  }
}

.info-panel, .attachment-panel {
  margin-bottom: 20px;

  .section-header {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }

  .info-form {
    ::v-deep .el-form-item {
      margin-bottom: 8px;
    }

    ::v-deep .el-form-item__label {
      color: #606266;
    }

    ::v-deep .el-form-item__content {
      color: #303133;
    }
  }
}

.attachment-list {
  .attachment-item {
    display: flex;
    align-items: center;
    padding: 0 10px;
    border: 1px solid #ccc;
    border: 1px solid var(--color-601-border);
    background: var(--color-601-background);
    i {
      color: #909399;
      margin-right: 5px;
    }
    .attachment-name-wrap{
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }

    .attachment-name {
      max-width: 190px;
      color: #000000D9;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .attachment-size {
      color: #000000D9;
      font-size: 12px;
    }

    .attachment-actions {
      display: flex;

      .el-button {
        padding: 0 5px;
        color:var(--color-600);
      }
    }
  }
}

.no-attachments {
  color: #909399;
  text-align: center;
  padding: 20px 0;
}
</style>
