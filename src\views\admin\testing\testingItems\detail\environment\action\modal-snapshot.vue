<template>
  <div class="resource-table" style="height: 100%; padding: 0;">
    <!-- 操作区 start-->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action"/>
        <el-button type="primary" icon="el-icon-refresh" @click="refresh"/>
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索标签显示区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索快照名称"
      @search="searchMultiple"
    />
    <!-- 列表 start-->
    <t-table-view
      ref="tableView"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      type="list"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="columnsObj[item].colMinWidth || colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'status'">
            <el-badge
              :type="getStatus(scope.row[item])" is-dot />
            {{ snapshot_status_info[scope.row[item].toLowerCase()] }}
          </span>
          <span v-else-if="item == 'option'">
            <el-link :disabled="scope.row.status == 'error' || detailData.pendingStatus == 9" :underline="false" type="primary" @click.stop="restoreSnapshot(scope.row)">恢复</el-link>
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
    <!-- 列表 end-->
  </div>
</template>
<script>
import { mapState } from 'vuex'
import moduleConf from '../config'
// 列表列配置
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import { restoreSnapshot } from '@/packages/topo/api/orchestration'
import { getVmSnapshotListApi, getNetSnapshotListApi, asyncRestoreSnapshotApi } from '@/api/testing/index.js'
import { testingItemsDetailAPI } from '@/api/testing/index'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [
    mixinsPageTable
  ],
  props: {
    resourceId: {
      type: String
    },
    topologyId: {
      type: String
    },
    data: {
      type: Array,
      default: () => []
    },
    // virtual: 虚拟机  network: 网络设备
    envType: {
      type: String,
      default: 'network'
    }
  },
  data() {
    return {
      detailData: {},
      loading: false,
      'snapshot_status_info': {
        'creating': '正在创建中',
        'created': '创建成功',
        'deleting': '正在删除中',
        'deleted': '已删除',
        'rollback-start': '快照恢复中',
        'rollback-end': '快照恢复成功',
        'available': '可用',
        'error': '异常'
      },
      // 模块名称 用于翻译
      moduleName: moduleConf.name,
      // 列表数据请求对象
      // 搜索项配置
      searchKeyList: [
        { key: 'name', label: '快照名称', placeholder: '请输入快照名称', master: true },
        { key: 'status', label: '状态', type: 'radio', placeholder: '请选择状态', valueList: [
          { label: '正在创建中', value: 'creating' },
          { label: '创建成功', value: 'created' },
          { label: '正在删除中', value: 'deleting' },
          { label: '已删除', value: 'deleted' },
          { label: '快照恢复中', value: 'rollback-start' },
          { label: '快照恢复成功', value: 'rollback-end' },
          { label: '可用', value: 'available' },
          { label: '异常', value: 'error' }
        ] },
        { key: 'timeRange', label: '创建时间', type: 'time_range', placeholder: '请选择创建时间' }
      ],
      columnsObj: {
        'name': {
          title: '快照名称', master: true
        },
        'status': {
          title: '状态', colMinWidth: 90
        },
        'description': {
          title: '描述'
        },
        'createdAt': {
          title: '创建时间'
        },
        'option': {
          title: '操作', colMinWidth: 90
        }
      },
      columnsViewArr: [
        'name',
        'status',
        'description',
        'createdAt',
        'option'
      ],
      apiType: ''
    }
  },
  computed: {
    ...mapState('socketListener', [
      'snapshotSocket'
    ])
  },
  watch: {
    'resourceId': function() {
      this.getList()
    },
    'snapshotSocket': function(nval, oval) {
      this.socketHandle(nval, oval)
      const payload = nval.payload
      if (payload.state === 'created') {
        this.getList()
      }
    }
  },
  mounted() {
    // 1.testing_detail: 检测项目测试环境
    // 2.testingTask_detail：测试任务详情测试环境
    if (this.$route.name == 'testingTask_detail') {
      this.columnsObj = {
        'name': {
          title: '快照名称', master: true
        },
        'status': {
          title: '状态', colMinWidth: 90
        },
        'description': {
          title: '描述'
        },
        'createdAt': {
          title: '创建时间'
        }
      }
      this.columnsViewArr = [
        'name',
        'status',
        'description',
        'createdAt'
      ]
    }
    this.getData()
  },
  methods: {
    // 根据id获取详情数据
    'getData': function() {
      this.loading = true
      this.id = this.$route.name === 'testingTask_detail' ? this.$route.params.projectId : this.$route.params.id
      testingItemsDetailAPI(this.id).then(res => {
        if (res.data && res.data.code === 0) {
          this.detailData = res.data.data
        } else {
          this.$message.error(res.data.msg || '获取项目详情失败')
        }
        this.loading = false
      })
    },
    restoreSnapshot(data) {
      if (this.envType == 'network') {
        this.$bus.$emit(
          'SINGLE_TASK_API',
          {
            taskName: '恢复快照',
            resource: data,
            apiObj: restoreSnapshot,
            data: { id: this.data[0].id, data: { 'snapshot_id': data.id }},
            sucsessCallback: (res) => {
              this.$bus.$emit(this.moduleName + '_module', 'reloadItem', data.id)
              this.$bus.$emit('node_module', 'reloadItem', this.resourceId)
              this.$bus.$emit('instances_module', 'reloadItem', this.resourceId)
            },
            errorCallback: () => {
              this.$bus.$emit(this.moduleName + '_module', 'reloadItem', data.id)
            }
          }
        )
      }
      if (this.envType == 'virtual') {
        const params = {
          vmId: this.data[0].id,
          snapshotId: data.id
        }
        this.loading = true
        asyncRestoreSnapshotApi(params).then((res) => {
          this.$message.success('快照恢复成功')
          this.getList()
          this.loading = false
        }).catch(() => {
          this.loading = false
          this.$message.error('快照恢复失败')
        }).finally(() => {
          this.loading = false
        })
      }
    },
    'getStatus': function(data) {
      let color = null
      switch (data.toLowerCase()) {
        case 'available':
          color = 'success'
          break
        case 'in-use':
          color = 'warning'
          break
        case 'error':
          color = 'danger'
          break
        default:
          color = 'info'
          break
      }
      return color
    },
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      if (params.timeRange) {
        params.createBeginTime = params.timeRange.split(',')[0]
        params.createEndTime = params.timeRange.split(',')[1]
        delete params.timeRange
      }
      if (this.envType == 'network') {
        params.nodeId = this.resourceId
        this.apiType = getNetSnapshotListApi
      }
      if (this.envType == 'virtual') {
        params.vmId = this.data[0].id
        this.apiType = getVmSnapshotListApi
      }
      this.apiType(params).then((res) => {
        this.tableData = res.data ? res.data.data.records : []
        this.tableTotal = res.data.data.total || 0
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
