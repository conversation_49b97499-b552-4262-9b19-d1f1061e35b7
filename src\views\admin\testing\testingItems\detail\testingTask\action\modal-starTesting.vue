<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        <p>请确保已具备测试条件再进行操作。</p>
      </div>
    </el-alert>
    <el-form ref="form" :model="form" label-width="100px">
      <el-form-item label="测试任务">
        <span>{{ data[0].name }}</span>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="disabled" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import { testingTaskStart } from '@/api/testing/index'
export default {
  name: 'StarTesting',
  components: {},
  mixins: [],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: false,
      disabled: false,
      form: {}
    }
  },
  computed: {},
  mounted() { },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm() {
      this.loading = true
      this.disabled = true
      const data = {
        id: this.data[0].id
      }
      testingTaskStart(data)
        .then((res) => {
          this.$message.success('操作成功')
          this.$emit('call', 'refresh')
          this.close()
        }).catch(() => {
        }).finally(() => {
          this.loading = false
          this.disabled = false
        })
    }
  }
}
</script>
