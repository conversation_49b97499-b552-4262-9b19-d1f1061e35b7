<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert
      :closable="false"
      type="warning"
      title="项目挂起后不可执行任何检测操作，请确认后再进行挂起。"
    />
    <el-form ref="form" :model="formData" :rules="rules" label-width="100px" label-position="left">
      <el-form-item label="检测项目">
        <span>{{ data[0].name }}</span>
      </el-form-item>
      <el-form-item label="当前状态">
        <el-badge :type="getStatusClass(data[0].status)" is-dot/>
        <span>{{ getStatusLabel(data[0].status) }}</span>
      </el-form-item>
      <el-form-item label="挂起说明" prop="reason">
        <el-input
          v-model.trim="formData.reason"
          :rows="3"
          type="textarea"
          placeholder="请输入挂起项目的原因"
          maxlength="255"
        />
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import { suspendProjectAPI } from '@/api/testing/index'

export default {
  name: 'ActionSuspend',
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      formData: {
        reason: ''
      },
      rules: {
        reason: [
          { required: true, message: '请输入挂起说明', trigger: 'blur' }
        ]
      },
      statusMap: {
        '0': { label: '待测试', value: '0', type: 'info' },
        '1': { label: '测试中', value: '1', type: 'warning' },
        '2': { label: '测试通过', value: '2', type: 'success' },
        '3': { label: '测试不通过', value: '3', type: 'danger' },
        // 项目状态 projectStatus
        '4': { label: '待送审资料', value: '4', type: 'info' },
        '5': { label: '待审核资料', value: '5', type: 'info' },
        '6': { label: '待部署环境', value: '6', type: 'info' },
        // 挂起状态 pendingStatus
        '8': { label: '取消挂起', value: '8', type: 'info' },
        '9': { label: '已挂起', value: '9', type: 'info' }
      }
    }
  },
  computed: {
    // 筛选返回可操作数据
    availableArr() {
      // 只有未通过的项目才能挂起
      return this.data.filter(item => item.status === '4')
    }
  },
  methods: {
    getStatusLabel(status) {
      return this.statusMap[status] && this.statusMap[status].label || '-'
    },
    getStatusClass(status) {
      return this.statusMap[status] && this.statusMap[status].type || 'info'
    },
    close() {
      this.$emit('close')
    },
    confirm() {
      this.$refs.form.validate(valid => {
        if (!valid) return
        if (!this.data || !this.data.length) {
          this.$message.warning('没有可操作的项目')
          return
        }
        this.loading = true
        const id = this.data[0].id
        const params = {
          reason: this.formData.reason
        }
        // 调用挂起API
        suspendProjectAPI(id, params).then(res => {
          if (res.data.code === 0) {
            this.$message.success('项目挂起成功')
            this.$emit('call', 'refresh')
            this.close()
          } else {
            this.$message.error(res.data.msg || '项目挂起失败')
          }
        }).finally(() => {
          this.loading = false
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.success {
  color: #67C23A;
}
.warning {
  color: #E6A23C;
}
.primary {
  color: var(--color-600);
}
.danger {
  color: #F56C6C;
}
</style>
