<template>
  <div class="cr-layout">
    <div v-if="notifyShow" class="license-notify">
      <span>您的系统将于 {{ licensesInfo.expiredDate }} 到期，到期后平台将无法正常使用，请及时更新授权许可。</span>
      <el-button class="ml-50" @click="jumpToLicense">立即更新</el-button>
      <el-button type="text" class="license-notify-later" @click="hide">稍后更新</el-button>
    </div>
    <cr-header
      v-if="$route.name != 'experiment_topo' && $route.name != 'exercisesPaper' && $route.name != 'cannotDynamicPaper' && $route.meta.type !=='full_create_all'"
      :active-menu="headerActiveMenu"
      :config="menuConfigView"
      :notify-show="notifyShow"
      @changeMenu="changeMenu"
      @pageJump="pageJump"
    >
      <page3DIcon v-if="!isDetectionRole" slot="page3D" />
      <div v-if="sysConfigData" slot="training" class="home-icon" style="margin-right: 5px;" @click="trainingEntrance">
        <svg
          t="1748748158016"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="4637"
          width="20"
          height="20"
        >
          <path d="M896 128H448a64.07 64.07 0 0 0-64 64v96a32 32 0 0 0 64 0v-96h313.84l-145.61 58.24A63.7 63.7 0 0 0 576 309.67V768H448v-96a32 32 0 0 0-64 0v96a64.07 64.07 0 0 0 64 64h128v48.74a64 64 0 0 0 87.77 59.42l256-102.4A63.7 63.7 0 0 0 960 778.33V192a64.07 64.07 0 0 0-64-64zM640 880.74V309.67l256-102.4v571.06zM288 640a32 32 0 0 1-22.63-54.63L338.75 512H96a32 32 0 0 1 0-64h242.75l-73.38-73.37a32 32 0 0 1 45.26-45.26l128 128a32 32 0 0 1 0 45.26l-128 128A31.9 31.9 0 0 1 288 640z" fill="#4A4C4F" p-id="4638" />
        </svg>
        回到培训
      </div>
      <user-header slot="user" />
    </cr-header>
    <div class="cr-container">
      <cr-aside
        v-if="$route.meta.type !=='full_create' && $route.meta.type !=='full_create_all' && itemRouters"
        :active-menu="sideActiveMenu"
        :items="itemRouters"
      />
      <cr-content/>
    </div>
  </div>
</template>

<script>
import license from '../admin/licenses/mixins/license'
import menuConfig from '../../router/config'
import { supervisorConfig, manufacturerConfig, personConfig, projectHeadConfig } from '../../router/detectionConfig'
import { mapGetters } from 'vuex'
import crHeader from '@/packages/layout/adminV1/cr-header'
import crAside from '@/packages/layout/adminV1/cr-aside'
import crContent from '@/packages/layout/adminV1/cr-content'
import page3DIcon from './components/page3D'
import userHeader from './components/UserHeader'
import { queryFirstConfigByName } from '@/api/admin/systemSettings'
import { sysDataConfigList } from '@/api/admin/sysDataConfig.js'

export default {
  name: 'AdminLayout',
  components: {
    crHeader,
    crAside,
    crContent,
    page3DIcon,
    userHeader
  },
  mixins: [license],
  data() {
    return {
      sysConfigData: null, // 系统数据配置
      headerActiveMenu: '',
      sideActiveMenu: '',
      menuConfig: menuConfig,
      itemRouters: [],
      licenseNotify: localStorage.getItem('licenseNotify') || 'show', // 是否显示license提醒
      licenseNoticeData: null
    }
  },
  computed: {
    ...mapGetters(['manage', 'manageRouters', 'userInfo']),
    // 根据权限展示导航菜单
    menuConfigView() {
      let data = []
      // 181251: 检测项目负责人
      // 181252: 检测主管
      // 181253: 检测人员
      // 181254: 检测厂商
      if (this.userInfo.roleId == '181251' || this.userInfo.roleId == '181252' || this.userInfo.roleId == '181253' || this.userInfo.roleId == '181254') {
        if (this.userInfo.roleId == '181251') {
          data = this.getMenuConfig(projectHeadConfig, data)
        }
        if (this.userInfo.roleId == '181252') {
          data = this.getMenuConfig(supervisorConfig, data)
        }
        if (this.userInfo.roleId == '181253') {
          data = this.getMenuConfig(personConfig, data)
        }
        if (this.userInfo.roleId == '181254') {
          data = this.getMenuConfig(manufacturerConfig, data)
        }
      } else {
        data = this.getMenuConfig(this.menuConfig, data)
      }
      return data
    },
    // 是否展示提醒栏
    notifyShow() {
      if (this.licensesInfo && this.licenseNoticeData && this.licenseNoticeData.enable) {
        const nowDate = new Date(this.licensesInfo.currentTime).getTime()
        const expiredDate = new Date(this.licensesInfo.expiredDate).getTime()
        const diff = (this.licenseNoticeData && this.licenseNoticeData.days ? this.licenseNoticeData.days : 30) * 24 * 60 * 60 * 1000
        return this.licensesInfo.status === 'authorized' && this.licenseNotify === 'show' && (expiredDate - nowDate < diff)
      } else {
        return false
      }
    }
  },
  watch: {
    '$route.matched'() {
      this.init()
    }
  },
  created() {
    this.init()
    this.getData()
    this.getLicenseNoticeData()
    this.getSysDataConfigList()
  },
  methods: {
    isDetectionRole() {
      return this.userInfo.roleId == '181252' || this.userInfo.roleId == '181253' || this.userInfo.roleId == '181254'
    },
    getMenuConfig(config, data) {
      config.forEach((item, index) => {
        if (item.subs && item.subs.length) {
          const itemCopy = JSON.parse(JSON.stringify(item))
          itemCopy.subs = itemCopy.subs.filter(sub => sub.alwaysShow || this.manage.hasOwnProperty(sub.key))
          if (itemCopy.subs.length) {
            data.push(itemCopy)
          }
        } else {
          if (item.alwaysShow || this.manage.hasOwnProperty(item.key)) {
            data.push(item)
          }
        }
      })
      return data
    },
    // 进入培训
    trainingEntrance() {
      if (this.sysConfigData) {
        window.open(`${this.sysConfigData.valueName}`, '_blank')
      } else {
        // 无培训链接时提示
        this.$message.warning('尚未开始')
      }
    },
    // 获取系统数据配置
    getSysDataConfigList() {
      sysDataConfigList({ page: 1, limit: 99999 }).then(res => {
        const data = res.data.records.find(item => {
          return item.groupName == 'home' && item.keyName == 'admin_course_url'
        })
        this.sysConfigData = data || null
      })
    },
    init() {
      // 设置左侧菜单
      let itemrouter = null
      let path = ''
      if (this.$route.matched[0].path === '/manage') {
        path = this.$route.path.split('/manage')[1]
        itemrouter = null
      } else {
        path = this.$route.matched[0].path
        itemrouter = this.manageRouters.find(item => item.path === path)
      }
      this.itemRouters = itemrouter

      // 设置顶部菜单的activeMenu
      let data = []
      this.menuConfig.forEach(item => {
        if (item.subs && item.subs.length) {
          data = data.concat(item.subs)
        } else {
          data.push(item)
        }
      })
      this.headerActiveMenu = data.find(item => item.path === path).key
      // 设置左侧菜单的activeMenu
      const sideMenuItem = this.itemRouters ? this.itemRouters.children.find(item => item.name === this.$route.name || item.name === this.$route.meta.activeMenu) : null
      this.sideActiveMenu = sideMenuItem ? (sideMenuItem.meta.nav || sideMenuItem.path) : ''
    },
    changeMenu(key, keyPath) {
      const menu = this.menuConfig.find(item => item.key === keyPath[0])
      let path = ''
      if (menu && menu.subs) {
        path = menu.subs.find(sub => sub.key === keyPath[1]).path
      } else {
        path = menu.path || '/manage'
      }
      if (path === '/manage/match') {
        this.$router.push({ name: 'matchList' })
        return
      }
      if (path === '/penetrant') {
        if (this.userInfo.roleId == '180162') {
          const url = window.ADMIN_CONFIG.PENETRANT_URL
          const token = JSON.parse(localStorage.getItem('Admin-Token')).data
          window.open(`${url}/penetrant/authorized?token=${token}&key=cr`)
        } else {
          this.$message({
            showClose: true,
            message: '用户没有开放该模块的权限，请联系管理员.',
            type: 'warning'
          })
        }
      } else {
        const itemRouters = this.manageRouters.find(item => item.path === path)
        this.itemRouters = itemRouters || null
        if (!this.itemRouters) {
          this.$router.push({ path: '/manage' + path })
        } else {
          const sideItemPath = this.itemRouters.children.filter(item => !item.hidden)[0].path
          this.sideActiveMenu = sideItemPath
          this.$router.push({ path: this.itemRouters.path + (this.itemRouters.path[this.itemRouters.path.length - 1] === '/' ? '' : '/') + sideItemPath })
        }
      }
    },
    pageJump(subs, meta) {
      if (subs) {
        if (subs.path === '/penetrant') {
          if (this.userInfo.roleId == '180162') {
            const url = window.ADMIN_CONFIG.PENETRANT_URL
            const token = JSON.parse(localStorage.getItem('Admin-Token')).data
            window.open(`${url}/penetrant/authorized?token=${token}&key=cr&path=${meta.path}`)
          } else {
            this.$message({
              showClose: true,
              message: '用户没有开放该模块的权限，请联系管理员.',
              type: 'warning'
            })
          }
        } else if (meta) {
          this.$router.push({ path: subs.path + '/' + meta.path })
        } else {
          this.$router.push({ path: subs.path })
        }
      }
    },
    // 获取license提醒配置数据
    'getLicenseNoticeData': function() {
      return new Promise((resolve, reject) => {
        queryFirstConfigByName('licenseAlert').then(res => {
          this.licenseNoticeData = JSON.parse(res.data.value)
          resolve()
        }).catch(() => {
          reject()
        })
      })
    },
    jumpToLicense() {
      this.$router.push({ name: 'license' })
      this.licenseNotify = 'hide'
      localStorage.setItem('licenseNotify', 'hide')
    },
    hide() {
      this.licenseNotify = 'hide'
      localStorage.setItem('licenseNotify', 'hide')
    }
  }
}
</script>

<style lang="scss" scoped>
.cr-layout {
  height: 100%;
  display: flex;
  flex: auto;
  flex-direction: column;
  min-height: 0;
  background: #f0f2f5;
  .license-notify {
    background-color: #f04848;
    background-image: url(../../assets/license_notify.png);
    background-size: contain;
    width: 100%;
    height: 44px;
    line-height: 44px;
    text-align: center;
    font-size: 14px;
    color: #fff;
    .license-notify-later {
      &:hover {
        border-color: transparent !important;
      }
    }
    /deep/ .el-button {
      background-color: transparent;
      color: #fff;
      &:hover {
        color: var(--color-600);
        border-color: var(--color-600);
      }
    }
  }
  .cr-container {
    flex: 1 1;
    overflow: hidden;
    position: relative;
    display: flex;
    .cr-content {
      display: flex;
      flex-direction: column;
      flex: 1 1;
      overflow-x: hidden;
      overflow-y: auto;
      background-color: var(--neutral-0);
      position: relative;
    }
  }
}
</style>
