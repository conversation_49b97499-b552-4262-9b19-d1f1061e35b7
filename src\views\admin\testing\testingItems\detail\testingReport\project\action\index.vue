<template>
  <div class="buttons-wrap">
    <el-button :disabled="data.pendingStatus == 9" type="primary" @click="clickDrop('generateTemplate')">生成测试报告</el-button>
    <el-button v-permission="'manage.testing.project.projectDetail.testingReport.testingReportProCreate'" :disabled="data.pendingStatus == 9" type="primary" @click="clickDrop('uploadReport')">上传测试报告</el-button>
    <el-dropdown trigger="click" placement="bottom-start" @command="clickDrop">
      <el-button type="primary">
        操作<i class="el-icon-arrow-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-permission="'manage.testing.project.projectDetail.testingReport.testingReportProModelDownload'" command="downloadTemplate">下载报告模版</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 弹窗 -->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      destroy-on-close
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 侧拉弹窗 start -->
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          :data="selectItem"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>
    <!-- 侧拉弹窗 end -->
  </div>
</template>

<script>
import { testingReportProModelDownload, generateWordReport } from '@/api/testing/index'
import uploadReport from './modal-uploadReport.vue'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import { fileDownload } from '@/packages/utils/downloadAndPreview.js'
export default {
  name: 'ActionMenu',
  components: {
    uploadReport
  },
  mixins: [mixinsActionMenu],
  inject: ['testSubmitVm'],
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    moduleName: {
      type: String,
      default: ''
    },
    selectItem: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      drawerAction: [], // 需要侧拉打开的操作
      // 弹窗title映射
      titleMapping: {
        'uploadReport': '上传测试报告'
      }
    }
  },
  methods: {
    generateTemplate() {
      generateWordReport({ projectId: this.$route.params.id }).then(res => {
        if (res.data.code == 0 || res.data.code == 200) {
          this.$emit('call', 'refresh')
        }
      })
    },
    downloadTemplate() {
      testingReportProModelDownload({ projectId: this.$route.params.id }).then(res => {
        if (res.data.data == '暂无模板') {
          this.$message.warning(res.data.data)
          return
        }
        const url = res.data.data
        const ext = url.substring(url.lastIndexOf('.'))
        const fileName = `${this.data.name}的报告模板${ext}`
        fileDownload(url, fileName)
      })
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    drawerConfirmCall: function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    'clickDrop': function(name) {
      if (this.drawerAction && this.drawerAction.includes(name)) {
        this.drawerName = name
      } else if (name === 'generateTemplate') {
        this.generateTemplate()
      } else if (name === 'downloadTemplate') {
        this.downloadTemplate()
      } else {
        this.modalName = name
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.action-menu {
  display: inline-block;
}
</style>
