const statusArr = [
  { label: '高', value: '1', type: 'success' },
  { label: '中', value: '2', type: 'warning' },
  { label: '低', value: '3', type: 'info' }
]

const statusObj = statusArr.reduce((acc, prev) => {
  acc[prev.value] = prev
  return acc
}, {})

const typeArr = [
  { label: '渗透测试用例', value: '1' },
  { label: '功能测试用例', value: '2' },
  { label: '安全测试用例', value: '3' }
]

const typeObj = typeArr.reduce((acc, prev) => {
  acc[prev.value] = prev
  return acc
}, {})

export default {
  name: 'testKits',
  statusArr,
  statusObj,
  typeArr,
  typeObj
}
