<template>
  <div :style="{'height': isOnlyTester ? '73vh' : '416px'}" class="page-layout">
    <div v-if="displayReport.length > 0" class="switch-group">
      <radio-group
        v-model="activeName"
        :options="displayReport"
        label-key="taskName"
        value-key="processTaskId"
        @change="changeType"
      />
    </div>
    <div class="table-wrap">
      <page-table
        ref="table"
        :default-selected-arr="defaultSelectedArr"
        :cache-pattern="true"
        :active-name="activeName"
        :data="data"
        @refresh="refresh"
        @link-event="linkEvent"
        @on-select="tabelSelect"
        @on-current="tabelCurrent"
        @call="actionHandler"
      >
        <action-menu
          slot="action"
          :module-name="moduleName"
          :data="data"
          :active-name="activeName"
          :task-name="taskName"
          :task-id="taskId"
          :select-item="selectItem"
          @call="actionHandler"
        />
      </page-table>
    </div>
  </div>
</template>

<script>
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
import moduleConf from './config.js'
import { testingReportQueryTask } from '@/api/testing/index'
import radioGroup from '@/components/commonRadioGroup/index.vue'
export default {
  name: 'TestingReport',
  components: {
    pageTable,
    actionMenu,
    radioGroup
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    // 是否仅为测试人员角色
    isOnlyTester: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      activeName: null,
      testingReport: [],
      displayReport: []
    }
  },
  computed: {
    taskName() {
      const item = this.displayReport.find(i => i.processTaskId === this.activeName)
      return item ? item.taskName : ''
    },
    taskId() {
      const item = this.displayReport.find(i => i.processTaskId === this.activeName)
      return item ? item.processTaskId : ''
    }
  },
  created() {
    this.getTestingReportQueryTask()
  },
  methods: {
    getTestingReportQueryTask() {
      testingReportQueryTask({ projectId: this.$route.params.id }).then(res => {
        if (res.data.code == 0) {
          const list = res.data.data
          this.testingReport = [{ taskName: '全部', processTaskId: null }, ...list]
          this.displayReport = this.getDisplayReport(list)
        }
      }).catch(() => {})
    },
    getDisplayReport(list) {
      const result = [{ taskName: '全部', processTaskId: null }]
      list.forEach(item => {
        if (item.children && Array.isArray(item.children) && item.children.length > 0) {
          // 有子任务，只展示子任务
          result.push(...item.children)
        } else {
          // 没有子任务，展示自己
          result.push(item)
        }
      })
      return result
    },
    // 列表点击
    linkEvent({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList(true, this.activeName)
          break
        case 'create':
          break
        case 'edit':
          break
        case 'delete':
          // 调用删除接口
          break
      }
    },
    changeType(val) {
      this.activeName = val
      this.$refs['table'].getList(true, val)
    },
    refresh() {
    }
  }
}
</script>
<style lang="scss" scoped>
.page-layout {
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  .switch-group {
    margin: 0px 0 10px 0px;
  }
  .table-wrap {
    flex: 1;
    display: flex;
    height: calc(100% - 45px);
    >div {
      padding-top: 10px;
    }
  }
}
</style>
