<template>
  <div class="content-wrap-layout">
    <div class="switch-group">
      <el-radio-group v-model="activeName" size="small" class="common-radio-group" @change="changeType">
        <el-radio-button label="1">资源申请</el-radio-button>
        <el-radio-button label="2">复测申请</el-radio-button>
        <el-radio-button label="3">延期申请</el-radio-button>
      </el-radio-group>
    </div>
    <div class="vertical-wrap">
      <!-- 资源申请-->
      <resourceApply v-if="activeName == '1'" @show-detail="showDrawerDetail"/>
      <!-- 复测申请 -->
      <retestingApply v-if="activeName == '2'" @show-detail="showDrawerDetail"/>
      <!-- 延期申请 -->
      <delayApply v-if="activeName == '3'" @show-detail="showDrawerDetail"/>
    </div>
    <el-drawer
      :visible.sync="detailShowDrawer"
      :modal="false"
      size="75%"
      append-to-body
      class="detail-view"
      @close="closeDetail"
    >
      <component
        v-if="detailShowDrawer"
        :is="detailMapping[activeName]"
        :id="drawerParams.id"
        :data="drawerParams"
        :project-id="drawerParams.projectId"
        @show-detail="showDrawerDetail"
      />
    </el-drawer>
  </div>
</template>

<script>
import resourceApply from './resourceApply/index.vue'
import retestingApply from './retestingApply/index.vue'
import delayApply from './delayApply/index.vue'
import resourceDetail from './resourceApply/detail/details.vue'
import retestingDetail from './retestingApply/detail/details.vue'
import delayDetail from './delayApply/detail/details.vue'
import moduleMixin from '@/packages/mixins/module_list'
export default {
  components: {
    resourceApply,
    retestingApply,
    delayApply,
    resourceDetail,
    retestingDetail,
    delayDetail
  },
  mixins: [moduleMixin],
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      listRouterName: 'testing_detail',
      drawerParams: null,
      detailShowDrawer: false,
      loading: false,
      activeName: '1', // 1.资源申请 2.复测申请 3.延期申请
      detailMapping: {
        '1': resourceDetail,
        '2': retestingDetail,
        '3': delayDetail
      }
    }
  },
  methods: {
    changeType(val) {
      this.activeName = val
      this.closeDetail()
    },
    closeDetail() {
      this.detailShowDrawer = false
    },
    showDrawerDetail(val) {
      this.$set(this, 'drawerParams', val)
      this.$set(this, 'detailShowDrawer', val.showDrawer)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-radio-group.common-radio-group {
  border-radius: 10px;
  border: none;
  padding: 2px;
  background-color: #F5F6F9;
  .el-radio-button {
    width: 90px;
    .el-radio-button__inner {
      width: 100%;
      background-color: #F5F6F9;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: bold;
      color: var(--neutral-700);
    }
  }
  .is-active {
    .el-radio-button__inner {
      background-color: #fff;
      color: var(--color-600);
      border-radius: 8px;
      font-size: 14px;
      border: none;
      font-weight: bold;
    }
  }
  .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    box-shadow: none;
    -webkit-box-shadow: none;
  }
}
</style>
