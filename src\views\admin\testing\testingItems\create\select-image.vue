<template>
  <div class="drawer-wrap">
    <div class="resource-table">
      <div>
        <el-alert
          :closable="false"
          type="warning"
        >
          <span slot="title">如果没有匹配的镜像，请先<span style="color: var(--color-600); cursor: pointer;" @click="linkToUpload">&nbsp;上传虚拟机镜像</span></span>
        </el-alert>
      </div>
      <!-- 操作区 -->
      <div class="operation-wrap">
        <div class="operation-left">
          <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
        </div>
        <div class="operation-right">
          <el-badge :value="searchBtnShowNum">
            <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
          </el-badge>
          <t-table-config
            v-if="!customColData.length"
            :data="columnsObj"
            :active-key-arr="columnsViewArr"
            style="margin-left: 10px;"
            @on-change-col="onChangeCol"
          />
        </div>
      </div>
      <!-- 搜索区 -->
      <t-search-box
        v-show="searchView"
        :search-key-list="searchKeyListView"
        default-placeholder="请输入镜像名称"
        @search="searchMultiple"
      />
      <!-- 列表 -->
      <t-table-view
        ref="tableView"
        :height="height"
        :single="true"
        :loading="loading"
        :data="tableData"
        :total="tableTotal"
        :page-size="pageSize"
        :current="pageCurrent"
        :select-item="selectItem"
        current-key="value"
        @on-select="onSelect"
        @on-current="onCurrent"
        @on-change="changePage"
        @on-sort-change="onSortChange"
        @on-page-size-change="onPageSizeChange"
      >
        <el-table-column v-for="item in columnsViewArr" :key="item" :label="columnsObj[item].title" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <template v-if="item === 'status'">
              <el-badge :type="getStatusType(scope.row.status)" is-dot />
              <span v-if="scope.row.status === 'error'">错误</span>
              <span v-if="scope.row.status === 'active'">可用</span>
              <span v-if="scope.row.status === 'creating'">创建中</span>
              <span v-if="scope.row.status === 'saving'">  {{ scope.row.progress }}{{ scope.row.progress ? '%' : '' }}</span>
            </template>
            <template v-else-if="item === 'size'">
              {{ handleSize(scope.row[item], 'B') }}
            </template>
            <template v-else>
              {{ scope.row[item] || '-' }}
            </template>
          </template>
        </el-table-column>
      </t-table-view>
    </div>
    <!-- 底部操作栏 -->
    <div class="drawer-footer">
      <el-button :disabled="!selectItem" type="primary" @click="confirmSelectImage">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
  </div>
</template>

<script>
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { queryImagesAPI } from '@/api/testing/index'

export default {
  name: 'SelectImage',
  components: {
    tSearchBox,
    tTableView,
    tTableConfig
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: 'selectImageTesting',
      loading: false,
      selectItem: null,
      // 搜索配置项
      searchKeyList: [
        { key: 'name', label: '镜像名称', master: true },
        { key: 'os_type', label: '系统类型', type: 'radio', valueList: [
          {
            value: 'linux',
            label: 'Linux'
          },
          {
            value: 'windows',
            label: 'Windows'
          }
        ] },
        { key: 'os_name', label: '系统版本' }
      ],
      // 所有可配置显示列
      columnsObj: {
        'label': {
          title: '镜像名称', master: true
        },
        'size': {
          title: '容量'
        },
        'status': {
          title: '状态'
        },
        'type': {
          title: '系统类型'
        },
        'version': {
          title: '系统版本'
        },
        'network': {
          title: '网卡型号'
        }
      },
      // 当前显示列
      columnsViewArr: [
        'label',
        'size',
        'type',
        'version',
        'network',
        'status'
      ],
      tableData: [],
      tableTotal: 8,
      currentSelected: null
    }
  },
  mounted() {
    // 设置表格高度，考虑抽屉头部和底部
    this.getList()
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirmSelectImage() {
      if (this.selectItem && this.selectItem.length === 1) {
        this.$emit('call', 'select_image', this.selectItem[0])
      } else {
        this.$message.warning('请选择一个镜像')
      }
    },
    // 获取数据，使用API调用
    getList(showLoading = true) {
      if (showLoading) {
        this.loading = true
      }
      // 使用mixins中的getPostData方法构建请求参数
      const params = this.getPostData('page', 'limit')
      params.offset = this.pageCurrent - 1
      // 调用API获取镜像列表
      queryImagesAPI(params).then(res => {
        this.loading = false
        if (res.data.code === 0) {
          // 处理返回的数据
          const imageList = res.data.data.result || []
          // 转换为组件需要的格式
          this.tableData = imageList.map(image => ({
            value: image.image_id || '',
            label: image.name || '',
            size: image.size || 0,
            type: image.os_type || '',
            version: image.os_name || '',
            network: image.hw_vif_model || '',
            status: image.status || ''
          }))
          this.tableTotal = res.data.data.total
        }
      }).catch(error => {
        console.error(error)
        this.loading = false
      })
    },
    getStatusType(status) {
      // 根据状态返回对应的类型
      const statusMap = {
        'error': 'danger',
        'active': 'success',
        'creating': 'warning',
        'saving': 'warning'
      }
      return statusMap[status] || 'info'
    },
    handleSize(num, unit, fixed, outUnit) {
      const viewUnit = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      let viewNum = num
      let unitNum = viewUnit.indexOf(unit)
      if (!outUnit) {
        while (viewNum >= 1024) {
          viewNum = viewNum / 1024
          unitNum++
        }
        return viewNum.toFixed(fixed !== undefined ? fixed : 2) + ' ' + viewUnit[unitNum]
      } else {
        const outUnitNum = viewUnit.indexOf(outUnit)
        viewNum = viewNum / Math.pow(1024, (outUnitNum - unitNum))
        return viewNum.toFixed(fixed !== undefined ? fixed : 2) + ' ' + outUnit
      }
    },
    linkToUpload() {
      window.open('/os_network/vm_images_upload')
    }
  }
}
</script>

<style scoped lang="scss">
.status-success {
  color: #67C23A;
}
.status-warning {
  color: #E6A23C;
}
</style>
