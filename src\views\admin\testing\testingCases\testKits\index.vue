<template>
  <div class="content-wrap-layout">
    <div class="vertical-wrap">
      <page-table
        ref="table"
        :default-selected-arr="defaultSelectedArr"
        :filter-data="filterData"
        :cache-pattern="true"
        @refresh="refresh"
        @link-event="linkEvent"
        @on-select="tableSelect"
        @on-current="tableCurrent"
      >
        <action-menu
          slot="action"
          :module-name="moduleName"
          :select-item="selectItem"
          @call="actionHandler"
        />
      </page-table>
    </div>
    <el-drawer
      :visible.sync="detailShow"
      :modal="false"
      size="75%"
      append-to-body
      class="detail-view"
      @close="closeDetail"
    >
      <t-detail v-if="detailShow" />
    </el-drawer>
  </div>
</template>

<script>
import Tree from '@/packages/tree'
import actionMenu from './action/index'
import moduleConf from './config'
import pageTable from './table/index'
import tDetail from './detail/index.vue'
import moduleMixin from '@/packages/mixins/module_list'
export default {
  name: 'TestCasesManager',
  components: {
    pageTable,
    actionMenu,
    Tree,
    tDetail
  },
  mixins: [moduleMixin],
  data() {
    return {
      listRouterName: 'testKits',
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      filterData: {},
      // 树形分类数据
      categoryList: [],
      currentCategoryId: null
    }
  },
  created() {
  },
  mounted() {
    // 组件挂载后，设置默认选中第一个分类
    this.$nextTick(() => {
      if (this.categoryList.length > 0) {
        const firstCategoryId = this.categoryList[0].id
        this.currentCategoryId = firstCategoryId
        if (this.$refs.table) {
          this.$refs.table.setCategoryAndFetch(firstCategoryId)
        }
      }
    })
  },
  methods: {
    // 处理链接点击
    linkEvent(row) {
      // 导航到详情页
      this.$router.push({
        name: 'testCaseDetail',
        params: { id: row.id }
      })
    },

    // 处理表格多选
    tableSelect(selection) {
      this.selectItem = selection
    },

    // 处理表格单选
    tableCurrent(row) {
      this.selectItem = [row]
    },

    // 处理操作菜单事件
    actionHandler(type) {
      if (type === 'refresh') {
        this.$refs.table.refresh()
      }
    },

    // 处理树节点选择
    handleTreeNodeSelect(nodeId) {
      this.currentCategoryId = nodeId
      if (this.$refs.table) {
        this.$refs.table.setCategoryAndFetch(nodeId)
      }
    },

    // 获取分类列表
    getCategoryList() {
      // 刷新时调用的方法
      this.categoryList = [...this.categoryList]
    },

    // 刷新分类列表
    refreshCategoryList() {
      this.getCategoryList()
    },

    // 刷新（空函数，根据规范需要）
    refresh() {}
  }
}
</script>

<style lang="scss" scoped>
.content-wrap-layout {
  height: 100%;
  display: flex;
  flex-direction: column;

  .el-container {
    height: 100%;
  }

  .el-aside {
    background-color: #fff;
    border-right: 1px solid #e5e5e5;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .el-main {
    padding: 0;
    overflow: hidden;
    background-color: #fff;
  }
}

:deep(.left-tree) {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>

