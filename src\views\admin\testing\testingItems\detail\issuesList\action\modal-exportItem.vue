<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-radio v-model="type" label="search">按搜索结果导出</el-radio>
    <div style="padding: 10px 0px 10px 24px;">
      <el-row>
        <el-col :span="3">搜索项:</el-col>
        <el-col :span="21">
          <template v-if="searchView.length">
            <el-tag
              v-for="item in searchView"
              :key="item.key"
              class="ellipsis mr-5"
              style="max-width: 190px;"
              size="small"
            >
              <span v-if="['impactLevel', 'type', 'status'].includes(item.key)">
                {{ item.label }}：{{ item.displayValue }}
              </span>
              <span v-else>
                {{ item.label }}：{{ item.value }}
              </span>
            </el-tag>
          </template>
          <span v-else>无</span>
        </el-col>
      </el-row>
    </div>
    <el-radio v-model="type" label="all">导出全部</el-radio>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import batchTemplate from '@/packages/batch-delete/modal-bat-template'
import modalMixins from '@/packages/mixins/modal_form'
import { projectProblemExport } from '@/api/testing/index'
import { downloadExcelWithResData } from '@/utils'

export default {
  name: 'RemoveItem',
  components: { batchTemplate },
  mixins: [modalMixins],
  inject: ['tableVm'],
  props: {
    projectData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      moduleName: module.name,
      statusObj: module.statusObj, // 状态
      levelObj: module.levelObj, // 状态
      typeObj: module.typeObj, // 状态
      loading: false,
      type: 'search'
    }
  },
  computed: {
    'searchView': function() {
      const _data = []
      for (const key in this.tableVm.searchData) {
        const value = this.tableVm.searchData[key]
        let displayValue = value
        if (key === 'status') {
          displayValue = value
            .split(',')
            .map(val => (this.statusObj[String(val)] && this.statusObj[String(val)].label) || val)
            .join(',')
        }
        if (key === 'type') {
          displayValue = value
            .split(',')
            .map(val => (this.typeObj[String(val)] && this.typeObj[String(val)].label) || val)
            .join(',')
        }
        if (key === 'impactLevel') {
          displayValue = value
            .split(',')
            .map(val => (this.levelObj[String(val)] && this.levelObj[String(val)].label) || val)
            .join(',')
        }
        _data.push({
          key: key,
          value: value,
          label: this.tableVm.searchKeyList.find(item => item.key === key).label,
          displayValue
        })
      }
      return _data
    }
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      let params = {}
      if (this.$route.name == 'testing_detail') {
        params = Object.assign({ projectId: this.$route.params.id }, this.tableVm.filterData, this.type === 'search' ? this.tableVm.searchData : {})
      }
      if (this.$route.name == 'testingTask_detail') {
        params = Object.assign({ taskId: this.$route.params.id }, this.tableVm.filterData, this.type === 'search' ? this.tableVm.searchData : {})
      }
      if (params.impactLevel && typeof params.impactLevel === 'string') {
        params.impactLevel = params.impactLevel.split(',')
      }
      if (params.status && typeof params.status === 'string') {
        params.status = params.status.split(',')
      }
      if (params.type && typeof params.type === 'string') {
        params.type = params.type.split(',')
      }
      projectProblemExport(params).then((res) => {
        this.$message.success('导出问题清单成功')
        downloadExcelWithResData(res)
      })
      this.close()
    }
  }
}
</script>
