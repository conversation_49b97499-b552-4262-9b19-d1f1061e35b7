<template>
  <div class="drawer-wrap">
    <div class="resource-table" style="height: 100%;">
      <!-- 操作区 -->
      <div class="operation-wrap">
        <div class="operation-left">
          <slot name="action" />
          <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
        </div>
        <div class="operation-right">
          <el-badge :value="searchBtnShowNum">
            <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
          </el-badge>
          <!-- 自定义表格列 -->
          <t-table-config
            v-if="!customColData.length"
            :data="columnsObj"
            :active-key-arr="columnsViewArr"
            @on-change-col="onChangeCol"
          />
        </div>
      </div>
      <!-- 搜索区 -->
      <t-search-box
        v-show="searchView"
        :search-key-list="searchKeyListView"
        default-placeholder="默认搜索名称"
        @search="searchMultiple"
      />
      <!-- 列表 -->
      <t-table-view
        ref="tableView"
        :height="height"
        :single="true"
        :loading="tableLoading"
        :data="tableData"
        :total="tableTotal"
        :page-size="pageSize"
        :current="pageCurrent"
        :select-item="selectItem"
        current-key="id"
        @on-select="onSelect"
        @on-current="onCurrent"
        @on-change="changePage"
        @on-sort-change="onSortChange"
        @on-page-size-change="onPageSizeChange"
      >
        <el-table-column v-for="item in columnsViewArr" :key="item" :min-width="colMinWidth" :label="columnsObj[item].title" :fixed="columnsObj[item].master ? 'left' : false" :show-overflow-tooltip="item !== 'sharingModel'">
          <template slot-scope="scope">
            <span v-if="item == 'sceneDifficulty'">{{ difficulty[scope.row[item] - 1] || '-' }}</span>
            <span v-else-if="item == 'sceneTypeId'">{{ sceneTypeList.filter(e => { return e.id == scope.row[item] }).length ? sceneTypeList.filter(e => { return e.id == scope.row[item] })[0].typeName : '-' }}</span>
            <span v-else-if="item == 'initRoleVOList'">
              <span v-if="scope.row[item] && scope.row[item].length && scope.row.needRole">
                <span v-for="(role, index) in scope.row[item]" :key="index">{{ role.initRoleName }}<span v-if="index + 1 != scope.row[item].length">,</span></span>
              </span>
              <span v-else>无</span>
            </span>
            <span v-else-if="item == 'sharingModel'">
              <span v-if="scope.row[item] == 1">不共享</span>
              <span v-if="scope.row[item] == 2">全局共享</span>
              <span v-if="scope.row[item] == 3">
                <table-td-multi-col :data="scope.row.shareUserRelVOList" :number="scope.row.shareUserRelVOList.length">
                  <div slot="reference">共享给：{{ scope.row.shareUserRelVOList[0].realName }}</div>
                  <div v-for="(val, index) in scope.row.shareUserRelVOList" :key="index">{{ val.realName }}</div>
                </table-td-multi-col>
              </span>
            </span>
            <a v-else-if="link && item === 'sceneName'" href="javascript:;" @click.stop="openNewPageInTab('scenement_detail', scope.row, { id: scope.row.id, view: 'overview' })">
              {{ scope.row[item] || '-' }}
            </a>
            <span v-else-if="item === 'sceneMode' && scope.row[item]">
              {{ scope.row[item] == 1 ? '单兵' : '团体' }}
            </span>
            <span v-else>{{ scope.row[item] || "-" }}</span>
          </template>
        </el-table-column>
      </t-table-view>
    </div>
    <div class="drawer-footer" style="margin-top: 10px;">
      <el-button :disabled="!selectItem.length != 0" type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script>
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { sceneQueryPageAPI, sceneTypePageAPI } from '@/api/testing/index'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  props: {
    selectScene: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      moduleName: 'TableScenarioTesting',
      // 搜索配置项
      searchKeyList: [
        { key: 'sceneName', label: '名称', master: true },
        { key: 'sceneDifficulty', label: '难度', type: 'radio', valueList: [{ label: '初级', value: '1' }, { label: '中级', value: '2' }, { label: '高级', value: '3' }] },
        { key: 'sceneMode', label: '模式', type: 'radio', valueList: [{ label: '单兵', value: '1' }, { label: '团体', value: '2' }] },
        { key: 'sceneTypeName', label: '类别' },
        { key: 'initRoleNameList', label: '角色阵营', type: 'select', valueList: [{ label: '红方', value: '红方' }, { label: '蓝方', value: '蓝方' }, { label: '白方', value: '白方' }, { label: '黄方', value: '黄方' }, { label: '绿方', value: '绿方' }, { label: '无', value: '无' }] },
        { key: 'createByName', label: '创建者', master: true },
        { key: 'time', label: '创建时间', type: 'time_range' }
      ],
      difficulty: ['初级', '中级', '高级'],
      sceneTypeList: [],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'sceneName': {
          title: '名称', master: true
        },
        'sceneDifficulty': {
          title: '难度'
        },
        'nodeScale': {
          title: '节点规模'
        },
        'suggestTime': {
          title: '建议时长（小时）'
        },
        'sceneTypeId': {
          title: '类别'
        },
        'sceneMode': {
          title: '模式'
        },
        'initRoleVOList': {
          title: '角色阵营'
        },
        'sharingModel': {
          title: '共享模式'
        },
        'createByName': {
          title: '创建者'
        },
        'createTime': {
          title: '创建时间'
        }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'sceneName',
        'sceneTypeId',
        'sceneMode',
        'initRoleVOList',
        'sharingModel',
        'sceneDifficulty',
        'nodeScale',
        'suggestTime',
        'createByName',
        'createTime'
      ]
    }
  },
  created() {
    this.getSceneTypePage()
  },
  methods: {
    confirm() {
      if (this.selectItem && this.selectItem.length === 1) {
        this.$emit('call', 'select_scenario', this.selectItem[0])
      } else {
        this.$message.warning('请选择一个场景')
      }
    },
    cancel() {
      this.$emit('close')
    },
    openNewPageInTab(name, row, params) {
      if (this.selectScene) {
        const url = this.$router.resolve({ name: name, params: params }).href
        window.open(url, '_blank') // '_blank' 表示在新标签页中打开
      } else {
        this.linkEvent('scenement_detail', row, { id: row.id, view: 'overview' })
      }
    },
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      if (params.time) {
        params.createTimeBegin = params.time.split(',')[0]
        params.createTimeEnd = params.time.split(',')[1]
      }
      params.pageType = 1
      sceneQueryPageAPI(params).then(res => {
        this.tableData = res.data.data.records
        this.tableTotal = Number(res.data.data.total)
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
      })
    },
    getSceneTypePage() {
      sceneTypePageAPI({ pageType: 0 }).then(res => {
        this.sceneTypeList = res.data.data.records
      })
    }
  }
}
</script>
<style scoped lang="scss">

</style>

