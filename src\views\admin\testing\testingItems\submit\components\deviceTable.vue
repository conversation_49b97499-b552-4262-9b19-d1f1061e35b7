<!--
设备信息表格组件 - 本地数据版本
改造说明：
1. 将原来的实时API调用改为本地数据存储
2. 实现了本地分页功能，数据存储在 localDevices 中
3. 通过 computed 属性 paginatedData 实现分页显示
4. 提供了 addDevice, updateDevice, removeDeviceLocal 等方法操作本地数据
5. 通过 syncToParent 方法保持与父组件数据同步
6. 所有增删改操作都在本地进行，不再调用后端API
7. 数据只在最终提交时发送到后端
-->
<template>
  <div class="resource-table" style="height: 100%; padding: 0;">
    <!-- 设备配置列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="paginatedData"
      :total="deviceTableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      class="tableView"
      type="list"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        :show-overflow-tooltip="columnsObj[item].showOverflowTooltip"
      >
        <template slot-scope="scope">
          <div v-if="item == 'index'">
            {{ getDisplayIndex(scope.$index) }}
          </div>
          <div v-else-if="item == 'deviceAccountBOList'">
            <div class="flex jc-between ai-center">
              <el-tooltip v-if="scope.row.deviceAccountBOListStr && scope.row.deviceAccountBOListStr.length > 0" :content="scope.row.deviceAccountBOListStr[0].name" class="item" effect="dark" placement="top">
                <div class="ellipsis">{{ scope.row.deviceAccountBOListStr[0].name || "-" }}</div>
              </el-tooltip>
              <CountPopover :list="scope.row.deviceAccountBOListStr" :show-name="'name'" />
            </div>
          </div>
          <div v-else-if="item == 'handle'">
            <el-button
              type="text"
              style="color: var(--color-600)"
              size="small"
              @click="editDevice('edit', scope.row)"
            >编辑</el-button>
            <el-button
              type="text"
              style="color: var(--color-600)"
              size="small"
              @click="removeDevice(getOriginalIndex(scope.$index))"
            >删除</el-button>
          </div>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import moduleConf from '../config'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import CountPopover from '@/components/testing/CountPopover'
export default {
  components: {
    tTableView,
    CountPopover,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  props: {
    projectId: {
      type: String,
      default: ''
    }
  },
  inject: ['testSubmitVm'],
  data() {
    return {
      searchKeyList: [],
      columnsObj: moduleConf.deviceColumnsObj,
      columnsViewArr: moduleConf.deviceColumnsViewArr,
      // 本地设备数据存储
      localDevices: [],
      // 下一个ID计数器（用于新增数据的临时ID）
      nextTempId: 1
    }
  },
  computed: {
    // 计算分页后的数据
    paginatedData() {
      const start = (this.pageCurrent - 1) * this.pageSize
      const end = start + this.pageSize
      return this.localDevices.slice(start, end)
    },
    // 更新总数为本地数据的长度
    deviceTableTotal() {
      return this.localDevices.length
    }
  },
  watch: {
    // 监听testSubmitVm中的设备数据变化，同步到本地
    'testSubmitVm.formData.devices': {
      handler(newVal) {
        if (!Array.isArray(newVal)) return
        this.localDevices = JSON.parse(JSON.stringify(newVal))
        this.formatDeviceAccounts(this.localDevices)
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    // 初始化时如果有数据，同步到本地
    if (this.testSubmitVm.formData.devices) {
      this.localDevices = [...this.testSubmitVm.formData.devices]
    }
  },
  methods: {
    formatDeviceAccounts(devices) {
      devices.forEach(device => {
        const accounts = device.deviceAccountVOList || device.deviceAccountBOList || []

        device.deviceAccountBOListStr = accounts.map(account => ({
          name: `${account.username}/${account.password}`
        }))
      })
    },
    // 重写getList方法，改为本地数据处理
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }

      // 模拟异步加载
      setTimeout(() => {
        this.tableLoading = false
        // 同步本地数据到父组件
        this.testSubmitVm.formData.devices = [...this.localDevices]
      }, 100)
    },

    // 添加设备到本地数据
    addDevice(deviceData) {
      this.localDevices.push(deviceData)
      this.syncToParent()

      // 如果当前页显示不下新数据，跳到最后一页
      const totalPages = Math.ceil(this.localDevices.length / this.pageSize)
      if (this.pageCurrent < totalPages) {
        this.pageCurrent = totalPages
      }
    },

    // 编辑设备
    editDevice(type, data) {
      // 添加原始索引属性
      const originalIndex = this.getOriginalIndex(this.paginatedData.indexOf(data))
      const dataWithIndex = { ...data, _index: originalIndex }
      this.$emit('editDevice', type, dataWithIndex)
    },

    // 删除设备
    removeDevice(index) {
      this.$emit('removeDevice', index)
    },

    // 从本地数据中删除设备
    removeDeviceLocal(index) {
      if (index >= 0 && index < this.localDevices.length) {
        this.localDevices.splice(index, 1)
        this.syncToParent()

        // 如果当前页没有数据了，回到上一页
        if (this.paginatedData.length === 0 && this.pageCurrent > 1) {
          this.pageCurrent = this.pageCurrent - 1
        }
      }
    },

    // 更新设备数据
    updateDevice(index, deviceData) {
      if (index >= 0 && index < this.localDevices.length) {
        this.$set(this.localDevices, index, deviceData)
        this.syncToParent()
      }
    },

    // 同步本地数据到父组件
    syncToParent() {
      this.testSubmitVm.formData.devices = [...this.localDevices]
    },

    // 获取显示的序号（考虑分页）
    getDisplayIndex(pageIndex) {
      return (this.pageCurrent - 1) * this.pageSize + pageIndex + 1
    },

    // 获取原始数组中的索引
    getOriginalIndex(pageIndex) {
      return (this.pageCurrent - 1) * this.pageSize + pageIndex
    },

    // 重写changePage方法
    changePage(num) {
      this.pageCurrent = num
    },

    // 重写onPageSizeChange方法
    onPageSizeChange(pageSize) {
      this.pageSize = pageSize
      // 如果当前页超出了新的页数范围，调整到最后一页
      const maxPage = Math.ceil(this.localDevices.length / pageSize)
      if (this.pageCurrent > maxPage && maxPage > 0) {
        this.pageCurrent = maxPage
      }
    }
  }
}
</script>
<style lang="scss">
  .tableView {
    min-height: 290px;
  }
</style>
