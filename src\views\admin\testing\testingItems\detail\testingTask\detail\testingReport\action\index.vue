<template>
  <div class="buttons-wrap">
    <el-button
      v-permission="'manage.testing.project.projectDetail.projectTask.taskDetailView.taskReport.testingFileUploadReport'"
      :disabled="detailData.pendingStatus == 9"
      type="primary"
      @click="clickDrop('uploadReport')"
    >上传测试报告</el-button>
    <el-button
      v-permission="'manage.testing.project.projectDetail.projectTask.taskDetailView.taskReport.testingReportTaskDownModelBy'"
      type="primary"
      @click="clickDrop('downloadTemplate')"
    >下载报告模版</el-button>
    <!-- 弹窗 -->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          :project-data="projectData"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
  </div>
</template>

<script>
import { downModelByTaskId } from '@/api/testing/index'
import uploadReport from './modal-uploadReport.vue'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import { fileDownload } from '@/packages/utils/downloadAndPreview.js'
import { testingItemsDetailAPI } from '@/api/testing/index'
export default {
  name: 'ActionMenu',
  components: {
    uploadReport
  },
  mixins: [mixinsActionMenu],
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    moduleName: {
      type: String,
      default: ''
    },
    selectItem: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      detailData: {}, // 详情数据
      drawerAction: [], // 需要侧拉打开的操作
      // 弹窗title映射
      titleMapping: {
        'uploadReport': '上传测试报告'
      },
      projectData: {}
    }
  },
  created() {
    this.projectData = this.data
    this.getData()
  },
  methods: {
    // 根据id获取详情数据
    'getData': function() {
      this.loading = true
      this.id = this.$route.name === 'testingTask_detail' ? this.$route.params.projectId : this.$route.params.id
      testingItemsDetailAPI(this.id).then(res => {
        if (res.data && res.data.code === 0) {
          this.detailData = res.data.data
        } else {
          this.$message.error(res.data.msg || '获取项目详情失败')
        }
        this.loading = false
      })
    },
    downloadTemplate() {
      const data = {
        taskId: this.$route.params.id
      }
      downModelByTaskId(data).then(res => {
        if (res.data.data == '暂无模板') {
          this.$message.warning(res.data.data)
          return
        }
        const url = res.data.data
        const ext = url.substring(url.lastIndexOf('.'))
        const fileName = `${this.data.name}的报告模板${ext}`
        fileDownload(url, fileName)
      })
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    'clickDrop': function(name) {
      if (this.drawerAction && this.drawerAction.includes(name)) {
        this.drawerName = name
      } else if (name === 'downloadTemplate') {
        this.downloadTemplate()
      } else {
        this.modalName = name
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.action-menu {
  display: inline-block;
}
</style>
