export default {
  name: 'selectCase',
  // 优先级映射
  priorityArr: [
    { label: '低', value: '1', type: 'info' },
    { label: '中', value: '2', type: 'warning' },
    { label: '高', value: '3', type: 'danger' }
  ],
  typeArr: [
    { label: '功能测试', value: '1' },
    { label: '性能测试', value: '2' },
    { label: '安全测试', value: '3' }
  ],

  get priorityObj() {
    return this.priorityArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  },
  get typeObj() {
    return this.typeArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  }
}
