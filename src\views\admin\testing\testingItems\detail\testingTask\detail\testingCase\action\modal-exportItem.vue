<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-radio v-model="type" label="search">按搜索结果导出</el-radio>
    <div style="padding: 10px 0px 10px 24px;">
      <el-row>
        <el-col :span="3">搜索项:</el-col>
        <el-col :span="21">
          <template v-if="searchView.length">
            <el-tag
              v-for="item in searchView"
              :key="item.key"
              :title="`${item.label}：${ item.value }`"
              class="ellipsis mr-5"
              style="max-width: 190px;"
              size="small"
            >
              <span>
                {{ item.label }}：{{ item.value }}
              </span>
            </el-tag>
          </template>
          <span v-else>无</span>
        </el-col>
      </el-row>
    </div>
    <el-radio v-model="type" label="all">导出全部</el-radio>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import batchTemplate from '@/packages/batch-delete/modal-bat-template'
import modalMixins from '@/packages/mixins/modal_form'
import { taskCaseExport, caseProjectPage } from '@/api/testing/testCase.js'
import { downloadExcelWithResData } from '@/utils'

export default {
  name: 'RemoveItem',
  components: { batchTemplate },
  mixins: [modalMixins],
  inject: ['tableVm'],
  data() {
    return {
      moduleName: module.name,
      loading: false,
      type: 'search',
      projectList: []
    }
  },
  computed: {
    'searchView': function() {
      const _data = []
      const vm = this.tableVm
      for (const key in vm.searchParams) {
        if (key === 'type') {
          // 处理type字段，将数字转换为对应的标签名称
          const typeValues = vm.searchParams[key].split(',')
          const typeLabels = typeValues.map(val => {
            const typeItem = module.typeArr.find(item => item.value === val)
            return typeItem ? typeItem.label : val
          }).join(',')

          _data.push({
            key: key,
            value: typeLabels, // 使用转换后的标签名称
            label: vm.searchKeyList.find(item => item.key === key).label
          })
        } else if (key === 'priority') {
          const priorityValues = vm.searchParams[key].split(',')
          const priorityLabels = priorityValues.map(val => {
            const priorityItem = module.priorityArr.find(item => item.value === val)
            return priorityItem ? priorityItem.label : val
          }).join(',')

          _data.push({
            key: key,
            value: priorityLabels, // 使用转换后的标签名称
            label: vm.searchKeyList.find(item => item.key === key).label
          })
        } else if (key === 'projectName') {
          const projectValues = vm.searchParams[key].split(',')
          const projectLabels = projectValues.map(val => {
            const projectItem = this.projectList.find(item => item.value === val)
            return projectItem ? projectItem.label : val
          }).join(',')

          _data.push({
            key: key,
            value: projectLabels, // 使用转换后的标签名称
            label: vm.searchKeyList.find(item => item.key === key).label
          })
        } else {
          _data.push({
            key: key,
            value: vm.searchParams[key],
            label: vm.searchKeyList.find(item => item.key === key).label
          })
        }
      }
      return _data
    }
  },
  async created() {
    await this.getProjectList()
  },
  methods: {
    async getProjectList() {
      const res = await caseProjectPage({})
      if (res && res.code === 0) {
        this.projectList = res.data.records.map(item => ({
          label: item.projectName,
          value: String(item.id)
        }))
      }
    },
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      const vm = this.tableVm
      this.loading = true
      let params = vm.requestParams
      params.pageType = 0
      if (this.type == 'all') {
        params = { pageType: 0, categoryId: params.categoryId, taskId: params.taskId }
      }
      taskCaseExport(params).then((res) => {
        this.close()
        this.loading = false
        this.$message.success('导出成功')
        downloadExcelWithResData(res)
      })
    }
  }
}
</script>
