export const resourcesConfigMap = {
  drill_type: {
    svgIcon: { name: '', size: '' }, // TODO 组件化
    total: { key: 'total', label: '总数量' },
    unit: '',
    options: [
      { label: '单兵演练', type: 'primary', countKey: 'individualDrillNum' },
      { label: '团体演练', type: 'warning', countKey: 'groupDrillNum' }
    ]
  },
  node_size: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    unit: '',
    options: [
      { label: '单兵演练', type: 'primary', countKey: 'individualNodeScale' },
      { label: '团体演练', type: 'warning', countKey: 'groupNodeScale' }
    ]
  },
  // team_drill_form: {
  //   svgIcon: { name: '', size: '' },
  //   total: { key: 'total', label: '总数量' },
  //   options: [
  //     { label: '以个人形式', type: 'primary', countKey: 'todoTaskCount' },
  //     { label: '以战队形式', type: 'warning', countKey: 'todoTaskCount' }
  //   ]
  // },
  single_drill_status: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    options: [
      { label: '未开始', type: 'info', countKey: 'individualDrillNoStart' },
      { label: '进行中', type: 'success', countKey: 'individualDrillProgress' },
      { label: '已暂停', type: 'danger', countKey: 'individualDrillStop' },
      { label: '已结束', type: 'info', countKey: 'individualDrillEnd' },
      { label: '其他', type: 'info', countKey: 'individualDrillOther' }
    ]
  },
  single_drill_level: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    unit: '',
    options: [
      { label: '初级', type: 'success', countKey: 'individualPrimaryDifficulty' },
      { label: '中级', type: 'warning', countKey: 'individualMiddleDifficulty' },
      { label: '高级', type: 'danger', countKey: 'individualHighDifficulty' }
    ]
  },
  team_drill_status: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    options: [
      { label: '未开始', type: 'info', countKey: 'groupDrillNoStart' },
      { label: '进行中', type: 'success', countKey: 'groupDrillProgress' },
      { label: '已暂停', type: 'danger', countKey: 'groupDrillStop' },
      { label: '已结束', type: 'info', countKey: 'groupDrillEnd' },
      { label: '其他', type: 'info', countKey: 'groupDrillOther' }
    ]
  },
  team_drill_level: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    unit: '',
    options: [
      { label: '初级', type: 'success', countKey: 'groupPrimaryDifficulty' },
      { label: '中级', type: 'warning', countKey: 'groupMiddleDifficulty' },
      { label: '高级', type: 'danger', countKey: 'groupHighDifficulty' }
    ]
  },

  // top 统计
  single_drill_times_top: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    unit: '次',
    options: [
      { label: '', type: 'success', viewName: 'name', viewValue: 'num' }
    ]
  },
  team_drill_times_top: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    unit: '次',
    options: [
      { label: '', type: 'success', viewName: 'name', viewValue: 'num' }
    ]
  },
  team_drill_size_top: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    unit: '人',
    options: [
      { label: '', type: 'success', viewName: 'name', viewValue: 'num' }
    ]
  },

  // 检测项目数量统计
  test_project_count: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    unit: '',
    options: [
      { label: '总数量', type: 'primary', countKey: 'total', icon: 'cr-icon-jcsmzs' },
      { label: '去年', type: 'info', countKey: 'lastYear', icon: 'cr-icon-jcsm-wtqn' },
      // 今年也用去年的icon，代码做处理
      { label: '今年', type: 'success', countKey: 'currentYear', icon: 'cr-icon-jcsm-wtqn' }
    ]
  },
  // 厂商人员检测项目
  vendor_test_project: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    unit: '',
    options: [
      { label: '未完成', type: 'danger', countKey: 'unfinished', icon: '' },
      { label: '已完成', type: 'success', countKey: 'finished', icon: '' }
    ]
  },
  // 问题数量统计
  test_pass_rate: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总体' },
    unit: '',
    options: [
      { label: '总体', type: 'primary', countKey: 'total', icon: 'cr-icon-wtzt' },
      { label: '去年', type: 'info', countKey: 'lastYear', icon: 'cr-icon-jcsm-wtqn' },
      { label: '今年', type: 'danger', countKey: 'currentYear', icon: 'cr-icon-wtjn' }
    ]
  },
  test_application: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    unit: '',
    options: [
      { label: '总数量', type: 'primary', countKey: 'total' },
      { label: '未完成', type: 'danger', countKey: 'unfinished' },
      { label: '已完成', type: 'success', countKey: 'finished' }
    ]
  },
  // 我参与的
  participate: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    unit: '',
    options: [
      { label: '检测项目', countKey: 'testProjectNum', icon: 'cr-icon-wcydjcxm' },
      { label: '检测任务', countKey: 'testTaskNum', icon: 'cr-icon-wcydcsrw' }
    ]
  },
  unfinished_test_project: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    unit: '',
    statusList: [
      { label: '资料待送审', type: 'warning', countKey: 'count' },
      { label: '环境待部署', type: 'info', countKey: 'count' },
      { label: '待测试', type: 'warning', countKey: 'count' },
      { label: '测试中', type: 'primary', countKey: 'count' }
    ]
  },

  // 待办
  my_todo: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    options: [
      { label: '检测申请', type: 'info', countKey: 'detectionApplicationSum', count: 4 },
      { label: '待创建检测项目', type: 'primary', countKey: 'pendingCreateProject', count: 2 },
      { label: '检测项目', type: 'warning', countKey: 'testProjects', count: 0 },
      { label: '待审核测试报告', type: 'success', countKey: 'pendingReviewReport', count: 12 }
    ]
  },

  // 资源趋势
  testing_project_progress: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    unit: '%',
    options: [
      { label: '进度', type: 'success', countKey: 'projectProgress' }
    ]
  },
  unclosed_issues: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    unit: '',
    options: [
      { label: '未关闭问题', type: 'danger', countKey: 'unclosedIssues' }
    ]
  },
  test_cases: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    unit: '',
    options: [
      { label: '测试用例', type: 'primary', countKey: 'testCases' }
    ]
  },
  issue_statistics: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    unit: '',
    options: [
      { label: '严重', type: 'danger', countKey: 'severeIssues' },
      { label: '一般', type: 'warning', countKey: 'normalIssues' },
      { label: '轻微', type: 'info', countKey: 'minorIssues' }
    ]
  },

  // 资源排行
  project_issue_count_rank: {
    svgIcon: { name: '', size: '' },
    total: { key: 'total', label: '总数量' },
    unit: '个',
    options: [
      { label: '', type: 'success', viewName: 'projectName', viewValue: 'issueCount' }
    ]
  }
}
