<template>
  <div class="plugin-view">
    <h3 class="plugin-title">{{ pluginTitle }}</h3>
    <div v-loading="true" v-if="loading" class="plugin-loading" />
    <el-empty v-else-if="!apiData" :image="noDataImg" :image-size="120" style="padding: 0;height: 100%;" description="暂无数据" />
    <el-empty v-else-if="apiData.categories.length === 0" :image="noDataImg" :image-size="120" style="padding: 0;height: 100%;" description="暂无数据" />
    <div v-else class="test-case-wrap">
      <div ref="testCaseChart" class="test-case-chart"/>
    </div>
  </div>
</template>

<style lang="scss">
.test-case-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 15px;
  overflow: hidden;

  .test-case-chart {
    flex: 1;
    min-height: 220px;
  }
}
</style>

<script>
import { executeCase } from '@/api/testing/testingOverview'
import * as echarts from 'echarts'
import pluginMixin from './mixin_plugin.js'

export default {
  mixins: [pluginMixin],
  props: {
    processId: {
      type: String,
      default: ''
    },
    currentRole: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      noDataImg: require('@/packages/table-view/nodata.png'),
      chart: null,
      apiData: null
    }
  },
  watch: {
    'pluginApiType': {
      handler() {
        this.$nextTick(() => {
          this.resizeChart()
        })
      }
    },
    'processId': {
      handler(val) {
        this.getData()
      },
      immediate: true
    },
    'currentRole': {
      handler(val) {
        this.getData()
      },
      immediate: true
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.apiData || !this.apiData.categories || this.apiData.categories.length === 0) return

      this.$nextTick(() => {
        // 初始化图表
        this.chart = echarts.init(this.$refs.testCaseChart)

        // 颜色映射
        const colorMap = {
          '安全测试': '#91cc75',
          '功能测试': '#fac858',
          '性能测试': '#73c0de '
        }

        // 准备数据
        const chartData = this.apiData.categories.map(item => ({
          name: item.typeName,
          value: item.count,
          itemStyle: {
            color: colorMap[item.typeName] || this.getRandomColor()
          }
        }))

        // 设置图表选项
        const option = {
          tooltip: {
            trigger: 'item'
          },
          series: [
            {
              type: 'pie',
              radius: ['50%', '70%'],
              center: ['50%', '50%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: true,
                position: 'outside',
                formatter: '{b}\n{c}个',
                fontSize: 12
              },
              labelLine: {
                show: true,
                length: 10,
                length2: 10
              },
              emphasis: {
                scale: true,
                scaleSize: 10
              },
              data: chartData
            }
          ],
          graphic: [
            {
              type: 'text',
              left: 'center',
              top: '43.5%',
              style: {
                text: this.apiData.total.toString() + '个',
                textAlign: 'center',
                fill: '#000',
                fontSize: 28,
                fontWeight: 'bold'
              }
            }
          ]
        }

        this.chart.setOption(option)

        // 添加窗口大小变化监听器
        window.addEventListener('resize', this.resizeChart)
      })
    },
    resizeChart() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    getRandomColor() {
      const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
      return colors[Math.floor(Math.random() * colors.length)]
    },
    /**
     * 获取测试用例数据
     *
     * @param {boolean} hideLoading - 是否隐藏加载状态
     * @API GET /api/testing/test-cases-summary
     * @response {
     *   total: number,
     *   categories: Array<{
     *     typeName: string,
     *     count: number
     *   }>
     * }
     */
    getData(hideLoading) {
      if (!hideLoading) {
        this.loading = true
      }

      const params = {
        processId: this.processId,
        roleId: this.currentRole
      }

      executeCase(params).then(res => {
        if (res.code === 0 && res.data) {
          this.apiData = res.data
        } else {
          this.apiData = null
        }
        this.loading = false
        this.$nextTick(() => {
          this.initChart()
        })
      }).catch(() => {
        this.apiData = null
        this.loading = false
      })
    }
  }
}
</script>
