<template>
  <div class="resource-table" style="padding: 0;">
    <split-pane ref="split-pane" :min-percent="minPercent" :default-percent="percent" split="vertical" @resize="resize">
      <div
        slot="paneL"
        class="collapse-transition"
        style="padding-right: 0;height: 100%;"
      >
        <div class="tree-container">
          <tree
            ref="treeRef"
            :tree-width="treeWidth"
            :i-search="false"
            :tree-data="treeData"
            :default-props="defaultProps"
            :default-expanded-keys="treeData.length > 0 ? [treeData[0].id] : []"
            :default-checked-keys="treeData.length > 0 ? [treeData[0].id] : []"
            :is-show-dropdown="false"
            :is-show-data-count="true"
            :is-show-children-num="false"
            @currentTreeNode="currentTreeNode"
            @clearCurrentNode="clearCurrentNode"
          />
        </div>
      </div>
      <div
        slot="paneR"
        ref="tableColRef"
        class="resource-table collapse-transition"
      >
        <div class="operation-wrap">
          <div class="operation-left">
            <slot name="action" />
            <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
          </div>
          <div class="operation-right">
            <el-badge :value="searchBtnShowNum">
              <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
            </el-badge>
            <!-- 自定义表格列 -->
            <t-table-config
              v-if="!customColData.length"
              :data="columnsObj"
              :active-key-arr="columnsViewArr"
              @on-change-col="onChangeCol"
            />
          </div>
        </div>
        <div
          class="collapse-btn"
          @click="toggleCollapse"
        >
          <i
            :class="fold ? 'el-icon-caret-right' : 'el-icon-caret-left'"
          />
        </div>
        <!-- 搜索区 -->
        <t-search-box
          v-show="searchView"
          :search-key-list="searchKeyListView"
          default-placeholder="默认搜索套件名称"
          @search="searchMultiple"
        />
        <!-- 列表 -->
        <t-table-view
          ref="tableView"
          :single="true"
          :height="height"
          :loading="tableLoading"
          :data="tableData"
          :total="tableTotal"
          :page-size="pageSize"
          :current="pageCurrent"
          :select-item="selectItem"
          current-key="id"
          @on-select="onSelect"
          @on-current="onCurrent"
          @on-change="changePage"
          @on-sort-change="onSortChange"
          @on-page-size-change="onPageSizeChange"
        >
          <el-table-column
            v-for="item in columnsViewArr"
            :key="item"
            :min-width="columnsObj[item].colMinWidth || colMinWidth"
            :width="columnsObj[item].colWidth"
            :label="columnsObj[item].title"
            :fixed="columnsObj[item].master ? 'left' : false"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span v-if="item === 'suiteName'">
                <a :href="`/testing/testingKitDetail/${scope.row.id}/overview?routeSearch=true&searchVal=${scope.row.suiteName}&searchKey=suiteName&moduleName=testKits`" target="_blank">
                  {{ scope.row[item] || '-' }}
                </a>
              </span>
              <span v-else-if="item === 'caseCount'">
                {{ scope.row[item].toString() || "-" }}
              </span>
              <span v-else>
                {{ scope.row[item] || '-' }}
              </span>
            </template>
          </el-table-column>
        </t-table-view>
      </div>
    </split-pane>
  </div>
</template>

<script>
import { caseCategoryTreeUrlName, taskCaseSuitePage } from '@/api/testing/testCase.js'
import mixinsPageTable from '@/packages/mixins/page_table'
import splitPane from '@/packages/mixins/split-pane'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import tTableView from '@/packages/table-view/index.vue'
import tree from '@/packages/tree/index.vue'
import module from '../config.js'

export default {
  components: {
    tree,
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable, splitPane],
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'label',
        count: 'count'
      },
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'suiteName', label: '名称', master: true },
        { key: 'categoryName', label: '分类' },
        { key: 'createByName', label: '创建人' }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'suiteName': {
          title: '名称',
          master: true,
          sortable: true
        },
        'categoryName': {
          title: '分类',
          sortable: true
        },
        'caseCount': {
          title: '用例数量',
          sortable: true,
          colWidth: 80
        },
        'createByName': {
          title: '创建人',
          sortable: true,
          colWidth: 120
        }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'suiteName',
        'categoryName',
        'caseCount',
        'createByName'
      ],
      // 树相关
      currentNodeId: '0',
      treeData: [],
      searchParams: {},
      treeWidth: 210
    }
  },
  computed: {
    'searchBtnShowNum': function() { // 搜索项的数量
      if (this.searchView) return null
      return Object.keys(this.searchParams).length || null
    }
  },
  async created() {
    await this.getTreeData()
    // 初始加载数据
    this.initDefaultTree()
  },
  methods: {
    toggleCollapse() {
      this.fold = !this.fold
      if (this.fold) {
        this.percent = this.minPercent
      } else {
        this.percent = 20
      }
    },
    // 初始化默认树选择
    initDefaultTree() {
      // 使用树的根分类ID
      if (this.treeData && this.treeData.length > 0) {
        this.currentNodeId = this.treeData[0].id
      } else {
        this.currentNodeId = '0' // 如果没有树数据，使用默认值
      }
      // 加载数据
      this.getList()
      // 确保根节点被选中
      this.$nextTick(() => {
        if (this.$refs.treeRef) {
          this.$refs.treeRef.setCurrentKey(this.currentNodeId)
        }
      })
    },
    clearCurrentNode() {
      // 重置为根分类
      if (this.treeData && this.treeData.length > 0) {
        this.currentNodeId = this.treeData[0].id
      } else {
        this.currentNodeId = '0'
      }
      this.getList()
    },
    currentTreeNode(data, node) {
      if (data && data.id !== undefined) {
        this.currentNodeId = data.id
        // 保留当前搜索条件，结合树节点进行搜索
        this.getList(this.searchParams)
      }
    },
    // 转换树结构数据，将id作为nodeKey，name作为label
    transformTreeData(data) {
      if (!data || !Array.isArray(data)) return []

      return data.map(item => {
        const node = {
          nodeKey: item.id,
          label: item.name || '未命名分类',
          id: item.id,
          parentId: item.parentId,
          type: item.type,
          count: item.count
        }

        if (
          item.children &&
          Array.isArray(item.children) &&
          item.children.length > 0
        ) {
          node.children = this.transformTreeData(item.children)
        }

        return node
      })
    },
    // 获取分类树结构
    async getTreeData() {
      try {
        this.tableLoading = true
        const params = {
          type: 2, // 套件分类类型为2
          busId: this.$route.params.id,
          urlName: 'project-task-case-add-suite'
        }
        const res = await caseCategoryTreeUrlName(params)

        if (res && res.data) {
          // 转换树结构数据
          this.treeData = this.transformTreeData(res.data)
        } else {
          this.treeData = []
        }
      } catch (error) {
        console.error('获取分类树结构失败:', error)
        this.treeData = []
      } finally {
        this.tableLoading = false
      }
    },
    getList(params = {}, showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }

      this.searchParams = { ...params }
      const requestParams = this.getPostData('page', 'limit')

      // 合并传入的参数
      if (params && Object.keys(params).length > 0) {
        Object.assign(requestParams, params)
      }

      requestParams.taskId = this.$route.params.id

      // 添加分类ID
      if (this.currentNodeId !== undefined && this.currentNodeId !== '0') {
        requestParams.categoryId = this.currentNodeId
      }

      taskCaseSuitePage(requestParams).then(res => {
        this.tableData = res.data.records
        this.tableTotal = res.data.total
        this.handleSelection()
      }).finally(() => {
        this.tableLoading = false
      })
    },
    // 处理搜索
    searchMultiple(params) {
      this.pageSize = 10
      this.pageCurrent = 1
      this.getList(params)
    }
  }
}
</script>

<style lang="scss" scoped>
.resource-table {
  height: 100%;
  padding-left: 0;

  .collapse-btn {
    z-index: 200;
    position: absolute;
    top: calc(50% - 80px);
    left: -10px;
    width: 10px;
    height: 60px;
    background-color: var(--color-600);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .collapse-transition {
    transition: all 0.3s ease;
  }
}
.tree-container {
  height: 100%;
  overflow-y: auto;
  border-right: 1px solid var(--neutral-300);
}

.operation-wrap {
  flex-shrink: 0;
}

.t-table-view {
  flex: 1;
  overflow: hidden;
}
</style>
