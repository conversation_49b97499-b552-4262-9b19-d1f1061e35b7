<template>
  <div class="resource-table" style="padding: 0;">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索测试报告名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      type="list"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="columnsObj[item].colMinWidth || colMinWidth"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'sort'">
            {{ scope.$index + 1 }}
          </span>
          <span v-else-if="item === 'title'">
            <a href="javascript:;" @click="linkEvent('testingCase_detail', scope.row, { id: scope.row.id, view: 'overview' })">
              {{ scope.row[item] || '-' }}
            </a>
          </span>
          <span v-else-if="item === 'size'">
            <span v-if="scope.row.size">{{ formatSize(scope.row[item]) }}</span>
            <div v-else>
              <el-progress :percentage="percentage" :color="'var(--color-600)'" :format="format"/>
            </div>
          </span>
          <span v-else-if="item === 'auditStatus'">
            <el-badge :type="module.levelObj[scope.row[item]] ? module.levelObj[scope.row[item]].type : 'info'" is-dot />
            <span style="margin-right: 5px;">{{ (module.levelObj[scope.row[item]] && module.levelObj[scope.row[item]].label) || '-' }}</span>
            <el-link v-permission="'manage.testing.project.projectDetail.testingReport.testingReportShowAudit'" v-if="scope.row[item] != 0" :disabled="false" :underline="false" type="primary" @click.stop="processDetail('process', scope.row)">查看</el-link>
          </span>
          <div v-else-if="item === 'operate'">
            <el-link v-permission="'manage.testing.project.projectDetail.testingReport.testingReport'" :underline="false" type="primary" @click.stop="handlePreview(scope.row)">查看</el-link>
            <el-link v-permission="'manage.testing.project.projectDetail.testingReport.testingReportUpdate'" :disabled="data.pendingStatus == 9" :underline="false" type="primary" @click.stop="processDetail('updateReport', scope.row)">更新</el-link>
            <el-link v-permission="'manage.testing.project.projectDetail.testingReport.testingReportDownload'" :underline="false" type="primary" @click.stop="handleDown(scope.row)">下载</el-link>
            <el-link v-permission="'manage.testing.project.projectDetail.testingReport.testingReportDelete'" :disabled="data.pendingStatus == 9" :underline="false" type="primary" @click.stop="handleDelete(scope.row)">删除</el-link>
            <el-link v-permission="'manage.testing.project.projectDetail.testingReport.testingReportAudit'" v-if="scope.row['auditStatus'] == 0" :disabled="data.pendingStatus == 9" :underline="false" type="primary" @click.stop="processDetail('auditReport', scope.row)">审核</el-link>
          </div>
          <span v-else>
            {{ scope.row[item] || '-' }}
          </span>
        </template>
      </el-table-column>
    </t-table-view>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      destroy-on-close
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          v-if="modalShow"
          :is="modalName"
          :name="modalName"
          :content="modalContent"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { testingReportProQuery, testingReportDeleteById, testingReportDownloadById, testingReportById } from '@/api/testing/index'
import process from '../action/modal-process.vue'
import updateReport from '../action/modal-uploadReport.vue'
import auditReport from '../action/modal-auditReport.vue'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    process,
    updateReport,
    auditReport
  },
  mixins: [mixinsPageTable],
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      module,
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'name', label: '测试报告名称', master: true },
        { key: 'round', label: '测试轮次' },
        { key: 'createUser', label: '提交人' },
        { key: 'statusList', label: '审核状态', type: 'select', valueList: module.levelStrArr },
        { key: 'time', label: '提交时间', type: 'time_range' }
      ],
      // 所有可配置显示列
      columnsObj: {
        'sort': {
          title: '序号',
          master: true,
          colMinWidth: 50
        },
        'round': {
          title: '测试轮次',
          master: true,
          colMinWidth: 60
        },
        'name': {
          title: '测试报告名称',
          colMinWidth: 250
        },
        'size': {
          title: '文件大小',
          colMinWidth: 120
        },
        'createUser': {
          title: '提交人',
          colMinWidth: 100
        },
        'createTime': {
          title: '提交时间'
        },
        'auditStatus': {
          title: '审核状态'
        },
        'operate': {
          title: '操作'
        }
      },
      // 当前显示列
      columnsViewArr: [
        'sort',
        'round',
        'name',
        'size',
        'createUser',
        'createTime',
        'auditStatus',
        'operate'
      ],
      modalDataObj: {},
      modalName: '',
      modalShow: false,
      modalContent: '',
      modalWidth: '520px',
      // 弹窗title映射
      titleMapping: {
        'process': '查看审核',
        'updateReport': '更新报告',
        'auditReport': '审核报告'
      },
      percentage: 50,
      sizePollingTimer: null // 新增定时器变量
    }
  },
  beforeDestroy() {
    // 组件销毁时清除定时器
    if (this.sizePollingTimer) {
      clearInterval(this.sizePollingTimer)
      this.sizePollingTimer = null
    }
  },
  methods: {
    format(percentage) {
      return percentage === 50 ? '进行中' : `${percentage}%`
    },
    modalClose() {
      this.modalShow = false
    },
    processDetail(name, content) {
      this.modalName = name
      this.modalContent = content
      this.modalShow = true
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.getList()
      }
    },
    // 查看附件
    handlePreview(file) {
      testingReportById({ id: file.id }).then(res => {
        if (res.data.code == 0) {
          if (file && file.name.includes('zip')) {
            this.$message.warning('该文件不支持预览，请下载查看')
            return
          }
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(window.ADMIN_CONFIG.VIP_URL + res.data.data.fileUrl))
          window.open(this.previewUrl, '_blank')
        }
      })
    },
    // 下载附件
    handleDown(file) {
      testingReportDownloadById({ id: file.id }).then(res => {
        if (res.data.data) {
          fetch(res.data.data, {
            method: 'get',
            responseType: 'blob'
          })
            .then((response) => response.blob())
            .then((blob) => {
              const a = document.createElement('a')
              const URL = window.URL || window.webkitURL
              const href = URL.createObjectURL(blob)
              a.href = href
              a.download = file.name
              document.body.appendChild(a)
              a.click()
              document.body.removeChild(a)
              URL.revokeObjectURL(href)
            })
        }
      })
    },
    // 删除附件
    handleDelete(file) {
      testingReportDeleteById({ id: file.id }).then(res => {
        if (res.data.code == 0 && res.data.data) {
          this.getList()
          this.$message.success('删除成功')
          this.$emit('call', 'refresh')
        }
      })
    },
    getList(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params.projectId = this.$route.params.id
      if (params.time) {
        params.createTimeStart = params.time.split(',')[0]
        params.createTimeEnd = params.time.split(',')[1]
      }
      testingReportProQuery(params).then(res => {
        if (res.data.code == 0) {
          this.tableData = res.data.data ? res.data.data.records : []
          this.tableTotal = Number(res.data.data.total) || 0
          this.tableLoading = false

          // 轮询逻辑,十秒调用一次列表接口,直到列表zise都存在则停止轮询
          const hasNullSize = this.tableData.some(item => item.size === null || item.size === undefined)
          if (hasNullSize) {
            if (!this.sizePollingTimer) {
              this.sizePollingTimer = setInterval(() => {
                this.getList(false)
              }, 10000)
            }
          } else {
            if (this.sizePollingTimer) {
              clearInterval(this.sizePollingTimer)
              this.sizePollingTimer = null
            }
          }
        }
      }).catch(() => {
        this.tableLoading = false
      })
    },
    formatSize(size) {
      if (!size && size !== 0) return '-'
      const byte = Number(size)
      if (byte >= 1024 * 1024) {
        return (byte / 1024 / 1024).toFixed(2) + ' MB'
      }
      return (byte / 1024).toFixed(2) + ' KB'
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-progress__text {
  color: #333;
  font-size: 13px !important;
}
</style>
