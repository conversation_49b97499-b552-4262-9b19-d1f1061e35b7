<template>
  <div class="dialog-wrap">
    <el-form ref="form" :model="form" label-width="100px">
      <el-form-item label="审核人">
        <span>{{ form.auditUserName || '-' }}</span>
      </el-form-item>
      <el-form-item label="审核时间">
        <span>{{ form.auditTime || '-' }}</span>
      </el-form-item>
      <el-form-item label="审核结果">
        <el-badge :type="module.levelObj[form.auditStatus] ? module.levelObj[form.auditStatus].type : 'info'" is-dot />
        <span style="margin-right: 5px;">{{ (module.levelObj[form.auditStatus] && module.levelObj[form.auditStatus].label) || '-' }}</span>
      </el-form-item>
      <el-form-item label="审核意见">
        <span>{{ form.auditOpinion || '-' }}</span>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import module from '../config.js'
import { testingReportShowAuditById } from '@/api/testing/index'
export default {
  name: 'Process',
  components: {},
  mixins: [],
  props: {
    content: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    }
  },
  data() {
    return {
      module,
      moduleName: module.name,
      form: {}
    }
  },
  computed: {},
  mounted() {
    this.getAuditMsg()
  },
  methods: {
    getAuditMsg() {
      const data = {
        id: this.content.id
      }
      testingReportShowAuditById(data)
        .then((res) => {
          if (res.data.code == 0) {
            this.form = res.data.data
          }
        }).catch(() => {
        })
    }
  }
}
</script>
