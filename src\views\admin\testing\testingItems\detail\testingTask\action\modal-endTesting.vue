<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        <p>任务测试报告审核通过后再进行此操作。</p>
      </div>
    </el-alert>
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="测试任务">
        <span>{{ data[0].name || '-' }}</span>
      </el-form-item>
      <el-form-item label="测试结果" prop="pass">
        <el-radio-group v-model="form.pass" size="small">
          <el-radio-button label="1">测试通过</el-radio-button>
          <el-radio-button label="0">测试不通过</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.pass == 0 && projectData.envType !== 1" label="复测环境">
        <el-checkbox
          v-model="form.redeploy"
          :true-label="1"
          :false-label="0"
        >需要重新部署</el-checkbox>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="disabled" type="primary" @click="confirm('form')">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import validate from '@/packages/validate'
import { testingTaskOver, getEnvType } from '@/api/testing/index'
export default {
  name: 'EndTesting',
  components: {},
  mixins: [],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: false,
      disabled: false,
      validate: validate,
      form: {
        pass: 1,
        redeploy: 0
      },
      rules: {
        pass: [validate.required(['blur', 'change'])]
      },
      projectData: {}
    }
  },
  computed: {},
  mounted() {
    this.getProjectEnvType()
  },
  methods: {
    getProjectEnvType() {
      getEnvType(this.$route.params.id)
        .then((res) => {
          this.projectData = res.data.data
        })
    },
    close() {
      this.$emit('close')
    },
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.loading = true
          this.disabled = true
          const data = {
            id: this.data[0].id,
            pass: this.form.pass,
            redeploy: this.form.redeploy
          }
          if (this.form.pass == 1) {
            data.redeploy = 0
          }
          testingTaskOver(data)
            .then((res) => {
              this.$message.success('操作成功')
              this.$emit('call', 'refresh')
              this.close()
            }).catch(() => {
            }).finally(() => {
              this.loading = false
              this.disabled = false
            })
        }
      })
    }
  }
}
</script>
