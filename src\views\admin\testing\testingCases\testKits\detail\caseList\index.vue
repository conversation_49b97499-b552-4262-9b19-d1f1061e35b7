<template>
  <div class="content-wrap-layout">
    <page-table
      ref="table"
      :default-selected-arr="defaultSelectedArr"
      :filter-data="filterData"
      @on-select="tableSelect"
      @on-current="tableCurrent"
    >
      <action-menu
        slot="action"
        :module-name="moduleName"
        :select-item="selectItem"
        @call="actionHandler"
      />
    </page-table>
  </div>
</template>

<script>
import actionMenu from './action/index.vue'
import pageTable from './table/index.vue'

export default {
  name: 'CaseList',
  components: {
    pageTable,
    actionMenu
  },
  provide() {
    return {
      tableVm: this.$refs.table
    }
  },
  props: {
    kitId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      moduleName: 'caseList',
      selectItem: [],
      defaultSelectedArr: [],
      filterData: {}
    }
  },
  created() {
    // 根据测试套件 ID 加载对应的测试用例列表
    // 实际应用中可能需要调用 API 获取数据
  },
  mounted() {
    // 确保表格组件已加载
    this.$nextTick(() => {
      // 更新 provide 值，确保可以注入到导出组件中
      Object.defineProperty(this, 'tableVm', {
        get: () => this.$refs.table
      })
    })
  },
  methods: {
    // 处理表格多选
    tableSelect(selection) {
      this.selectItem = selection
    },

    // 处理表格单选
    tableCurrent(row) {
      this.selectItem = [row]
    },

    // 处理操作菜单事件
    actionHandler(type, data) {
      if (type === 'refresh') {
        this.$refs.table.refresh()
      } else if (type === 'addTestCase') {
        // 处理添加测试用例
        this.$refs.table.addTestCases(data)
      } else if (type === 'deleteTestCases') {
        // 处理删除多个测试用例
        this.$refs.table.deleteTestCases(data)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.content-wrap-layout {
  height: 100%;
  padding: 16px;
  background-color: #fff;
}
</style>
