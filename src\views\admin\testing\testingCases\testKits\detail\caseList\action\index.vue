<!--
 * @Author: FinKiin
 * @Date: 2025-04-27 16:00:20
 * @LastEditTime: 2025-04-27 19:01:32
 * @FilePath: \hrm-zsk-manage-web\src\views\admin\testing\testingCases\testKits\detail\caseList\action\index.vue
 * @Description:
 *
-->
<template>
  <div class="buttons-wrap">
    <el-button
      type="primary" icon="el-icon-plus" @click="handleAdd"
    >添加用例</el-button
    >
    <el-dropdown trigger="click" placement="bottom" @command="clickDrop">
      <el-button type="primary">
        操作
        <i class="el-icon-arrow-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          :disabled="multipleDisabled" command="deleteTestCase"
        >移除</el-dropdown-item
        >
        <el-dropdown-item
          command="export"
        >导出</el-dropdown-item
        >
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 侧拉弹窗 start -->
    <el-drawer
      :visible.sync="drawerShow"
      :title="titleMapping[drawerName]"
      :size="'56.25%'"
      append-to-body
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          :data="selectItem"
          :is-single="false"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>
    <!-- 侧拉弹窗 end -->

    <!-- 弹窗 -->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
  </div>
</template>

<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import testCaseSelector from '../components/test-case-selector.vue'
import deleteTestCase from './modal-delete.vue'
import exportItem from './modal-exportItem.vue'

export default {
  name: 'ActionMenu',
  components: {
    testCaseSelector,
    exportItem,
    deleteTestCase
  },
  mixins: [mixinsActionMenu],
  props: {
    moduleName: {
      type: String,
      default: ''
    },
    selectItem: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      drawerAction: ['testCaseSelector'], // 需要侧拉打开的操作
      // 弹窗title映射
      titleMapping: {
        deleteTestCase: '移除',
        exportItem: '导出',
        testCaseSelector: '选择测试用例'
      },
      modalAction: ['exportItem', 'deleteTestCase'] // 需要弹窗打开的操作
    }
  },
  computed: {
    // 单选操作的禁用条件
    singleDisabled() {
      return !this.selectItem.length || this.selectItem.length > 1
    },
    // 多选操作的禁用条件
    multipleDisabled() {
      return !this.selectItem.length
    }
  },
  methods: {
    handleAdd() {
      this.drawerName = 'testCaseSelector'
      this.drawerShow = true
    },

    // 下拉菜单点击
    clickDrop(command) {
      if (command === 'deleteTestCase') {
        if (this.multipleDisabled) return
        this.modalName = 'deleteTestCase'
        this.modalShow = true
      } else if (command === 'export') {
        this.modalName = 'exportItem'
        this.modalShow = true
      }
    },

    confirmCall(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      } else if (type === 'deleteTestCases') {
        // 处理删除测试用例
        this.$emit('call', type, data)
      }
    },

    drawerConfirmCall(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      } else if (type === 'testCase') {
        // 处理选中的测试用例
        this.$emit('call', 'addTestCase', data)
        this.drawerClose()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.buttons-wrap {
  display: inline-block;
}
</style>
