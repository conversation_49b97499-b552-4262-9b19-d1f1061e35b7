<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      class="form-infoDrawer"
      label-width="100px"
    >
      <el-form-item label="审核" prop="examine">
        <el-radio-group v-model="form.examine" size="small" @change="changeExamineStatus">
          <el-radio-button :label="1">通过</el-radio-button>
          <el-radio-button :label="0">驳回</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审核意见" prop="remark">
        <el-input
          v-model.trim="form.remark"
          :autosize="false"
          :rows="3"
          type="textarea"
          style="width: 100%"
          maxlength="255"
          placeholder="请输入"
        />
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="disabled" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import validate from '@/packages/validate/index'
import { auditRetestApplyAPI } from '@/api/testing/index'

export default {
  props: {
    // 传入数据
    data: {
      type: [Array, Object],
      default: () => {
        return []
      }
    },
    name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      form: {
        examine: 1, // 0.驳回 1.通过
        remark: ''
      },
      rules: {
        examine: [validate.required()],
        remark: [validate.required()]
      },
      disabled: false
    }
  },
  methods: {
    changeExamineStatus(val) {
      this.form.examine = val
    },
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true
          this.disabled = true
          const params = {
            auditStatus: this.form.examine === 1 ? 1 : 2, // 1-通过，2-拒绝
            auditComment: this.form.remark
          }

          auditRetestApplyAPI(this.data.id, params)
            .then(res => {
              if (res.data && res.data.code === 0) {
                this.$message.success('审核成功')
                this.$emit('call', 'refresh')
                this.close()
              }
            })
            .catch(() => {
            })
            .finally(() => {
              this.loading = false
              this.disabled = false
            })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.drawer-wrap {
  .el-input,
  .el-select,
  .el-data-picker {
    width: 100%;
  }
}
</style>
