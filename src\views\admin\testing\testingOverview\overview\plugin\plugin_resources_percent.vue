<template>
  <!-- 资源状态统计 待办 进行中...-->
  <div class="plugin-view">
    <h3 class="plugin-title">{{ pluginTitle }}</h3>
    <div v-loading="true" v-if="loading" class="plugin-loading" />
    <el-empty v-else-if="!apiData" :image="noDataImg" :image-size="120" style="padding: 0;height: 100%;" description="暂无数据" />
    <div v-else class="resources-percent-content">
      <!-- 旧布局 -->
      <template v-if="oldStyle && !['my_todo'].includes(pluginApiType)">
        <div class="left">
          <!-- 图标 -->
          <span class="icon-view">
            <svg
              v-if="['drill_type'].includes(pluginApiType)"
              t="1711523650911"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="7779"
              width="200"
              height="200"
            >
              <path
                d="M691.3 184.8L805.2 70.9c7.5-9.3 20.5-9.3 29.9 0 1.9 1.9 3.7 5.6 5.6 9.3l22.4 80.3 80.3 20.5c3.7 1.9 5.6 1.9 9.3 5.6 7.5 9.3 7.5 22.4 0 29.9L838.8 330.4c-5.6 5.6-13.1 7.5-20.5 5.6L753 319.2l-33.6 33.6c35.5 46.7 57.9 104.5 57.9 168 0 74.7-29.9 143.7-80.3 194.1h-2c-50.4 48.5-117.6 80.3-192.3 80.3-76.5 0-143.7-29.9-194.1-80.3-50.4-50.4-80.3-117.6-80.3-194.1 0-74.7 29.9-143.7 80.3-194.1 50.4-50.4 117.6-80.3 194.1-80.3 63.5 0 121.3 22.4 168 57.9l33.6-33.6-16.8-65.3c-3.7-7.5-1.8-16.8 3.8-20.6z m166.1 225.9c-5.6-18.7 5.6-37.3 22.4-42.9 18.7-5.6 37.3 5.6 42.9 22.4 5.6 20.5 11.2 41.1 14.9 63.5 3.7 20.5 5.6 42.9 5.6 65.3 0 121.3-48.5 231.5-128.8 311.8-80.3 78.4-190.4 128.8-311.8 128.8-121.3 0-231.5-50.4-311.8-128.8C112.6 750.5 62.2 640.3 62.2 519S112.6 287.5 191 207.2C271.2 127 381.4 78.4 502.7 78.4c22.4 0 42.9 1.9 65.3 5.6 22.4 3.7 42.9 7.5 63.5 13.1 18.7 5.6 28 26.1 22.4 44.8-5.6 18.7-24.3 28-42.9 22.4-16.8-5.6-35.5-9.3-52.3-11.2-16.8-1.9-35.5-3.7-54.1-3.7-102.7 0-196 41.1-263.2 108.3-67.2 67.1-108.3 160.5-108.3 261.3 0 102.7 41.1 194.1 108.3 261.4 67.2 67.2 160.5 108.3 263.2 108.3 102.7 0 194.1-41.1 261.4-108.3 67.2-67.2 108.3-160.5 108.3-261.4 0-18.7-1.9-37.3-3.7-54.1-3.8-18.7-7.6-37.4-13.2-54.2zM502.7 392c22.4 0 42.9 5.6 61.6 16.8l76.5-76.5c-37.3-28-85.9-44.8-136.3-44.8-63.5 0-121.3 26.1-164.3 69.1-41.1 41.1-67.2 98.9-67.2 164.3 0 63.5 26.1 123.2 67.2 164.3 42.9 42.9 98.9 67.2 164.3 67.2 63.5 0 121.3-26.1 162.4-67.2l1.9-1.9C709.9 642.2 736 582.5 736 519c0-52.3-16.8-98.9-44.8-138.1l-76.5 76.5c9.3 18.7 14.9 39.2 14.9 61.6 0 35.5-14.9 65.3-37.3 89.6-22.4 22.4-54.1 37.3-89.6 37.3-35.5 0-65.3-14.9-89.6-37.3-22.4-22.4-37.3-54.1-37.3-89.6s13.1-65.3 37.3-89.6c24.3-22.4 56-37.4 89.6-37.4z m29.9 48.6c-9.3-3.7-18.7-5.6-29.9-5.6-24.3 0-44.8 9.3-59.7 24.3-14.9 14.9-24.3 35.5-24.3 59.7 0 24.3 9.3 44.8 24.3 59.7 14.9 14.9 35.5 24.3 59.7 24.3 22.4 0 44.8-9.3 59.7-24.3 14.9-14.9 24.3-35.5 24.3-59.7 0-11.2-1.9-20.5-5.6-29.9L527 543.3c-13.1 13.1-35.5 13.1-48.5 0-13.1-13.1-13.1-35.5 0-48.5l54.1-54.2z m276.3-315.5l-80.3 80.3 13.1 44.8 80.3-80.3-13.1-44.8z m42.9 74.7L771.6 280l44.8 13.1 80.3-80.3-44.9-13z"
                fill="#515151"
                p-id="7780"
              />
            </svg>
            <svg
              v-if="['node_size'].includes(pluginApiType)"
              t="1711524623900"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="11410"
              width="200"
              height="200"
            >
              <path
                d="M322.56 686.08c-30.72-30.72-51.2-76.8-51.2-122.88V384c0-35.84 10.24-66.56 25.6-92.16-10.24 0-15.36-5.12-25.6-5.12H143.36C87.04 286.72 35.84 332.8 35.84 394.24V563.2c0 40.96 20.48 76.8 56.32 92.16v327.68h87.04l30.72-209.92 30.72 209.92h76.8l5.12-296.96z m312.32-20.48c35.84-20.48 61.44-56.32 61.44-102.4V384c0-61.44-51.2-117.76-112.64-117.76H440.32c-61.44 0-112.64 51.2-112.64 117.76V563.2c0 46.08 25.6 81.92 61.44 102.4v358.4h92.16l35.84-225.28 35.84 225.28h87.04c-5.12 0-5.12-358.4-5.12-358.4zM517.12 0c66.56 0 122.88 56.32 122.88 122.88 0 66.56-56.32 122.88-122.88 122.88S394.24 189.44 394.24 122.88C394.24 56.32 450.56 0 517.12 0z m184.32 686.08v296.96h76.8l30.72-209.92 30.72 209.92h87.04v-327.68c35.84-15.36 56.32-51.2 56.32-92.16V394.24c0-56.32-46.08-107.52-107.52-107.52h-128c-10.24 0-15.36 0-25.6 5.12 20.48 25.6 25.6 61.44 25.6 92.16V563.2c5.12 46.08-15.36 92.16-46.08 122.88zM814.08 40.96c-61.44 0-112.64 51.2-112.64 112.64s51.2 112.64 112.64 112.64C870.4 266.24 921.6 215.04 921.6 153.6S870.4 40.96 814.08 40.96zM215.04 40.96c61.44 0 112.64 51.2 112.64 112.64s-51.2 112.64-112.64 112.64C153.6 266.24 102.4 215.04 102.4 153.6 102.4 92.16 153.6 40.96 215.04 40.96z"
                p-id="11411"
                fill="#515151"
              />
            </svg>
            <svg
              v-if="['team_drill_form'].includes(pluginApiType)"
              t="1711524827310"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="21599"
              width="200"
              height="200"
            >
              <path
                d="M384 96H192c-53 0-96 43-96 96v192c0 53 43 96 96 96h192c53 0 96-43 96-96V192c0-53-43-96-96-96zM384 544H192c-53 0-96 43-96 96v192c0 53 43 96 96 96h192c53 0 96-43.2 96-96.2v-192c0-52.8-43-95.8-96-95.8zM831.8 672h-192c-53 0-96 43-96 96v64c0 53 43 96 96 96H832c52.8 0 96-43.2 95.8-96v-64c0-53-43-96-96-96zM831.8 96h-192c-53 0-96 43-96 96v320c0 53 43 96 96 96H832c52.8 0 96-43 95.8-96V192c0-53-43-96-96-96z"
                p-id="21600"
                fill="#515151"
              />
            </svg>
            <svg
              v-if="['single_drill_status', 'single_drill_level'].includes(pluginApiType)"
              t="1711523064406"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="4467"
              width="200"
              height="200"
            >
              <path
                d="M718.421333 593.664c81.28 31.146667 134.912 88.661333 134.912 175.658667v90.581333c0 43.52-35.285333 78.762667-78.762666 78.762667H249.429333A78.762667 78.762667 0 0 1 170.666667 859.904V769.28c0-87.04 53.632-144.512 134.912-175.658667a37.077333 37.077333 0 0 1 39.850666 8.746667c4.906667 5.034667 9.130667 9.088 12.714667 12.16A235.392 235.392 0 0 0 512 671.488a235.434667 235.434667 0 0 0 157.781333-60.373333c2.56-2.304 5.546667-5.205333 8.96-8.704a36.906667 36.906667 0 0 1 39.68-8.746667zM512 128a196.906667 196.906667 0 0 1 196.906667 196.906667V415.573333a196.906667 196.906667 0 0 1-393.813334 0V324.906667A196.906667 196.906667 0 0 1 512 128z"
                fill="#515151"
                p-id="4468"
              />
            </svg>
            <svg
              v-if="['team_drill_status', 'team_drill_level'].includes(pluginApiType)"
              t="1711523366227"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="5793"
              width="200"
              height="200"
            >
              <path
                d="M774.4 742.4c0-44.8-12.8-89.6-38.4-121.6C691.2 569.6 640 544 576 544c-38.4 0-76.8 0-115.2 0-44.8 0-83.2 12.8-121.6 38.4-44.8 32-70.4 76.8-76.8 128-6.4 57.6 0 121.6-6.4 185.6 6.4 0 19.2 6.4 25.6 6.4 76.8 19.2 147.2 38.4 224 38.4 44.8 0 96 0 140.8-6.4 38.4-6.4 76.8-19.2 115.2-32 6.4 0 6.4-6.4 6.4-12.8C774.4 838.4 774.4 787.2 774.4 742.4z"
                p-id="5794"
                fill="#515151"
              />
              <path
                d="M358.4 531.2c12.8-6.4 19.2-6.4 32-12.8-38.4-38.4-57.6-76.8-57.6-128-44.8 0-89.6 0-134.4 0C172.8 390.4 147.2 396.8 128 403.2 57.6 428.8 0 499.2 0 576c0 51.2 0 102.4 0 160 0 6.4 0 6.4 6.4 12.8 51.2 12.8 102.4 25.6 153.6 32 19.2 0 44.8 6.4 64 6.4 0-12.8 0-25.6 0-38.4 0-19.2 0-38.4 6.4-57.6C249.6 614.4 294.4 563.2 358.4 531.2z"
                p-id="5795"
                fill="#515151"
              />
              <path
                d="M1024 588.8c0-25.6-6.4-57.6-19.2-83.2-38.4-76.8-96-115.2-179.2-115.2-121.6 0 0 0-121.6 0 0 51.2-19.2 96-57.6 128 6.4 0 12.8 6.4 12.8 6.4 32 12.8 64 32 89.6 57.6 38.4 44.8 57.6 96 57.6 153.6 0 12.8 0 32 0 44.8 32-6.4 57.6-6.4 89.6-12.8 38.4-6.4 83.2-19.2 121.6-38.4 6.4 0 6.4-6.4 6.4-12.8C1024 684.8 1024 633.6 1024 588.8z"
                p-id="5796"
                fill="#515151"
              />
              <path
                d="M518.4 537.6c83.2 0 153.6-70.4 147.2-153.6 0-83.2-70.4-147.2-147.2-147.2-83.2 0-153.6 64-153.6 147.2C371.2 467.2 435.2 537.6 518.4 537.6z"
                p-id="5797"
                fill="#515151"
              />
              <path
                d="M704 371.2C723.2 377.6 742.4 384 768 384c83.2 0 153.6-70.4 147.2-153.6 0-83.2-70.4-147.2-147.2-147.2-83.2 0-153.6 64-153.6 147.2C665.6 256 697.6 307.2 704 371.2z"
                p-id="5798"
                fill="#515151"
              />
              <path
                d="M256 384c25.6 0 57.6-6.4 76.8-19.2 6.4-51.2 32-96 70.4-121.6 0 0 0-6.4 0-6.4 0-83.2-70.4-147.2-147.2-147.2-83.2 0-153.6 64-153.6 147.2C102.4 313.6 172.8 384 256 384z"
                p-id="5799"
                fill="#515151"
              />
            </svg>
          </span>
          <!-- 总数量 -->
          <span class="label">
            <div class="count">
              <span
                v-if="
                  [
                    'drill_type',
                    'node_size',
                    'team_drill_form',
                    'single_drill_status',
                    'team_drill_status',
                  ].includes(pluginApiType)
                "
              >{{ apiData[resourcesConfig.total.key] }}</span
              >
            </div>
            <div class="name">
              {{ resourcesConfig.total.text || "总数量" }}
            </div>
          </span>
        </div>
        <!-- 项目类型 -->
        <div
          v-if="
            [
              'drill_type',
              'node_size',
              'team_drill_form',
              'single_drill_status',
              'team_drill_status',
            ].includes(pluginApiType)
          "
          class="right"
        >
          <div
            v-for="item of resourcesConfig.options"
            :key="item.label"
            class="state-row"
          >
            <el-badge :type="item.type" is-dot />{{ item.label }}
            <span class="count">
              <span>{{ apiData[item.countKey] }}</span>
            </span>
          </div>
        </div>
      </template>

      <!-- 我的待办新布局 -->
      <div class="my-todo-wrap">
        <!-- 测试主管 -->
        <!-- 检测申请区域 -->
        <div v-if="currentRole === '181252'" class="todo-section">
          <div class="section-title">检测申请</div>
          <div class="todo-item">
            <div class="todo-icon blue-font">
              <i class="cr-icon-wddbdqr"/>
            </div>
            <div class="todo-content">
              <div class="todo-label">待确认</div>
              <div class="todo-count blue-font">{{ apiData.detectionApplicationSum||0 }}</div>
            </div>
            <div class="todo-action">
              <el-button :disabled="apiData.detectionApplicationSum === 0" type="text" class="view-btn" @click="handleView('unconfirmedApplication')">查看</el-button>
            </div>
          </div>
        </div>

        <!-- 待关联检测项目 -->
        <div v-if="currentRole === '181252'" class="todo-section">
          <div class="todo-item">
            <div class="todo-icon green-font">
              <i class="cr-icon-wddbdgx"/>
            </div>
            <div class="todo-content">
              <div class="todo-label">待关联检测项目</div>
              <div class="todo-count green-font">{{ apiData.projectSum }}</div>
            </div>
            <div class="todo-action">
              <el-button :disabled="apiData.projectSum === 0" type="text" class="view-btn" @click="handleView('unrelatedApplication')">查看</el-button>
            </div>
          </div>
        </div>

        <!-- 检测项目区域 -->
        <div v-if="currentRole === '181252' || currentRole === '181251'" class="todo-section">
          <div class="section-title">检测项目</div>
          <div class="todo-item">
            <div class="todo-icon orange-font">
              <i class="cr-icon-wddbdsh"/>
            </div>
            <div class="todo-content">
              <div class="todo-label">待审核测试报告</div>
              <div class="todo-count orange-font">{{ apiData.testReportSum }}</div>
            </div>
            <div class="todo-action">
              <el-button :disabled="!apiData.testReportSum" type="text" class="view-btn" @click="handleView('projectPendingAuditReportPage')">查看</el-button>
            </div>
          </div>
        </div>

        <div v-if="currentRole === '181252' || currentRole === '181251'" class="todo-section">
          <div class="todo-item">
            <div class="todo-icon orange-font">
              <i class="el-icon-document"/>
            </div>
            <div class="todo-content">
              <div class="todo-label">待审核复测申请</div>
              <div class="todo-count orange-font">{{ apiData.retestApplySum }}</div>
            </div>
            <div class="todo-action">
              <el-button :disabled="!apiData.retestApplySum" type="text" class="view-btn" @click="handleView('projectPendingAuditReportPageFuce')">查看</el-button>
            </div>
          </div>
        </div>
        <div v-if="currentRole === '181252' || currentRole === '181251'" class="todo-section">
          <div class="todo-item">
            <div class="todo-icon orange-font">
              <i class="cr-icon-wddbyanqi"/>
            </div>
            <div class="todo-content">
              <div class="todo-label">待审核延期申请</div>
              <div class="todo-count orange-font">{{ apiData.delayApplySum }}</div>
            </div>
            <div class="todo-action">
              <el-button :disabled="!apiData.delayApplySum" type="text" class="view-btn" @click="handleView('projectPendingAuditReportPageYanqi')">查看</el-button>
            </div>
          </div>
        </div>
        <div v-if="currentRole === '181252' || currentRole === '181251'" class="todo-section">
          <div class="todo-item">
            <div class="todo-icon orange-font">
              <i class="cr-icon-wddbsongshen"/>
            </div>
            <div class="todo-content">
              <div class="todo-label">待审核资源申请</div>
              <div class="todo-count orange-font">{{ apiData.applySum }}</div>
            </div>
            <div class="todo-action">
              <el-button :disabled="!apiData.applySum" type="text" class="view-btn" @click="handleView('projectPendingAuditReportPageZIyuanshenqing')">查看</el-button>
            </div>
          </div>
        </div>

        <!-- 检测人员检测项目 -->
        <div v-if="currentRole === '181253'" class="todo-section">
          <div class="section-title">检测项目</div>
          <div class="todo-item mb-10">
            <div class="todo-icon">
              <i class="cr-icon-wddbdks blue-font"/>
            </div>
            <div class="todo-content">
              <div class="todo-label">待开始任务</div>
              <div class="todo-count blue-font">{{ apiData.toStartTaskSum }}</div>
            </div>
            <div class="todo-action">
              <el-button :disabled="apiData.toStartTaskSum === 0" type="text" class="view-btn" @click="handleView('toStartTaskSum')">查看</el-button>
            </div>
          </div>
          <div class="todo-item mb-10">
            <div class="todo-icon">
              <i class="cr-icon-wddbjxz green-font"/>
            </div>
            <div class="todo-content">
              <div class="todo-label">进行中任务</div>
              <div class="todo-count green-font">{{ apiData.inProgressTaskSum }}</div>
            </div>
            <div class="todo-action">
              <el-button :disabled="apiData.inProgressTaskSum === 0" type="text" class="view-btn" @click="handleView('inProgressTaskSum')">查看</el-button>
            </div>
          </div>
          <div class="todo-item">
            <div class="todo-icon">
              <i class="cr-icon-wddbdgx orange-font"/>
            </div>
            <div class="todo-content">
              <div class="todo-label">待更新问题</div>
              <div class="todo-count orange-font">{{ apiData.toUpdateProblemSum }}</div>
            </div>
            <div class="todo-action">
              <el-button :disabled="apiData.toUpdateProblemSum === 0" type="text" class="view-btn" @click="handleView('toUpdateProblemSum')">查看</el-button>
            </div>
          </div>
        </div>

        <!-- 厂商人员待办 -->
        <template v-if="currentRole === '181254'">
          <div class="todo-section">
            <div class="section-title">检测申请</div>
            <div class="todo-item">
              <div class="todo-icon">
                <i class="cr-icon-wddbdqr blue-font"/>
              </div>
              <div class="todo-content">
                <div class="todo-label">被退回</div>
                <div class="todo-count blue-font">{{ apiData.toSubmitSum||0 }}</div>
              </div>
              <div class="todo-action">
                <el-button :disabled="!apiData.toSubmitSum" type="text" class="view-btn" @click="handleView('toSubmitSum')">查看</el-button>
              </div>
            </div>
          </div>
          <div class="todo-section">
            <div class="section-title">检测项目</div>
            <div class="todo-item mb-10">
              <div class="todo-icon">
                <i class="cr-icon-wddbdsh blue-font"/>
              </div>
              <div class="todo-content">
                <div class="todo-label">待送审项目资料</div>
                <div class="todo-count blue-font">{{ apiData.toAuditSum||0 }}</div>
              </div>
              <div class="todo-action">
                <el-button :disabled="!apiData.toAuditSum" type="text" class="view-btn" @click="handleView('toAuditSum')">查看</el-button>
              </div>
            </div>
            <div class="todo-item mb-10">
              <div class="todo-icon">
                <i class="cr-icon-wddbdcj orange-font"/>
              </div>
              <div class="todo-content">
                <div class="todo-label">待部署环境</div>
                <div class="todo-count orange-font">{{ apiData.toDeploySum||0 }}</div>
              </div>
              <div class="todo-action">
                <el-button :disabled="!apiData.toDeploySum" type="text" class="view-btn" @click="handleView('toDeploySum')">查看</el-button>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <!-- 侧拉弹窗 start -->
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      append-to-body
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          :process-id="processId"
          :current-role="currentRole"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>
    <!-- 侧拉弹窗 end -->
  </div>
</template>
<style lang="scss">
.blue-font {
  color: #409EFF;
}

.green-font {
  color: #67C23A;
}

.orange-font {
  color: #E6A23C;
}
.resources-percent-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;

  /* 我的代办新样式 */
  .my-todo-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .todo-section {
      margin-bottom: 10px;

      .section-title {
        font-size: 14px;
        color: #000;
        margin-bottom: 13px;
      }

      .todo-item {
        display: flex;
        align-items: center;
        padding: 15px;
        background-color: #f5f7fa;
        border-radius: 8px;
        height: 80px;

        .todo-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          background-color: #E2ECFD;

          i {
            font-size: 18px;
          }

          &.blue {
            background-color: #409EFF;
          }

          &.green {
            background-color: #67C23A;
          }

          &.orange {
            background-color: #E6A23C;
          }
        }

        .todo-content {
          flex: 1;
          padding-top: 3px;

          .todo-label {
            font-size: 14px;
            color: #606266;
            margin-bottom: 5px;
          }

          .todo-count {
            font-size: 16px;
            font-weight: bold;
          }
        }

        .todo-action {
          align-self: center;
          .view-btn {
            font-size: 14px;
            color: var(--color-600);
            border: none;
          }
        }
      }
    }
  }

  .todo-list {
    width: 100%;
    height: 100%;
  }
  .icon-view {
    display: inline-block;
    background: #f5f7fa;
    position: relative;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    color: #707275;
    font-size: 20px;
    font-weight: 800;
    .icon {
      width: 36px;
      height: 36px;
      top: 17px;
      position: absolute;
      left: 16px;
    }
  }
  .label {
    display: inline-block;
    margin-left: 10px;
    vertical-align: top;
    line-height: 30px;
    padding-top: 5px;
    .count {
      color: #181a1d;
      font-size: 24px;
      font-weight: 600;
      a {
        color: #181a1d;
        &:hover {
          color: var(--color-600);
        }
      }
    }
    .name {
      font-size: 14px;
      color: #707275;
    }
  }
  .left {
    width: 50%;
    padding-top: 15px;
    text-align: center;
  }
  .right {
    width: 50%;
    margin-left: 30px;
  }
  .legend-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    position: relative;
    right: 10px;
  }
}
</style>
<script>
import { mapGetters } from 'vuex'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import pluginMixin from './mixin_plugin.js'
import moment from 'moment'
import { managerTodo, testerTodo, vendorTodo, projectManagerTodo } from '@/api/testing/testingOverview'
import unconfirmedApplication from '../components/unconfirmedApplication.vue'
import unrelatedApplication from '../components/unrelatedApplication.vue'
import projectPendingAuditReportPage from '../components/projectPendingAuditReportPage.vue'
import projectPendingAuditReportPageFuce from '../components/projectPendingAuditReportPageFuce.vue'
import projectPendingAuditReportPageYanqi from '../components/projectPendingAuditReportPageYanqi.vue'
import projectPendingAuditReportPageZIyuanshenqing from '../components/projectPendingAuditReportPageZIyuanshenqing.vue'
import toStartTaskSum from '../components/toStartTaskSum.vue'
import inProgressTaskSum from '../components/inProgressTaskSum.vue'
import toUpdateProblemSum from '../components/toUpdateProblemSum.vue'
import toSubmitSum from '../components/toSubmitSum.vue'
import toAuditSum from '../components/toAuditSum.vue'
import toDeploySum from '../components/toDeploySum.vue'

export default {
  components: {
    toAuditSum,
    toDeploySum,
    toSubmitSum,
    toUpdateProblemSum,
    inProgressTaskSum,
    toStartTaskSum,
    unconfirmedApplication,
    unrelatedApplication,
    projectPendingAuditReportPage,
    projectPendingAuditReportPageFuce,
    projectPendingAuditReportPageYanqi,
    projectPendingAuditReportPageZIyuanshenqing
  },
  mixins: [pluginMixin, mixinsActionMenu],
  props: {
    processId: {
      type: [String, Number],
      default: ''
    },
    currentRole: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      noDataImg: require('@/packages/table-view/nodata.png'),
      titleMapping: {
        'toDeploySum': '检测项目',
        'toAuditSum': '检测项目',
        'toSubmitSum': '检测申请',
        'toUpdateProblemSum': '待更新问题',
        'inProgressTaskSum': '进行中任务',
        'toStartTaskSum': '待开始任务',
        'projectPendingAuditReportPage': '检测项目',
        'projectPendingAuditReportPageFuce': '检测项目',
        'projectPendingAuditReportPageYanqi': '检测项目',
        'projectPendingAuditReportPageZIyuanshenqing': '检测项目',
        'unconfirmedApplication': '检测申请',
        'unrelatedApplication': '检测申请'
      },
      oldStyle: false // 使用新样式
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  watch: {
    processId: {
      handler(newVal) {
        this.getData()
      },
      immediate: true
    },
    currentRole: {
      handler(newVal) {
        this.getData()
      },
      immediate: true
    }
  },
  methods: {
    handleView(drawerName) {
      this.drawerName = drawerName
      this.drawerShow = true
    },
    // 侧拉弹窗确认回调
    drawerConfirmCall(type, data) {
      if (type === 'caseProject' && data.length > 0) {
        this.formData.projectId = data[0].id
        this.formData.projectName = data[0].projectName
      }
      this.drawerClose()
    },
    hasItem(key) {
      return this.apiData && (this.apiData[key] >= 0)
    },
    getData: function() {
      if (this.currentRole === '181252') { // 测试主管
        const params = {
          processId: this.processId
        }
        managerTodo(params).then(res => {
          if (res.code == 0) {
            this.apiData = res.data
          }
        })
      }
      if (this.currentRole === '181253') { // 检测人员
        const params = {
          processId: this.processId
        }
        testerTodo(params).then(res => {
          if (res.code == 0) {
            this.apiData = res.data
          }
        })
      }
      if (this.currentRole === '181254') { // 厂商人员
        const params = {
          processId: this.processId
        }
        vendorTodo(params).then(res => {
          if (res.code == 0) {
            this.apiData = res.data
          }
        })
      }
      if (this.currentRole === '181251') { // 检测项目负责人
        const params = {
          processId: this.processId,
          roleId: this.currentRole
        }
        projectManagerTodo(params).then(res => {
          if (res.code == 0) {
            this.apiData = res.data
          }
        })
      }
    },
    getTotal(data) {
      let total = 0
      data.forEach(key => {
        total += this.apiData[key]
      })
      return total
    },
    getTimeParams() {
      if (
        this.data.pluginConfig.timeName == 'undefined' ||
        this.data.pluginConfig.timeName == '不限'
      ) { return }
      const now = new Date()
      // 近一天的时间
      this.dayAgoParams = new Date()
      if (this.data.pluginConfig.timeName === '近1天') {
        this.dayAgoParams.setDate(now.getDate() - 1)
      }
      // 近一周的时间
      if (this.data.pluginConfig.timeName === '近1周') {
        this.dayAgoParams.setDate(now.getDate() - 7)
      }
      // 近一个月的时间
      if (this.data.pluginConfig.timeName === '近1个月') {
        this.dayAgoParams.setMonth(now.getMonth() - 1)
      }
      return moment(this.dayAgoParams).format('YYYY-MM-DD')
    },
    getInfo() {
      const countKeyList = this.resourcesConfig.options.map(v => v.countKey)
      this.apiData.total = this.getTotal(countKeyList)
    }
  }
}
</script>
